// src/router/routes/modules/ba.ts
import type { AppRouteModule } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';

const BARoute: AppRouteModule = {
  path: '/ba',
  name: 'BA',
  component: LAYOUT,
  meta: {
    orderNo: 30,
    icon: 'carbon:chart-line-data',
    title: 'BA系统管理',
  },
  children: [
    {
      path: 'sensor',
      name: 'BASensor',
      meta: {
        title: '传感器管理',
      },
      children: [
        {
          path: 'real-time-data',
          name: 'RealTimeData',
          component: () => import('/@/views/ba/sensor/real-time-data/index.vue'),
          meta: {
            title: '实时数据管理',
          },
        },
        {
          path: 'alarm',
          name: 'BAAlarm',
          component: () => import('/@/views/ba/sensor/alarm/index.vue'),
          meta: {
            title: '告警数据管理',
          },
        },
      ],
    },
    {
      path: 'device-data',
      name: 'BADeviceData',
      component: () => import('/@/views/ba/device-data/index.vue'),
      meta: {
        title: '设备数据',
      },
    },
  ],
};

export default BARoute;
