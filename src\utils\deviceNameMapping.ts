/**
 * deviceNameMapping.ts
 * 设备名称映射和翻译功能
 *
 * 职责：
 * - 将设备编码格式转换为用户友好的显示名称
 * - 设备类型分类和识别
 * - 复用 deviceIdentifier.ts 中的楼层提取逻辑，避免重复代码
 */
import { extractFloorNumber } from '/@/views/scene/utils/deviceIdentifier';

interface DeviceTypeMap {
  [key: string]: string;
}

// 设备类型映射
const deviceTypeMap: DeviceTypeMap = {
  pdg: '配电柜',
  fwq: '服务器',
  jg: '机柜',
  kzg: '控制柜',
  plczkg: 'PLC中控柜',
  gyjlfzg: '工艺计量阀组柜',
  zldyj: '整流电源机',
  zjzkg: '主机中控柜',
};

/**
 * 解析设备名称
 * @param deviceName 原始设备名称，如 1F-fwq-jg-134 或 5F-fwq-jg-134
 */
export function parseDeviceName(deviceName: string): string {
  if (!deviceName) return '未知设备';

  // 使用统一的楼层提取函数
  const floorNumber = extractFloorNumber(deviceName);
  const floor = floorNumber ? `${floorNumber}楼` : '';

  // 分割剩余部分
  const parts = deviceName.split('-').slice(1); // 去掉楼层部分

  // 转换设备类型
  const translatedParts = parts.map((part) => {
    const type = deviceTypeMap[part.toLowerCase()];
    if (type) return type;
    // 如果是数字，保留原样
    if (/^\d+$/.test(part)) return part;
    return part;
  });

  // 特殊组合处理
  if (translatedParts.includes('服务器') && translatedParts.includes('机柜')) {
    const number = translatedParts.find((part) => /^\d+$/.test(part)) || '';
    return `${floor}服务器机柜${number}号`;
  }

  // 一般情况处理
  const type = translatedParts.filter((part) => isNaN(Number(part))).join('');
  const number = translatedParts.find((part) => /^\d+$/.test(part)) || '';

  return `${floor}${type}${number}号`;
}

/**
 * 获取设备类型
 * @param deviceName 设备名称
 */
export function getDeviceCategory(deviceName: string): string {
  if (!deviceName) return '其他设备';

  const typeKeys = Object.keys(deviceTypeMap);
  for (const key of typeKeys) {
    if (deviceName.toLowerCase().includes(key)) {
      return deviceTypeMap[key];
    }
  }

  return '其他设备';
}
