// src/router/routes/modules/device.ts
import type { AppRouteModule } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';

const DeviceRoute: AppRouteModule = {
  path: '/device',
  name: 'Device',
  component: LAYOUT,
  meta: {
    orderNo: 32,
    icon: 'ant-design:database-outlined',
    title: '设备管理',
  },
  children: [
    {
      path: 'list',
      name: 'DeviceList',
      component: () => import('/@/views/device/list/index.vue'),
      meta: {
        title: '设备列表',
      },
    },
  ],
};

export default DeviceRoute;
