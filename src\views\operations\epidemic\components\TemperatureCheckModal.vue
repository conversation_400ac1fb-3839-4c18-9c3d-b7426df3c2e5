<template>
  <a-modal v-model:visible="visible" title="体温检测" width="400px" @ok="handleSubmit" @cancel="handleCancel">
    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <a-form-item label="身份证号" name="personId">
        <a-input v-model:value="formData.personId" placeholder="请输入身份证号" />
      </a-form-item>
      <a-form-item label="体温" name="temperature">
        <a-input-number v-model:value="formData.temperature" :min="35" :max="42" :step="0.1" :precision="1" style="width: 100%">
          <template #addonAfter>°C</template>
        </a-input-number>
      </a-form-item>
      <a-form-item label="检测地点" name="checkLocation">
        <a-input v-model:value="formData.checkLocation" placeholder="请输入检测地点" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { temperatureCheck } from '/@/api/operations/epidemic';

  interface Props {
    visible: boolean;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{ 'update:visible': [value: boolean]; success: [] }>();

  const formRef = ref();
  const formData = reactive({
    personId: '',
    temperature: 36.5,
    checkLocation: '',
  });

  const rules = {
    personId: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
    temperature: [{ required: true, message: '请输入体温', trigger: 'blur' }],
    checkLocation: [{ required: true, message: '请输入检测地点', trigger: 'blur' }],
  };

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const handleSubmit = async () => {
    try {
      await formRef.value.validate();
      await temperatureCheck(formData);
      message.success('体温检测记录成功');
      emit('success');
    } catch (error) {
      message.error('记录失败');
    }
  };

  const handleCancel = () => {
    visible.value = false;
  };
</script>
