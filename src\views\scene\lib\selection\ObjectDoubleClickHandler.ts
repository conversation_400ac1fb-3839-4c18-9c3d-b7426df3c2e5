import * as THREE from 'three';
import { debounce, throttle } from 'lodash-es';
import { gsap } from 'gsap';
import { ControlManager } from '../control/ControlManager';
import { BaseObjectHandler } from './BaseObjectHandler';
import { ObjectSelection } from './ObjectSelection';
import { SceneManager } from '../SceneManager';
import { RenderingPipeline } from '../RenderingPipeline';
import { useGlobalThreeStore } from '../../store/globalThreeStore';
import { useDeviceDetailStore } from '../../pages/components/deviceDetail/deviceDetailStore';
import { isFloorDevice } from '../../utils/deviceIdentifier';

// 设备数据接口定义 - 已不再使用，因为设备数据现在通过API获取
// 保留注释以便理解代码历史

interface CameraAnimConfig {
  duration: number;
  ease: string;
  orbitSpeed: number;
  orbitEnabled: boolean;
  orbitRadius: number;
  minDistance: number;
  maxDistance: number;
  heightOffset: number;
}

export class ObjectDoubleClickHandler extends BaseObjectHandler {
  public isDeviceObserving = false;
  private boundDoubleClickHandler!: (event: MouseEvent) => void;
  private debouncedHandleDoubleClick!: ((event: MouseEvent) => void) & { cancel: () => void };
  private handleKeyDownThrottled!: ((event: KeyboardEvent) => void) & { cancel: () => void };
  private environmentObjects: Set<THREE.Object3D>;
  private cameraAnimConfig: CameraAnimConfig;
  private currentTween: gsap.core.Timeline | null;
  private selectedObject: THREE.Object3D | null;
  private deviceCenter: THREE.Vector3 = new THREE.Vector3();
  private deviceDistance: number = 0;
  private originalOrbitConfig: {
    target: THREE.Vector3;
    minDistance: number;
    maxDistance: number;
    enabled: boolean;
    minPolarAngle: number;
    maxPolarAngle: number;
  } | null = null;

  constructor(rendererDom: HTMLElement, camera: THREE.Camera) {
    super(rendererDom, camera);
    if (!rendererDom || !camera) {
      throw new Error('rendererDom and camera must be provided');
    }
    this.isDeviceObserving = false;

    if (this.config.features.enableDoubleClick) {
      this.boundDoubleClickHandler = this.handleDoubleClick.bind(this);
      this.debouncedHandleDoubleClick = debounce(this.boundDoubleClickHandler, this.config.events.doubleClickDelay);

      if (this.rendererDom && !this.rendererDom._doubleClickHandlerBound) {
        this.rendererDom.addEventListener('dblclick', this.boundDoubleClickHandler);
        this.rendererDom._doubleClickHandlerBound = true;
      }
    }

    // 使用节流包装键盘事件处理函数
    this.handleKeyDownThrottled = throttle(this.handleKeyDown.bind(this), 100);
    window.addEventListener('keydown', this.handleKeyDownThrottled);

    this.environmentObjects = new Set<THREE.Object3D>();

    this.cameraAnimConfig = {
      duration: 1.5,
      ease: 'power3.inOut',
      orbitSpeed: 0.0005,
      orbitEnabled: false,
      orbitRadius: 0.2,
      minDistance: 1,
      maxDistance: 10,
      heightOffset: 0.3,
    };

    this.currentTween = null;
    this.selectedObject = null;
  }

  public handleDoubleClick(event: MouseEvent): void {
    if (!this.config.features.enableDoubleClick) return;

    if (this.isDeviceObserving) return;

    const _controlManager = ControlManager.getInstance();
    const currentMode = _controlManager.getCurrentMode();
    if (currentMode === 'first') return;

    const objectSelection = ObjectSelection.getInstance();
    const result = objectSelection.performGpuPicking(event);

    if (result) {
      this.isDeviceObserving = true;
      this.disableHighlighting();
      this.handleObjectDoubleSelection(result);
    }
  }
  public isDeviceGroup(object: THREE.Object3D | null): boolean {
    if (!object) return false;

    // 使用统一的设备识别逻辑
    const isDevice = object.name && isFloorDevice(object.name);

    // 只有以楼层标识开头的对象才被识别为设备
    if (isDevice) {
      console.debug(`[ObjectDoubleClickHandler] 识别到设备: ${object.name}`);
      return true;
    }

    // 不再使用关键词匹配作为备用，确保只有楼层设备被识别
    if (Math.random() < 0.01) {
      // 随机记录一小部分未匹配的对象，避免日志过多
      console.debug(`[ObjectDoubleClickHandler] 未识别为设备: ${object.name}`);
    }

    return false;
  }

  public async handleObjectDoubleSelection(object: THREE.Object3D): Promise<void> {
    if (process.env.NODE_ENV !== 'production') {
      console.debug('进入设备观察模式:', object.name);
    }

    this.hideEnvironmentObjects();

    const objectSelection = ObjectSelection.getInstance();
    if (objectSelection?.objectHighlighter) {
      objectSelection.objectHighlighter.setHighlightEnabled(false);
    }

    this.selectedObject = object;

    // 保存设备模型名称到全局状态
    const globalThreeStore = useGlobalThreeStore();
    globalThreeStore.setCurrentDeviceModelName(object.name || '未命名设备');

    if (process.env.NODE_ENV !== 'production') {
      console.debug('设置观察模式状态');
    }
    this.enterObserveMode();

    const box = new THREE.Box3().setFromObject(object);
    const size = box.getSize(new THREE.Vector3());
    const maxDim = Math.max(size.x, size.y, size.z);

    this.deviceCenter = box.getCenter(new THREE.Vector3());
    this.deviceDistance = maxDim * 2;

    requestAnimationFrame(() => {
      this._setupInitialViewPosition();
      this._configureOrbitControlsForDeviceView();
    });

    // 激活设备详情面板
    await this._activateDeviceDetailPanel(object);
  }

  // 激活设备详情面板
  private async _activateDeviceDetailPanel(object: THREE.Object3D): Promise<void> {
    if (!object || !object.name) return;

    try {
      // 获取全局状态管理器
      const globalThreeStore = useGlobalThreeStore();
      const deviceDetailStore = useDeviceDetailStore();

      // 加载设备数据，并检查是否有数据返回
      const hasData = await deviceDetailStore.loadDeviceData(object.name);

      // 只有在有数据的情况下才激活设备详情面板
      if (hasData) {
        // 设置设备详情激活状态，并传递设备编码（使用设备名称作为编码）
        globalThreeStore.setDeviceDetailActive(true, object.name);
      }
    } catch (error) {
      console.error(`激活设备详情面板出错:`, error);
    }
  }
  private _setupInitialViewPosition(): void {
    if (!this.selectedObject) return;

    if (this.currentTween) {
      this.currentTween.kill();
      this.currentTween = null;
    }

    const controlManager = ControlManager.getInstance();
    const orbitControls = controlManager.getOrbitControls();

    this.originalOrbitConfig = {
      target: orbitControls.target.clone(),
      minDistance: orbitControls.minDistance,
      maxDistance: orbitControls.maxDistance,
      enabled: orbitControls.enabled,
      minPolarAngle: orbitControls.minPolarAngle,
      maxPolarAngle: orbitControls.maxPolarAngle,
    };

    const box = new THREE.Box3().setFromObject(this.selectedObject);
    const center = box.getCenter(new THREE.Vector3());
    const size = box.getSize(new THREE.Vector3());
    const maxDim = Math.max(size.x, size.y, size.z);
    const idealDistance = maxDim * 2;

    // 保持当前观察角度，只调整距离到合适的观察位置
    if (!this.camera) return;
    // 计算从设备中心到当前相机位置的方向向量
    const currentDirection = new THREE.Vector3().subVectors(this.camera.position, center).normalize();

    // 基于当前视角方向，计算新的目标位置
    const targetPosition = new THREE.Vector3().copy(center).add(currentDirection.multiplyScalar(idealDistance));

    const sceneManager = SceneManager.getInstance();

    this.currentTween = gsap.timeline();

    this.currentTween.to(this.camera.position, {
      x: targetPosition.x,
      y: targetPosition.y,
      z: targetPosition.z,
      duration: 1.0,
      ease: 'power2.inOut',
      onUpdate: () => {
        this.camera?.lookAt(center);
        orbitControls.update();
      },
      onComplete: () => {
        this.camera?.lookAt(center);
        orbitControls.target.copy(center);
        orbitControls.update();
        if (sceneManager) {
          sceneManager.forceRender();
        }
      },
    });
  }

  private _configureOrbitControlsForDeviceView(): void {
    const controlManager = ControlManager.getInstance();
    const controls = controlManager.getOrbitControls();

    controls.target.copy(this.deviceCenter);
    controls.minDistance = this.deviceDistance * 0.5;
    controls.maxDistance = this.deviceDistance * 2;
    controls.minPolarAngle = Math.PI * 0.1;
    controls.maxPolarAngle = Math.PI * 0.9;
    controls.enabled = true;
    controls.update();
  }

  public enterObserveMode(): void {
    const _controlManager = ControlManager.getInstance();
    const controls = _controlManager.getOrbitControls();

    // 始终保存相机状态，确保退出时能正确恢复
    if (!this.camera) return;

    this.config.observeMode.previousCameraState = {
      position: this.camera.position.clone(),
      quaternion: this.camera.quaternion.clone(),
      target: controls ? controls.target.clone() : new THREE.Vector3(0, 0, 0),
    };

    // 记录控制器的原始配置，便于退出时恢复
    if (controls) {
      this.originalOrbitConfig = {
        target: controls.target.clone(),
        minDistance: controls.minDistance,
        maxDistance: controls.maxDistance,
        enabled: controls.enabled,
        minPolarAngle: controls.minPolarAngle,
        maxPolarAngle: controls.maxPolarAngle,
      };
    }

    _controlManager.setControlsEnabled(false);
    this.config.observeMode.enabled = true;
  }

  public exitObserveMode(): void {
    const _controlManager = ControlManager.getInstance();
    const state = this.config.observeMode.previousCameraState;

    // 如果没有保存的状态，直接退出
    if (!state) {
      this.config.observeMode.enabled = false;
      return;
    }

    // 先关闭设备详情面板
    try {
      const globalThreeStore = useGlobalThreeStore();
      // 关闭设备详情，并清空当前设备编码
      globalThreeStore.setDeviceDetailActive(false);
    } catch (error) {
      console.error('关闭设备详情面板出错:', error);
    }

    const controls = _controlManager.getOrbitControls();
    if (controls) {
      controls.enabled = false;
    }

    // 创建一个简单的动画时间线
    const duration = 1.2;
    const ease = 'power3.inOut';
    this.currentTween = gsap.timeline();

    // 简化：直接一步同时动画位置和旋转
    if (!this.camera) return;

    this.currentTween.to(
      this.camera.position,
      {
        x: state.position.x,
        y: state.position.y,
        z: state.position.z,
        duration,
        ease,
      },
      0
    );

    // 直接设置四元数旋转，确保视角恢复准确
    if (!this.camera) return;

    const currentQuaternion = this.camera.quaternion.clone();
    this.currentTween.to(
      currentQuaternion,
      {
        x: state.quaternion.x,
        y: state.quaternion.y,
        z: state.quaternion.z,
        w: state.quaternion.w,
        duration,
        ease,
        onUpdate: () => {
          this.camera?.quaternion.copy(currentQuaternion);
        },
      },
      0
    );

    // 目标点也要同步恢复
    this.currentTween.to(
      controls.target,
      {
        x: state.target.x,
        y: state.target.y,
        z: state.target.z,
        duration,
        ease,
        onComplete: () => {
          // 重置控制器状态
          if (controls) {
            controls.enabled = true;
            controls.enableRotate = true;
            controls.enableZoom = true;
            controls.enablePan = true;
            controls.minPolarAngle = 0.1;
            controls.maxPolarAngle = Math.PI * 0.5;
            controls.minDistance = 1;
            controls.maxDistance = 1000;
            controls.update();
          }

          // 重置所有状态
          this.config.observeMode.enabled = false;
          this.config.observeMode.previousCameraState = null;
          this.originalOrbitConfig = null;
          this.currentTween = null;

          // 确保场景渲染正确
          const sceneManager = SceneManager.getInstance();
          const renderingPipeline = RenderingPipeline.getInstance();
          if (renderingPipeline) {
            renderingPipeline.render();
          }
          if (sceneManager) {
            sceneManager.needsAnimate = true;
          }
        },
      },
      0
    );

    // 触发设备取消选择事件
    const event = new CustomEvent('device-unselected');
    window.dispatchEvent(event);

    // 恢复控制器
    _controlManager.setControlsEnabled(true);

    // 确保高亮功能恢复
    this.enableHighlighting();
  }

  // 这些方法已不再使用，因为设备数据现在通过API获取
  // 保留接口定义以便类型检查

  public hideEnvironmentObjects(): void {
    this.environmentObjects.clear();

    if (!this.camera?.parent) {
      return;
    }

    const scene = this.camera.parent;
    scene.traverse((object: THREE.Object3D) => {
      if (this.isEnvironmentObject(object)) {
        if (object.visible !== false) {
          object.userData._originalVisible = true;
          object.visible = false;
          this.environmentObjects.add(object);
        }
      }
    });
  }

  public restoreEnvironmentObjects(): void {
    this.environmentObjects.forEach((object) => {
      if (object.userData._originalVisible) {
        object.visible = true;
        delete object.userData._originalVisible;
      }
    });
    this.environmentObjects.clear();
  }

  public isEnvironmentObject(object: THREE.Object3D): boolean {
    if (!object.name) {
      return false;
    }

    const name = object.name.toLowerCase();
    const isEnvNamed =
      name.includes('lightprobe') || name.includes('environment') || name.includes('envmap') || name.includes('sky') || name.includes('hdri');

    if (isEnvNamed) {
      return true;
    }

    if (!(object instanceof THREE.Mesh)) {
      return false;
    }

    const material = object.material;
    if (!(material instanceof THREE.Material)) {
      return false;
    }

    return Boolean((material as { envMap?: unknown }).envMap || (material.name && material.name.toLowerCase().includes('env')));
  }

  public disableHighlighting(): void {
    const objectSelection = ObjectSelection.getInstance();
    if (objectSelection?.objectHighlighter) {
      objectSelection.objectHighlighter.setHighlightEnabled(false);
    }
  }

  public enableHighlighting(): void {
    const objectSelection = ObjectSelection.getInstance();
    if (objectSelection?.objectHighlighter) {
      objectSelection.objectHighlighter.setHighlightEnabled(true);
    }
  }

  public handleKeyDown(event: KeyboardEvent): void {
    if (!this.config?.observeMode) {
      return;
    }

    // 按返回键时，需要同时退出设备详情和观察模式
    if (event.key === this.config.observeMode.exitKey && this.config.observeMode.enabled) {
      // 退出观察模式
      this.exitObserveMode();

      // 重置状态
      this.isDeviceObserving = false;

      // 恢复可见性和高亮效果
      this.restoreEnvironmentObjects();
      this.enableHighlighting();
    }
  }

  // 添加更新对象列表的方法
  public updateObjectsList(objects: THREE.Object3D[] | null): void {
    if (objects && objects.length > 0) {
      const sceneManager = SceneManager.getInstance();
      if (sceneManager) {
        this.interactableObjects = sceneManager.getInteractableObjects();
      }
    }
  }

  // 重新初始化事件监听器
  public reinitializeEventListeners(): void {
    if (!this.rendererDom || !this.config.features.enableDoubleClick) {
      console.warn('[ObjectDoubleClickHandler] 无法重新初始化事件监听器：rendererDom不存在或双击功能未启用');
      return;
    }

    console.log('[ObjectDoubleClickHandler] 重新初始化事件监听器');

    // 先移除现有的事件监听器
    if (this.boundDoubleClickHandler) {
      this.rendererDom.removeEventListener('dblclick', this.boundDoubleClickHandler);
      if (this.rendererDom._doubleClickHandlerBound) {
        this.rendererDom._doubleClickHandlerBound = false;
      }
    }

    // 重新绑定双击事件监听器
    if (this.boundDoubleClickHandler) {
      this.rendererDom.addEventListener('dblclick', this.boundDoubleClickHandler);
      this.rendererDom._doubleClickHandlerBound = true;
    }

    console.log('[ObjectDoubleClickHandler] 事件监听器重新绑定完成');
  }

  public dispose(): void {
    if (this.currentTween) {
      this.currentTween.kill();
      this.currentTween = null;
    }

    if (this.boundDoubleClickHandler && this.rendererDom) {
      this.rendererDom.removeEventListener('dblclick', this.boundDoubleClickHandler);
      if (this.rendererDom._doubleClickHandlerBound) {
        this.rendererDom._doubleClickHandlerBound = false;
      }
    }

    // 移除事件监听并取消节流函数
    window.removeEventListener('keydown', this.handleKeyDownThrottled);
    if (this.handleKeyDownThrottled) {
      this.handleKeyDownThrottled.cancel();
    }

    if (this.config.observeMode.enabled) {
      this.exitObserveMode();
    }

    this.selectedObject = null;
    this.originalOrbitConfig = null;
    this.restoreEnvironmentObjects();

    super.dispose();
  }
}
declare global {
  interface HTMLElement {
    _doubleClickHandlerBound?: boolean;
  }
}
