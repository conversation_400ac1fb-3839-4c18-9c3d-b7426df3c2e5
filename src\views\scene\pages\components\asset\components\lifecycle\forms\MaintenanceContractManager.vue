<template>
  <div class="p-[1vw] space-y-[1vw]">
    <div class="text-[0.7vw] text-white">维保合同管理</div>
    <div class="text-[0.6vw] text-gray-400">功能开发中...</div>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue';
  const props = defineProps({ contracts: { type: Array, default: () => [] } });
  const emit = defineEmits(['update:contracts']);
  const contracts = ref([...props.contracts]);
  watch(
    contracts,
    (newValue) => {
      emit('update:contracts', newValue);
    },
    { deep: true }
  );
</script>
