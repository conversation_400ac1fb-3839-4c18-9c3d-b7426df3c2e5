<template>
  <div class="h-[1.6vw] shrink-0 relative">
    <!-- 背景图片 -->
    <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />

    <!-- 标题文字 - 绝对定位确保与背景图片对齐 -->
    <div class="absolute left-[1vw] top-1/2 transform -translate-y-1/2 text-white text-[0.7vw] font-medium z-10">
      {{ title }}
    </div>

    <!-- 详情管理按钮 - 绝对定位到右侧 -->
    <div
      v-if="showDetailButton"
      class="absolute right-[0.5vw] top-1/2 transform -translate-y-1/2 px-[0.6vw] py-[0.25vw] bg-[#3B8EE6]/20 rounded cursor-pointer hover:bg-[#3B8EE6]/30 transition-all text-[0.6vw] text-[#3B8EE6] border border-[#3B8EE6]/30 hover:border-[#3B8EE6]/50 z-10"
      @click="handleDetailClick"
    >
      {{ detailButtonText }}
    </div>

    <!-- 自定义按钮插槽 -->
    <div v-if="$slots.actions" class="absolute right-[0.5vw] top-1/2 transform -translate-y-1/2 z-10">
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script setup>
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';

  // 定义 props
  const props = defineProps({
    title: {
      type: String,
      required: true,
      default: '系统标题',
    },
    showDetailButton: {
      type: Boolean,
      default: true,
    },
    detailButtonText: {
      type: String,
      default: '详情管理',
    },
  });

  // 定义 emits
  const emit = defineEmits(['detail-click']);

  // 处理详情按钮点击
  const handleDetailClick = () => {
    emit('detail-click');
  };
</script>

<style scoped>
  /* 响应式设计 */
  @media (max-width: 1920px) {
    .absolute.left-\[1vw\] {
      left: 0.8vw;
    }

    .text-\[0\.7vw\] {
      font-size: 0.65vw;
    }

    .text-\[0\.6vw\] {
      font-size: 0.55vw;
    }
  }

  @media (max-width: 1366px) {
    .absolute.left-\[1vw\] {
      left: 0.6vw;
    }

    .text-\[0\.7vw\] {
      font-size: 0.6vw;
    }

    .text-\[0\.6vw\] {
      font-size: 0.5vw;
    }

    .px-\[0\.6vw\] {
      padding-left: 0.5vw;
      padding-right: 0.5vw;
    }

    .py-\[0\.25vw\] {
      padding-top: 0.2vw;
      padding-bottom: 0.2vw;
    }
  }

  /* 确保文字清晰可读 */
  .text-white {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  /* 按钮悬停效果增强 */
  .cursor-pointer:hover {
    transform: translateY(-50%) scale(1.02);
  }

  /* 确保在不同屏幕尺寸下的可见性 */
  @media (max-width: 1024px) {
    .h-\[1\.6vw\] {
      height: 2.5vw;
    }

    .text-\[0\.7vw\] {
      font-size: 1vw;
    }

    .text-\[0\.6vw\] {
      font-size: 0.9vw;
    }
  }
</style>
