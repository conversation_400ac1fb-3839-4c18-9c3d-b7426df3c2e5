declare module '*.vue' {
  import { DefineComponent } from 'vue';
  const Component: DefineComponent<{}, {}, any>;
  export default Component;
}

declare module 'ant-design-vue/es/locale/*' {
  import { Locale } from 'ant-design-vue/types/locale-provider';
  const locale: Locale & ReadonlyRecordable;
  export default locale as Locale & ReadonlyRecordable;
}

declare module 'virtual:*' {
  const result: any;
  export default result;
}

declare module 'vue' {
  export * from '@vue/runtime-dom';
  export * from '@vue/reactivity';
  export * from '@vue/runtime-core';
  export * from '@vue/shared';

  import {
    DefineComponent,
    ComponentPublicInstance,
    FunctionalComponent,
    PropType as VuePropType,
    App,
    VNode,
    Ref as VueRef,
    ComputedRef,
    WritableComputedRef,
    UnwrapRef,
    ShallowRef,
    InjectionKey,
    Directive,
    Plugin,
    Component,
  } from '@vue/runtime-core';

  export { DefineComponent, ComponentPublicInstance, FunctionalComponent };
  export type PropType<T> = VuePropType<T>;

  // Core API exports
  export function defineComponent<Props = {}, RawBindings = {}, D = {}, C = {}, M = {}, E = {}, EE = string>(
    options: any
  ): DefineComponent<Props, RawBindings, D, C, M, E, EE>;

  // Reactivity API
  export function ref<T>(value: T): VueRef<UnwrapRef<T>>;
  export function reactive<T extends object>(target: T): UnwrapRef<T>;
  export function computed<T>(getter: () => T): ComputedRef<T>;
  export function computed<T>(options: { get: () => T; set: (value: T) => void }): WritableComputedRef<T>;
  export function readonly<T extends object>(target: T): Readonly<UnwrapRef<T>>;
  export function shallowRef<T>(value: T): ShallowRef<T>;
  export function shallowReactive<T extends object>(target: T): T;
  export function shallowReadonly<T extends object>(target: T): Readonly<T>;
  export function isRef<T>(r: VueRef<T> | unknown): r is VueRef<T>;
  export function toRef<T extends object, K extends keyof T>(object: T, key: K): VueRef<T[K]>;
  export function toRefs<T extends object>(object: T): { [K in keyof T]: VueRef<T[K]> };
  export function unref<T>(ref: T | VueRef<T>): T;
  export function proxyRefs<T extends object>(objectWithRefs: T): any;
  export function customRef<T>(factory: any): VueRef<T>;
  export function triggerRef(ref: VueRef): void;
  export function isReactive(value: unknown): boolean;
  export function isReadonly(value: unknown): boolean;
  export function isProxy(value: unknown): boolean;
  export function toRaw<T>(observed: T): T;
  export function markRaw<T extends object>(value: T): T;

  // Lifecycle hooks
  export function onBeforeMount(hook: () => any, target?: ComponentPublicInstance | null): void;
  export function onMounted(hook: () => any, target?: ComponentPublicInstance | null): void;
  export function onBeforeUpdate(hook: () => any, target?: ComponentPublicInstance | null): void;
  export function onUpdated(hook: () => any, target?: ComponentPublicInstance | null): void;
  export function onBeforeUnmount(hook: () => any, target?: ComponentPublicInstance | null): void;
  export function onUnmounted(hook: () => any, target?: ComponentPublicInstance | null): void;
  export function onActivated(hook: () => any, target?: ComponentPublicInstance | null): void;
  export function onDeactivated(hook: () => any, target?: ComponentPublicInstance | null): void;
  export function onErrorCaptured(
    hook: (err: Error, instance: ComponentPublicInstance | null, info: string) => boolean | void,
    target?: ComponentPublicInstance | null
  ): void;
  export function onRenderTracked(hook: (e: any) => any, target?: ComponentPublicInstance | null): void;
  export function onRenderTriggered(hook: (e: any) => any, target?: ComponentPublicInstance | null): void;
  export function onServerPrefetch(hook: () => any, target?: ComponentPublicInstance | null): void;

  // Watch API
  export function watch<T>(source: T | (() => T), callback: (newVal: T, oldVal: T) => void, options?: any): any;
  export function watchEffect(effect: () => void, options?: any): any;
  export function watchPostEffect(effect: () => void, options?: any): any;
  export function watchSyncEffect(effect: () => void, options?: any): any;

  // Composition API
  export function provide<T>(key: InjectionKey<T> | string | number, value: T): void;
  export function inject<T>(key: InjectionKey<T> | string): T | undefined;
  export function inject<T>(key: InjectionKey<T> | string, defaultValue: T, treatDefaultAsFactory?: false): T;
  export function inject<T>(key: InjectionKey<T> | string, defaultValue: T | (() => T), treatDefaultAsFactory: true): T;
  export function getCurrentInstance(): ComponentPublicInstance | null;

  // Utilities
  export function nextTick(fn?: () => void): Promise<void>;
  export function createVNode(type: any, props?: any, children?: any): VNode;
  export { createVNode as h };
  export function mergeProps(...args: any[]): any;
  export function isVNode(value: any): value is VNode;
  export function Fragment(props: any, context: any): any;
  export function Text(props: any, context: any): any;
  export function Comment(props: any, context: any): any;
  export function Static(props: any, context: any): any;
  export function Teleport(props: any, context: any): any;
  export function Suspense(props: any, context: any): any;
  export function KeepAlive(props: any, context: any): any;
  export function BaseTransition(props: any, context: any): any;
  export function withDirectives(vnode: VNode, directives: any[]): VNode;
  export function createApp(rootComponent: Component, rootProps?: any): App;

  // Script setup helpers
  export function withDefaults<T>(props: T, defaults: any): T;
  export function defineProps<T = {}>(): T;
  export function defineEmits<T = {}>(): T;
  export function defineExpose(exposed?: Record<string, any>): void;
  export function withCtx(fn: Function): Function;
  export function useSlots(): any;
  export function useAttrs(): any;

  export interface Ref<T = any> {
    value: T;
  }

  export interface SetupContext<E = any> {
    attrs: any;
    slots: any;
    emit: E;
    expose: (exposed?: Record<string, any>) => void;
  }
}

declare module '@ant-design/icons-vue' {
  import { DefineComponent } from 'vue';
  export const DownOutlined: DefineComponent<{}, {}, any>;
  export const PlusOutlined: DefineComponent<{}, {}, any>;
  export const UpOutlined: DefineComponent<{}, {}, any>;
  export const LeftOutlined: DefineComponent<{}, {}, any>;
  export const RightOutlined: DefineComponent<{}, {}, any>;
  export const SearchOutlined: DefineComponent<{}, {}, any>;
  export const DeleteOutlined: DefineComponent<{}, {}, any>;
  export const EditOutlined: DefineComponent<{}, {}, any>;
  export const SettingOutlined: DefineComponent<{}, {}, any>;
  export const CloseOutlined: DefineComponent<{}, {}, any>;
  export const CheckOutlined: DefineComponent<{}, {}, any>;
  export const LoadingOutlined: DefineComponent<{}, {}, any>;
  export const MenuOutlined: DefineComponent<{}, {}, any>;
  export const UserOutlined: DefineComponent<{}, {}, any>;
  export const HomeOutlined: DefineComponent<{}, {}, any>;
  export const FileOutlined: DefineComponent<{}, {}, any>;
  export const FolderOutlined: DefineComponent<{}, {}, any>;
  export const SaveOutlined: DefineComponent<{}, {}, any>;
  export const CopyOutlined: DefineComponent<{}, {}, any>;
  export const InfoCircleOutlined: DefineComponent<{}, {}, any>;
  export const WarningOutlined: DefineComponent<{}, {}, any>;
  export const ExclamationCircleOutlined: DefineComponent<{}, {}, any>;
  export const QuestionCircleOutlined: DefineComponent<{}, {}, any>;
  // 添加更多图标类型声明...
  const icons: Record<string, DefineComponent<{}, {}, any>>;
  export default icons;
}

declare module 'ant-design-vue' {
  import { App, DefineComponent, Plugin } from 'vue';

  // 常用组件导出
  export const Button: DefineComponent<{}, {}, any>;
  export const Modal: DefineComponent<{}, {}, any> & {
    info: (config: any) => any;
    success: (config: any) => any;
    error: (config: any) => any;
    warning: (config: any) => any;
    confirm: (config: any) => any;
    destroyAll: () => void;
  };
  export const Form: DefineComponent<{}, {}, any> & {
    Item: DefineComponent<{}, {}, any>;
    useForm: (config?: any) => any;
  };
  export const Input: DefineComponent<{}, {}, any> & {
    Search: DefineComponent<{}, {}, any>;
    TextArea: DefineComponent<{}, {}, any>;
    Password: DefineComponent<{}, {}, any>;
    Group: DefineComponent<{}, {}, any>;
  };
  export const Select: DefineComponent<{}, {}, any> & {
    Option: DefineComponent<{}, {}, any>;
    OptGroup: DefineComponent<{}, {}, any>;
  };
  export const Dropdown: DefineComponent<{}, {}, any>;
  export const Menu: DefineComponent<{}, {}, any> & {
    Item: DefineComponent<{}, {}, any>;
    SubMenu: DefineComponent<{}, {}, any>;
    ItemGroup: DefineComponent<{}, {}, any>;
    Divider: DefineComponent<{}, {}, any>;
  };
  export const Spin: DefineComponent<{}, {}, any>;
  export const Radio: DefineComponent<{}, {}, any> & {
    Group: DefineComponent<{}, {}, any>;
    Button: DefineComponent<{}, {}, any>;
  };
  export const Checkbox: DefineComponent<{}, {}, any> & {
    Group: DefineComponent<{}, {}, any>;
  };
  export const Switch: DefineComponent<{}, {}, any>;
  export const DatePicker: DefineComponent<{}, {}, any> & {
    RangePicker: DefineComponent<{}, {}, any>;
    MonthPicker: DefineComponent<{}, {}, any>;
    WeekPicker: DefineComponent<{}, {}, any>;
    YearPicker: DefineComponent<{}, {}, any>;
    QuarterPicker: DefineComponent<{}, {}, any>;
  };
  export const TimePicker: DefineComponent<{}, {}, any> & {
    RangePicker: DefineComponent<{}, {}, any>;
  };
  export const Upload: DefineComponent<{}, {}, any> & {
    Dragger: DefineComponent<{}, {}, any>;
  };
  export const Row: DefineComponent<{}, {}, any>;
  export const Col: DefineComponent<{}, {}, any>;
  export const Table: DefineComponent<{}, {}, any> & {
    Column: DefineComponent<{}, {}, any>;
    ColumnGroup: DefineComponent<{}, {}, any>;
  };
  export const Tag: DefineComponent<{}, {}, any> & {
    CheckableTag: DefineComponent<{}, {}, any>;
  };
  export const Tabs: DefineComponent<{}, {}, any> & {
    TabPane: DefineComponent<{}, {}, any>;
  };
  export const Card: DefineComponent<{}, {}, any> & {
    Meta: DefineComponent<{}, {}, any>;
    Grid: DefineComponent<{}, {}, any>;
  };
  export const Avatar: DefineComponent<{}, {}, any> & {
    Group: DefineComponent<{}, {}, any>;
  };
  export const Badge: DefineComponent<{}, {}, any> & {
    Ribbon: DefineComponent<{}, {}, any>;
  };
  export const Popover: DefineComponent<{}, {}, any>;
  export const Tooltip: DefineComponent<{}, {}, any>;
  export const Textarea: DefineComponent<{}, {}, any>;
  export const message: {
    success: (content: any, duration?: number, onClose?: () => void) => void;
    error: (content: any, duration?: number, onClose?: () => void) => void;
    info: (content: any, duration?: number, onClose?: () => void) => void;
    warning: (content: any, duration?: number, onClose?: () => void) => void;
    loading: (content: any, duration?: number, onClose?: () => void) => void;
    destroy: () => void;
    config: (options: any) => void;
  };
  export const notification: {
    success: (config: any) => void;
    error: (config: any) => void;
    info: (config: any) => void;
    warning: (config: any) => void;
    open: (config: any) => void;
    destroy: () => void;
    config: (options: any) => void;
  };

  // 主要导出
  const Antd: Plugin;
  export default Antd;

  // 常用类型导出
  export interface FormInstance {
    validateFields: (nameList?: string[]) => Promise<any>;
    getFieldsValue: (nameList?: string[] | true) => any;
    setFieldsValue: (values: any) => void;
    resetFields: (nameList?: string[]) => void;
    // 添加更多方法...
  }
}

// 添加pinia类型声明
declare module 'pinia' {
  export interface PiniaCustomProperties {}
  export interface PiniaCustomStateProperties {}

  export function createPinia(): any;
  export function defineStore<T = any>(id: string, setup: () => T): () => T;
  export function defineStore<T = any>(id: string, options: any): () => T;
  export function storeToRefs<T = any>(store: T): any;
  export function acceptHMRUpdate(hotUpdate: any, hot: any): any;
}

// 添加dat.gui类型声明
declare module 'dat.gui' {
  export interface GUIController {
    setValue(value: any): GUIController;
    updateDisplay(): GUIController;
    name(name: string): GUIController;
    onChange(callback: (value: any) => void): GUIController;
    listen(): GUIController;
  }

  export class GUI {
    domElement: HTMLElement;
    constructor(options?: { autoPlace?: boolean; width?: number });
    add(object: any, property: string, ...args: any[]): GUIController;
    addColor(object: any, property: string): GUIController;
    addFolder(name: string): GUI;
    destroy(): void;
    open(): void;
    close(): void;
  }

  export default GUI;
}

// 添加three.js相关类型声明
declare module 'three' {
  export * from '@types/three';
}

declare module 'three/examples/jsm/controls/OrbitControls' {
  export class OrbitControls {
    constructor(camera: any, domElement?: HTMLElement);
    update(): void;
    dispose(): void;
    enabled: boolean;
    target: any;
    enableDamping: boolean;
    dampingFactor: number;
    enableZoom: boolean;
    enableRotate: boolean;
    enablePan: boolean;
    autoRotate: boolean;
    autoRotateSpeed: number;
    minDistance: number;
    maxDistance: number;
    minPolarAngle: number;
    maxPolarAngle: number;
    addEventListener(type: string, listener: (event: any) => void): void;
    removeEventListener(type: string, listener: (event: any) => void): void;
  }
}

declare module 'three/examples/jsm/loaders/GLTFLoader' {
  export class GLTFLoader {
    constructor();
    load(url: string, onLoad: (gltf: any) => void, onProgress?: (progress: any) => void, onError?: (error: any) => void): void;
    setDRACOLoader(dracoLoader: any): void;
  }
}

declare module 'three/examples/jsm/loaders/DRACOLoader' {
  export class DRACOLoader {
    constructor();
    setDecoderPath(path: string): void;
    setDecoderConfig(config: any): void;
    dispose(): void;
  }
}

declare module 'three/examples/jsm/postprocessing/EffectComposer' {
  export class EffectComposer {
    constructor(renderer: any, renderTarget?: any);
    addPass(pass: any): void;
    render(deltaTime?: number): void;
    setSize(width: number, height: number): void;
  }
}

declare module 'three/examples/jsm/postprocessing/RenderPass' {
  export class RenderPass {
    constructor(scene: any, camera: any, overrideMaterial?: any, clearColor?: any, clearAlpha?: number);
  }
}

declare module 'three/examples/jsm/postprocessing/UnrealBloomPass' {
  export class UnrealBloomPass {
    constructor(resolution: any, strength: number, radius: number, threshold: number);
    strength: number;
    radius: number;
    threshold: number;
  }
}

declare module 'three/examples/jsm/postprocessing/ShaderPass' {
  export class ShaderPass {
    constructor(shader: any, textureID?: string);
    uniforms: any;
  }
}

declare module 'three/examples/jsm/shaders/CopyShader' {
  export const CopyShader: any;
}

declare module 'three/examples/jsm/shaders/FXAAShader' {
  export const FXAAShader: any;
}

// 添加GSAP类型声明
declare module 'gsap' {
  export interface GSAPTimeline {
    to(target: any, vars: any): GSAPTimeline;
    from(target: any, vars: any): GSAPTimeline;
    fromTo(target: any, fromVars: any, toVars: any): GSAPTimeline;
    set(target: any, vars: any): GSAPTimeline;
    play(): GSAPTimeline;
    pause(): GSAPTimeline;
    reverse(): GSAPTimeline;
    restart(): GSAPTimeline;
    kill(): void;
    duration(): number;
    duration(value: number): GSAPTimeline;
  }

  export const gsap: {
    to(target: any, vars: any): any;
    from(target: any, vars: any): any;
    fromTo(target: any, fromVars: any, toVars: any): any;
    set(target: any, vars: any): any;
    timeline(vars?: any): GSAPTimeline;
    killTweensOf(target: any): void;
  };

  export function timeline(vars?: any): GSAPTimeline;
}

// 添加stats.js类型声明
declare module 'stats.js' {
  export default class Stats {
    constructor();
    dom: HTMLElement;
    begin(): void;
    end(): void;
    update(): void;
    showPanel(id: number): void;
  }
}

// 添加dayjs类型声明
declare module 'dayjs' {
  interface Dayjs {
    format(template?: string): string;
    valueOf(): number;
    unix(): number;
    toDate(): Date;
    toISOString(): string;
    toString(): string;
    utc(): Dayjs;
    local(): Dayjs;
    isValid(): boolean;
    year(): number;
    year(value: number): Dayjs;
    month(): number;
    month(value: number): Dayjs;
    date(): number;
    date(value: number): Dayjs;
    day(): number;
    day(value: number): Dayjs;
    hour(): number;
    hour(value: number): Dayjs;
    minute(): number;
    minute(value: number): Dayjs;
    second(): number;
    second(value: number): Dayjs;
    millisecond(): number;
    millisecond(value: number): Dayjs;
    add(value: number, unit: string): Dayjs;
    subtract(value: number, unit: string): Dayjs;
    startOf(unit: string): Dayjs;
    endOf(unit: string): Dayjs;
    isBefore(date: Dayjs | string | Date): boolean;
    isAfter(date: Dayjs | string | Date): boolean;
    isSame(date: Dayjs | string | Date): boolean;
    diff(date: Dayjs | string | Date, unit?: string): number;
  }

  interface DayjsStatic {
    (date?: string | number | Date | Dayjs): Dayjs;
    extend(plugin: any): DayjsStatic;
    locale(preset: string | any, object?: any): string;
    isDayjs(d: any): d is Dayjs;
    unix(t: number): Dayjs;
  }

  const dayjs: DayjsStatic;
  export = dayjs;
}

// 添加图片资源类型声明
declare module '*.png' {
  const value: string;
  export default value;
}

declare module '*.jpg' {
  const value: string;
  export default value;
}

declare module '*.jpeg' {
  const value: string;
  export default value;
}

declare module '*.gif' {
  const value: string;
  export default value;
}

declare module '*.svg' {
  const value: string;
  export default value;
}

declare module '*.webp' {
  const value: string;
  export default value;
}
