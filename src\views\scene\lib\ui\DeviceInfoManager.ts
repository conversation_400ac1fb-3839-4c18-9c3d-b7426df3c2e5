import { createVNode, createApp } from 'vue';
import type { ComponentPublicInstance } from 'vue';

// 定义VNode类型
type VNode = ReturnType<typeof createVNode>;
import * as THREE from 'three';
import DeviceInfoTooltip from '/@/views/scene/components/DeviceInfoTooltip.vue';
import { parseDeviceName, getDeviceCategory } from '/@/utils/deviceNameMapping';
import { getDeviceDetailByCode, type DeviceDetailData as DeviceApiDataImported } from '/@/api/scene'; // 导入API函数和类型

// interface DeviceApiData { // 此接口定义已移除
//   id: number;
//   name: string;
//   code: string;
//   type: number;
//   remark: string;
//   dataTime: string;
//   valueData: string;
//   addr: number;
// }
type DeviceApiData = DeviceApiDataImported;

interface DeviceData {
  id: string;
  name: string;
  type: string;
  status?: string;
  code?: string;
  apiData?: DeviceApiData[]; // 使用更新后的类型
  cpuUsage?: number;
  memoryUsage?: number;
  storageUsage?: number;
  warnings?: Array<{
    message: string;
    time: string;
    level: string;
  }>;
  [key: string]: any;
}

interface ScreenPosition {
  x: number;
  y: number;
}

interface CurrentDevice {
  uuid: string;
  object: THREE.Object3D;
  screenPosition: ScreenPosition;
  data: DeviceData;
}

interface ComponentProps {
  visible: boolean;
  x: number;
  y: number;
  data: DeviceData;
  [key: string]: unknown;
}

/**
 * 设备信息管理器 - 管理3D场景中设备的悬浮信息面板
 */
export class DeviceInfoManager {
  private static instance: DeviceInfoManager;
  private container: HTMLDivElement = document.createElement('div');
  private vNode: ReturnType<typeof createVNode> | null = null;
  private currentDevice: CurrentDevice | null = null;
  private visible: boolean = false;
  private updateTimer: number | null = null;
  private debugMode: boolean = false;
  private showAttempts: number = 0;
  private showFailures: number = 0;

  // 恢复必要的成员定义
  private pendingApiCalls: Set<string> = new Set();
  private failedApiCalls: Set<string> = new Set();
  private readonly failedCallCooldown: number = 2 * 60 * 1000;

  private currentApp: ComponentPublicInstance | null = null; // Track the current Vue app instance

  constructor() {
    // 单例模式
    if (DeviceInfoManager.instance) {
      return DeviceInfoManager.instance;
    }

    DeviceInfoManager.instance = this;

    // 创建容器
    document.body.appendChild(this.container);

    // 初始化
    this._init();

    return this;
  }

  public static getInstance(): DeviceInfoManager {
    if (!DeviceInfoManager.instance) {
      return new DeviceInfoManager();
    }
    return DeviceInfoManager.instance;
  }

  private _init(): void {
    // 创建Vue组件
    this._renderComponent({
      visible: false,
      x: 0,
      y: 0,
      data: {} as DeviceData,
    });

    // 添加窗口大小变化监听
    window.addEventListener('resize', this._onWindowResize.bind(this));
  }

  private _renderComponent(props: ComponentProps): void {
    const newProps = {
      ...props,
      y: props.y - 25, // 增加向上的偏移量
      // 添加关闭事件处理器
      'onUpdate:visible': (visible: boolean) => {
        if (!visible) {
          this.hide();
        }
      },
    };

    // 性能优化：如果新props与当前props相同，不重新渲染
    if (this.vNode && this._propsAreEqual(this.vNode.props as ComponentProps, newProps)) {
      return;
    }

    try {
      // 如果存在之前的app实例，先卸载它
      if (this.currentApp) {
        this.currentApp.unmount();
        this.container.innerHTML = ''; // 清空容器
      }

      // 创建新的Vue应用实例
      const app = createApp(DeviceInfoTooltip, newProps);
      app.mount(this.container);

      // 保存当前app实例
      this.currentApp = app;

      // 保存VNode引用
      this.vNode = createVNode(DeviceInfoTooltip, newProps);
    } catch (error) {
      console.error('[DeviceInfoManager] 渲染组件失败:', error);
      // 确保容器被清空
      this.container.innerHTML = '';
      this.vNode = null;
      this.currentApp = null;
    }
  }

  /**
   * 比较两个组件属性是否相等，避免不必要的重新渲染
   */
  private _propsAreEqual(oldProps: ComponentProps | null, newProps: ComponentProps): boolean {
    if (!oldProps || !newProps) return false;

    // 检查可见性、位置和数据
    if (oldProps.visible !== newProps.visible) return false;
    if (oldProps.x !== newProps.x || oldProps.y !== newProps.y) return false;

    // 简单检查数据对象的关键属性
    if (oldProps.data && newProps.data) {
      const oldData = oldProps.data;
      const newData = newProps.data;

      // 比较基本属性
      if (oldData.id !== newData.id) return false;
      if (oldData.name !== newData.name) return false;
      if (oldData.type !== newData.type) return false;
      if (oldData.status !== newData.status) return false;

      // 比较API数据
      const oldApiData = oldData.apiData;
      const newApiData = newData.apiData;

      // 如果一个有API数据而另一个没有，则不相等
      if (!!oldApiData !== !!newApiData) return false;

      // 如果都有API数据，比较数据长度
      if (oldApiData && newApiData) {
        if (oldApiData.length !== newApiData.length) return false;

        // 简单检查第一条数据是否相同（避免深度比较所有数据）
        if (oldApiData.length > 0 && newApiData.length > 0) {
          if (oldApiData[0].id !== newApiData[0].id) return false;
          if (oldApiData[0].valueData !== newApiData[0].valueData) return false;
        }
      }

      // 比较告警数量
      const oldWarningsLength = oldData.warnings?.length || 0;
      const newWarningsLength = newData.warnings?.length || 0;
      if (oldWarningsLength !== newWarningsLength) return false;
    } else {
      return false;
    }

    return true;
  }

  private _onWindowResize = (): void => {
    if (this.visible && this.currentDevice) {
      this.updatePosition(this.currentDevice.screenPosition);
    }
  };

  public updatePosition(screenPosition: ScreenPosition): void {
    if (!this.currentDevice) return;

    this.currentDevice.screenPosition = screenPosition;

    // 直接更新位置，移除节流处理
    this._renderComponent({
      visible: this.visible,
      x: screenPosition.x,
      y: screenPosition.y,
      data: this.currentDevice.data,
    });
  }

  /**
   * 显示设备信息面板
   * @param device 设备对象
   * @param screenPosition 屏幕位置
   * @param deviceData 可选的设备数据
   */
  public async showDeviceInfo(device: THREE.Object3D, screenPosition: ScreenPosition, deviceData: DeviceData | null = null): Promise<void> {
    try {
      this.showAttempts++;

      // 如果是同一设备，并且面板已经显示，则只更新位置
      if (this.currentDevice && this.currentDevice.uuid === device.uuid && this.visible) {
        this.updatePosition(screenPosition);
        return;
      }

      // 停止之前的更新
      this._stopDataUpdates();

      // 提取或使用传入的设备数据
      const data = deviceData || this._extractDeviceData(device);

      // 设置当前设备
      this.currentDevice = {
        uuid: device.uuid,
        object: device,
        screenPosition,
        data: data,
      };

      // 显示面板（先显示基本信息，标记为加载中状态）
      this.visible = true;
      this._renderComponent({
        visible: true,
        x: screenPosition.x,
        y: screenPosition.y,
        data: data,
      });

      // 尝试加载API数据（优先使用缓存）
      if (data.code) {
        // 异步加载数据，不阻塞UI显示
        this._loadDeviceDataFromApi(data.code)
          .then((apiData) => {
            // 确保设备仍然是当前设备
            if (this.currentDevice && this.currentDevice.uuid === device.uuid) {
              // 更新设备数据（即使apiData是空数组也更新）
              this.currentDevice.data = {
                ...this.currentDevice.data,
                apiData: apiData || [], // 确保apiData不为null
              };

              // 更新组件显示
              this._renderComponent({
                visible: true,
                x: this.currentDevice.screenPosition.x,
                y: this.currentDevice.screenPosition.y,
                data: this.currentDevice.data,
              });

              if (this.debugMode) {
                if (apiData && apiData.length > 0) {
                  console.log(`[DeviceInfoManager] 设备数据已加载并显示: ${data.code}，共 ${apiData.length} 条记录`);
                } else {
                  console.log(`[DeviceInfoManager] 设备无数据: ${data.code}`);
                }
              }
            }
          })
          .catch((err) => {
            // 出错时也更新UI，显示暂无数据
            if (this.currentDevice && this.currentDevice.uuid === device.uuid) {
              this.currentDevice.data = {
                ...this.currentDevice.data,
                apiData: [], // 设置为空数组
              };

              // 更新组件显示
              this._renderComponent({
                visible: true,
                x: this.currentDevice.screenPosition.x,
                y: this.currentDevice.screenPosition.y,
                data: this.currentDevice.data,
              });
            }

            console.error(`[DeviceInfoManager] 加载设备数据失败: ${data.code}`, err);
          });
      }

      // 开始数据更新
      this._startDataUpdates();

      if (this.debugMode) {
        console.log(`[DeviceInfoManager] 显示设备信息面板: ${device.name}`, screenPosition);
      }
    } catch (err) {
      this.showFailures++;
      console.error('[DeviceInfoManager] 显示设备信息面板失败', err, device, screenPosition);
    }
  }

  public hide(): void {
    if (!this.visible) return; // 已经隐藏，不需要重复操作

    this.visible = false;
    this._stopDataUpdates();

    // 清理当前设备引用
    this.currentDevice = null;

    this._renderComponent({
      visible: false,
      x: 0,
      y: 0,
      data: {} as DeviceData,
      'onUpdate:visible': () => {}, // 隐藏时不需要事件处理器
    });

    if (this.debugMode) {
      console.log('隐藏设备信息面板');
    }
  }

  private _extractDeviceData(device: THREE.Object3D): DeviceData {
    const userData = device.userData || {};

    // 解析设备名称和类型
    const displayName = parseDeviceName(device.name);
    const deviceType = getDeviceCategory(device.name);

    return {
      id: userData.id || device.name || '未知',
      name: displayName,
      type: deviceType,
      code: device.name, // 使用设备名称作为编码
      ...userData,
    };
  }
  /**
   * 从API加载设备数据（每次都重新获取最新数据）
   * @param deviceCode 设备编码
   * @returns Promise<DeviceApiData[] | null>
   */
  private async _loadDeviceDataFromApi(deviceCode: string): Promise<DeviceApiData[] | null> {
    // 返回类型更新
    if (!deviceCode) {
      console.warn('[DeviceInfoManager] 设备编码为空，无法加载数据');
      return null;
    }

    // 检查是否已有相同的API调用正在进行
    if (this.pendingApiCalls.has(deviceCode)) {
      if (this.debugMode) {
        console.log(`[DeviceInfoManager] API调用正在进行中，跳过重复请求: ${deviceCode}`);
      }
      return null;
    }

    // 检查是否在失败调用冷却期内
    if (this.failedApiCalls.has(deviceCode)) {
      if (this.debugMode) {
        console.log(`[DeviceInfoManager] 设备在冷却期内，跳过API调用: ${deviceCode}`);
      }
      return null;
    }

    // 标记API调用正在进行
    this.pendingApiCalls.add(deviceCode);

    try {
      if (this.debugMode) {
        console.log(`[DeviceInfoManager] 从API加载设备数据: ${deviceCode}`);
      }

      const response = await getDeviceDetailByCode(deviceCode); // 使用导入的API函数

      if (response && Array.isArray(response)) {
        if (response.length > 0) {
          if (this.debugMode) {
            console.log(`[DeviceInfoManager] 成功从API加载设备数据，共 ${response.length} 条记录`);
          }
        } else {
          if (this.debugMode) {
            console.log(`[DeviceInfoManager] API返回的数据为空数组，设备可能没有数据: ${deviceCode}`);
          }
          // 将失败的设备加入冷却列表
          this.failedApiCalls.add(deviceCode);
          // 设置定时器清除冷却状态
          setTimeout(() => {
            this.failedApiCalls.delete(deviceCode);
          }, this.failedCallCooldown);
        }

        // 不再缓存数据，每次都获取最新数据

        return response;
      } else {
        console.warn('[DeviceInfoManager] API返回的数据格式异常:', response);
        // 标记为失败调用
        this.failedApiCalls.add(deviceCode);
        setTimeout(() => {
          this.failedApiCalls.delete(deviceCode);
        }, this.failedCallCooldown);
        return []; // 返回空数组而不是null，确保UI显示"暂无数据"
      }
    } catch (err) {
      console.error('[DeviceInfoManager] 获取设备数据出错:', err);
      // 标记为失败调用
      this.failedApiCalls.add(deviceCode);
      setTimeout(() => {
        this.failedApiCalls.delete(deviceCode);
      }, this.failedCallCooldown);
      return []; // 返回空数组而不是null，确保UI显示"暂无数据"
    } finally {
      // 移除正在进行中的API调用标记
      this.pendingApiCalls.delete(deviceCode);
    }
  }

  private _startDataUpdates(): void {
    this._stopDataUpdates();

    this.updateTimer = window.setInterval(async () => {
      if (!this.currentDevice || !this.visible) return;

      try {
        const originalData = this.currentDevice.data;

        // 如果有设备编码，尝试从API更新数据
        if (originalData.code) {
          // 每次都重新获取最新数据，不使用缓存
          const apiData = await this._loadDeviceDataFromApi(originalData.code);

          // 无论apiData是否为空数组，都更新UI
          // 更新设备数据
          const updatedData: DeviceData = {
            ...originalData,
            apiData: apiData || [], // 确保apiData不为null
            status: '正常', // 可以根据API数据判断状态
          };

          // 更新当前设备数据
          this.currentDevice.data = updatedData;

          // 更新UI
          this._renderComponent({
            visible: this.visible,
            x: this.currentDevice.screenPosition.x,
            y: this.currentDevice.screenPosition.y,
            data: updatedData,
          });

          if (this.debugMode) {
            if (apiData && apiData.length > 0) {
              console.log(`[DeviceInfoManager] 设备数据已更新: ${originalData.code}，共 ${apiData.length} 条记录`);
            } else {
              console.log(`[DeviceInfoManager] 设备无数据: ${originalData.code}`);
            }
            console.log(`[DeviceInfoManager] 数据更新完成`);
          }
        }
      } catch (err) {
        console.error('[DeviceInfoManager] 设备数据更新失败', err);
      }
    }, 10000); // 每10秒更新一次，避免频繁请求API
  }

  private _stopDataUpdates(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }
  }

  public updateDeviceData(deviceId: string, data: Partial<DeviceData>): void {
    if (!this.currentDevice || this.currentDevice.data.id !== deviceId) return;

    this.currentDevice.data = {
      ...this.currentDevice.data,
      ...data,
    };

    if (this.visible) {
      this._renderComponent({
        visible: true,
        x: this.currentDevice.screenPosition.x,
        y: this.currentDevice.screenPosition.y,
        data: this.currentDevice.data,
      });
    }
  }

  /**
   * 销毁组件并清理资源
   */
  public dispose(): void {
    // 停止数据更新
    this._stopDataUpdates();

    // 移除事件监听
    window.removeEventListener('resize', this._onWindowResize);

    // 卸载Vue应用实例
    if (this.currentApp) {
      this.currentApp.unmount();
    }

    // 移除DOM元素
    if (this.container && this.container.parentNode) {
      this.container.innerHTML = '';
      this.container.parentNode.removeChild(this.container);
    }

    // 清理引用
    this.vNode = null;
    this.currentDevice = null;
    this.currentApp = null;

    if (this.debugMode) {
      console.log('[DeviceInfoManager] 组件已销毁，资源已清理');
    }

    // 使用类型断言避免类型错误
    DeviceInfoManager.instance = undefined as unknown as DeviceInfoManager;
  }

  /**
   * 强制重置DeviceInfoManager状态
   * 用于楼层切换等场景，确保面板显示正常
   */
  public forceReset(): void {
    console.log('[DeviceInfoManager] 强制重置状态');

    // 停止所有数据更新
    this._stopDataUpdates();

    // 隐藏当前面板
    this.hide();

    // 清理失败的API调用记录
    this.failedApiCalls.clear();
    this.pendingApiCalls.clear();

    // 重置统计信息
    this.showAttempts = 0;
    this.showFailures = 0;

    console.log('[DeviceInfoManager] 状态重置完成');
  }

  /**
   * 设置调试模式
   * @param enabled 是否启用调试模式
   */
  public setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
    console.log(`[DeviceInfoManager] 调试模式已${enabled ? '启用' : '禁用'}`);

    if (enabled) {
      console.log(`[DeviceInfoManager] 实时数据模式已启用，每次都会重新获取最新设备数据`);
    }
  }
}
