<template>
  <a-modal v-model:visible="visible" title="健康登记" width="600px" @ok="handleSubmit" @cancel="handleCancel">
    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="姓名" name="name">
            <a-input v-model:value="formData.name" placeholder="请输入姓名" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="联系电话" name="phone">
            <a-input v-model:value="formData.phone" placeholder="请输入联系电话" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="身份证号" name="personId">
            <a-input v-model:value="formData.personId" placeholder="请输入身份证号" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="部门" name="department">
            <a-input v-model:value="formData.department" placeholder="请输入部门" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="体温" name="temperature">
            <a-input-number
              v-model:value="formData.temperature"
              :min="35"
              :max="42"
              :step="0.1"
              :precision="1"
              placeholder="体温"
              style="width: 100%"
            >
              <template #addonAfter>°C</template>
            </a-input-number>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="健康码" name="healthCode">
            <a-select v-model:value="formData.healthCode" placeholder="健康码状态">
              <a-select-option value="green">绿码</a-select-option>
              <a-select-option value="yellow">黄码</a-select-option>
              <a-select-option value="red">红码</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="疫苗状态" name="vaccineStatus">
            <a-select v-model:value="formData.vaccineStatus" placeholder="疫苗接种状态">
              <a-select-option value="none">未接种</a-select-option>
              <a-select-option value="partial">部分接种</a-select-option>
              <a-select-option value="full">完全接种</a-select-option>
              <a-select-option value="booster">加强针</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="核酸检测日期" name="nucleicTestDate">
            <a-date-picker v-model:value="formData.nucleicTestDate" placeholder="选择检测日期" style="width: 100%" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="核酸结果" name="nucleicTestResult">
            <a-select v-model:value="formData.nucleicTestResult" placeholder="核酸检测结果">
              <a-select-option value="negative">阴性</a-select-option>
              <a-select-option value="positive">阳性</a-select-option>
              <a-select-option value="pending">待出结果</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="检查地点" name="checkLocation">
        <a-input v-model:value="formData.checkLocation" placeholder="请输入检查地点" />
      </a-form-item>

      <a-form-item label="人员类型" name="isEmployee">
        <a-radio-group v-model:value="formData.isEmployee">
          <a-radio :value="true">员工</a-radio>
          <a-radio :value="false">访客</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="旅行史">
        <a-select v-model:value="formData.travelHistory" mode="tags" placeholder="请输入近14天旅行史" style="width: 100%" />
      </a-form-item>

      <a-form-item label="症状">
        <a-checkbox-group v-model:value="formData.symptoms">
          <a-checkbox value="fever">发热</a-checkbox>
          <a-checkbox value="cough">咳嗽</a-checkbox>
          <a-checkbox value="fatigue">乏力</a-checkbox>
          <a-checkbox value="sore_throat">咽痛</a-checkbox>
          <a-checkbox value="headache">头痛</a-checkbox>
          <a-checkbox value="diarrhea">腹泻</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import dayjs, { Dayjs } from 'dayjs';
  import { healthCheck } from '/@/api/operations/epidemic';

  interface Props {
    visible: boolean;
  }

  const props = defineProps<Props>();

  const emit = defineEmits<{
    'update:visible': [value: boolean];
    success: [];
  }>();

  const formRef = ref();
  const formData = reactive({
    personId: '',
    name: '',
    phone: '',
    department: '',
    temperature: 36.5,
    healthCode: 'green',
    vaccineStatus: 'none',
    nucleicTestDate: null as Dayjs | null,
    nucleicTestResult: '',
    checkLocation: '',
    isEmployee: true,
    travelHistory: [] as string[],
    symptoms: [] as string[],
  });

  const rules = {
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
    personId: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
    temperature: [{ required: true, message: '请输入体温', trigger: 'blur' }],
    healthCode: [{ required: true, message: '请选择健康码状态', trigger: 'change' }],
    vaccineStatus: [{ required: true, message: '请选择疫苗状态', trigger: 'change' }],
    checkLocation: [{ required: true, message: '请输入检查地点', trigger: 'blur' }],
  };

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const handleSubmit = async () => {
    try {
      await formRef.value.validate();

      await healthCheck({
        personId: formData.personId,
        name: formData.name,
        phone: formData.phone,
        department: formData.department,
        temperature: formData.temperature,
        healthCode: formData.healthCode as any,
        vaccineStatus: formData.vaccineStatus as any,
        nucleicTestDate: formData.nucleicTestDate?.format('YYYY-MM-DD'),
        nucleicTestResult: formData.nucleicTestResult as any,
        checkLocation: formData.checkLocation,
        isEmployee: formData.isEmployee,
        travelHistory: formData.travelHistory,
        symptoms: formData.symptoms,
      });

      message.success('健康登记成功');
      emit('success');
    } catch (error) {
      message.error('登记失败');
    }
  };

  const handleCancel = () => {
    visible.value = false;
  };
</script>
