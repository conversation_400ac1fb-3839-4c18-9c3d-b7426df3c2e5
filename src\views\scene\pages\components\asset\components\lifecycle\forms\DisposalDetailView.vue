<template>
  <div class="p-[1vw] space-y-[1vw]">
    <!-- 报废基本信息 -->
    <div class="bg-red-500/10 border border-red-500/20 rounded p-[0.8vw]">
      <h3 class="text-[0.8vw] text-red-400 font-semibold mb-[0.6vw] flex items-center">
        <span class="mr-[0.4vw]">🗑️</span>
        报废单号：{{ disposal?.disposalCode }}
      </h3>
      <div class="grid grid-cols-3 gap-[0.8vw] text-[0.6vw]">
        <div
          ><span class="text-gray-400">资产名称：</span><span class="text-white">{{ disposal?.assetName }}</span></div
        >
        <div
          ><span class="text-gray-400">资产编号：</span><span class="text-white">{{ disposal?.assetCode }}</span></div
        >
        <div
          ><span class="text-gray-400">当前状态：</span>
          <span
            :class="[
              disposal?.status === 'disposed'
                ? 'text-gray-400'
                : disposal?.status === 'approved'
                  ? 'text-green-400'
                  : disposal?.status === 'pending'
                    ? 'text-yellow-400'
                    : 'text-red-400',
            ]"
            >{{ getStatusText(disposal?.status) }}</span
          >
        </div>
      </div>
    </div>

    <!-- 报废详情 -->
    <div class="grid grid-cols-2 gap-[1vw]">
      <!-- 基本信息 -->
      <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
        <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">基本信息</h4>
        <div class="space-y-[0.4vw] text-[0.6vw]">
          <div class="flex justify-between">
            <span class="text-gray-400">申请人：</span>
            <span class="text-white">{{ disposal?.applicant }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">申请时间：</span>
            <span class="text-white">{{ disposal?.applicationDate }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">报废原因：</span>
            <span class="text-white">{{ getReasonText(disposal?.reason) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">处置方式：</span>
            <span class="text-white">{{ disposal?.disposalMethod || '回收处理' }}</span>
          </div>
        </div>
      </div>

      <!-- 审批信息 -->
      <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
        <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">审批信息</h4>
        <div class="space-y-[0.4vw] text-[0.6vw]">
          <div class="flex justify-between">
            <span class="text-gray-400">审批时间：</span>
            <span class="text-white">{{ disposal?.approveDate || '待审批' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">审批人：</span>
            <span class="text-white">{{ disposal?.approver || '待分配' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">执行时间：</span>
            <span class="text-white">{{ disposal?.executeDate || '未执行' }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">执行人：</span>
            <span class="text-white">{{ disposal?.executor || '待分配' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 报废说明 -->
    <div class="bg-black/20 rounded border border-white/10 p-[0.8vw]">
      <h4 class="text-[0.7vw] text-white font-semibold mb-[0.6vw]">报废说明</h4>
      <div class="text-[0.6vw] text-gray-300">
        {{ disposal?.description || '设备已达到使用年限，性能严重下降，无维修价值，申请报废处理。' }}
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end space-x-[0.6vw] pt-[0.6vw] border-t border-white/10">
      <button
        v-if="disposal?.status === 'pending'"
        class="px-[0.8vw] py-[0.4vw] bg-green-500 text-white text-[0.6vw] rounded hover:bg-green-600 transition-colors"
        @click="approveDisposal"
      >
        审批报废
      </button>
      <button
        v-if="disposal?.status === 'approved'"
        class="px-[0.8vw] py-[0.4vw] bg-red-500 text-white text-[0.6vw] rounded hover:bg-red-600 transition-colors"
        @click="executeDisposal"
      >
        执行报废
      </button>
      <button
        class="px-[0.8vw] py-[0.4vw] bg-orange-500 text-white text-[0.6vw] rounded hover:bg-orange-600 transition-colors"
        @click="printDisposal"
      >
        打印单据
      </button>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    disposal: { type: Object, default: null },
  });

  const getStatusText = (status) => {
    const statusMap = {
      pending: '待审批',
      approved: '已批准',
      rejected: '已拒绝',
      disposed: '已处置',
    };
    return statusMap[status] || status;
  };

  const getReasonText = (reason) => {
    const reasonMap = {
      damaged: '设备损坏',
      obsolete: '技术淘汰',
      expired: '超期使用',
      upgrade: '设备升级',
    };
    return reasonMap[reason] || reason;
  };

  const approveDisposal = () => {
    alert('跳转到审批页面');
  };

  const executeDisposal = () => {
    alert('执行报废操作');
  };

  const printDisposal = () => {
    alert('正在生成报废单据...');
  };
</script>
