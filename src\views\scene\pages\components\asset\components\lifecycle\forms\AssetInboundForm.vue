<template>
  <div class="p-[1vw] space-y-[1vw]">
    <div class="grid grid-cols-2 gap-[1vw]">
      <!-- 基本信息 -->
      <div class="space-y-[0.8vw]">
        <h3 class="text-[0.8vw] text-white font-semibold mb-[0.6vw]">基本信息</h3>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">资产名称 *</label>
          <input
            v-model="formData.name"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入资产名称"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">资产类型 *</label>
          <select
            v-model="formData.type"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          >
            <option value="">请选择资产类型</option>
            <option value="server">服务器</option>
            <option value="network">网络设备</option>
            <option value="storage">存储设备</option>
            <option value="power">电力设备</option>
            <option value="other">其他设备</option>
          </select>
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">型号规格</label>
          <input
            v-model="formData.model"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入型号规格"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">序列号</label>
          <input
            v-model="formData.serialNumber"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入序列号"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">制造商</label>
          <input
            v-model="formData.manufacturer"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入制造商"
          />
        </div>
      </div>

      <!-- 采购信息 -->
      <div class="space-y-[0.8vw]">
        <h3 class="text-[0.8vw] text-white font-semibold mb-[0.6vw]">采购信息</h3>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">供应商 *</label>
          <input
            v-model="formData.supplier"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入供应商"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">采购价格</label>
          <input
            v-model.number="formData.price"
            type="number"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入采购价格"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">采购日期</label>
          <input
            v-model="formData.purchaseDate"
            type="date"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">入库日期 *</label>
          <input
            v-model="formData.inboundDate"
            type="date"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
          />
        </div>

        <div>
          <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">保修期（月）</label>
          <input
            v-model.number="formData.warrantyPeriod"
            type="number"
            class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
            placeholder="请输入保修期"
          />
        </div>
      </div>
    </div>

    <!-- 详细描述 -->
    <div>
      <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">备注说明</label>
      <textarea
        v-model="formData.description"
        rows="4"
        class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400 resize-none"
        placeholder="请输入备注说明"
      ></textarea>
    </div>

    <!-- 附件上传 -->
    <div>
      <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">相关附件</label>
      <div class="border-2 border-dashed border-white/20 rounded p-[1vw] text-center">
        <div class="text-[0.6vw] text-gray-400 mb-[0.4vw]"> 点击或拖拽文件到此区域上传 </div>
        <div class="text-[0.5vw] text-gray-500"> 支持 PDF、DOC、XLS、JPG、PNG 格式，单个文件不超过 10MB </div>
        <input type="file" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png" class="hidden" @change="handleFileUpload" />
      </div>

      <!-- 已上传文件列表 -->
      <div v-if="uploadedFiles.length > 0" class="mt-[0.6vw] space-y-[0.3vw]">
        <div v-for="(file, index) in uploadedFiles" :key="index" class="flex items-center justify-between bg-black/20 rounded px-[0.6vw] py-[0.3vw]">
          <span class="text-[0.6vw] text-white">{{ file.name }}</span>
          <button class="text-red-400 hover:text-red-300 text-[0.5vw]" @click="removeFile(index)"> 删除 </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue';

  const props = defineProps({
    formData: {
      type: Object,
      default: () => ({}),
    },
  });

  const emit = defineEmits(['update:formData']);

  // 表单数据
  const formData = ref({
    name: '',
    type: '',
    model: '',
    serialNumber: '',
    manufacturer: '',
    supplier: '',
    price: null,
    purchaseDate: '',
    inboundDate: new Date().toISOString().split('T')[0],
    warrantyPeriod: 12,
    description: '',
    ...props.formData,
  });

  // 上传文件列表
  const uploadedFiles = ref([]);

  // 监听表单数据变化
  watch(
    formData,
    (newValue) => {
      emit('update:formData', newValue);
    },
    { deep: true }
  );

  // 文件上传处理
  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    uploadedFiles.value.push(...files);
    formData.value.attachments = uploadedFiles.value;
  };

  // 删除文件
  const removeFile = (index) => {
    uploadedFiles.value.splice(index, 1);
    formData.value.attachments = uploadedFiles.value;
  };
</script>
