<template>
  <div class="device-preview p-4">
    <!-- 设备信息 -->
    <div class="device-info mb-3 p-4 bg-white rounded-lg shadow-sm border border-gray-100">
      <div class="flex items-center mb-2">
        <i class="i-ant-design:database-outlined text-base text-primary mr-1.5"></i>
        <h3 class="text-base font-medium">设备信息</h3>
      </div>
      <div class="grid grid-cols-3 gap-3">
        <div class="device-info-item">
          <span class="text-gray-500 text-xs">模型名称</span>
          <span class="font-medium text-gray-900 mt-0.5 text-sm">{{ device.modelName }}</span>
        </div>
        <div class="device-info-item">
          <span class="text-gray-500 text-xs">楼层</span>
          <span class="font-medium text-gray-900 mt-0.5 text-sm">{{ device.floor }}</span>
        </div>
        <div class="device-info-item">
          <span class="text-gray-500 text-xs">编号</span>
          <span class="font-medium text-gray-900 mt-0.5 text-sm">{{ device.deviceNumber }}</span>
        </div>
        <div class="device-info-item">
          <span class="text-gray-500 text-xs">类型</span>
          <span class="font-medium text-gray-900 mt-0.5 text-sm">{{ device.deviceType }}</span>
        </div>
        <div class="device-info-item">
          <span class="text-gray-500 text-xs">状态</span>
          <div class="flex items-center mt-0.5">
            <span class="w-1.5 h-1.5 rounded-full mr-1" :class="device.status === '正常' ? 'bg-green-500' : 'bg-red-500'"></span>
            <span class="font-medium text-sm" :class="device.status === '正常' ? 'text-green-600' : 'text-red-600'">
              {{ device.status }}
            </span>
          </div>
        </div>
        <div class="device-info-item">
          <span class="text-gray-500 text-xs">更新时间</span>
          <span class="font-medium text-gray-900 mt-0.5 text-sm">{{ device.updateTime }}</span>
        </div>
      </div>
    </div>

    <!-- 3D模型预览 -->
    <div class="model-preview flex-1">
      <div class="flex items-center mb-2">
        <i class="i-ant-design:experiment-outlined text-base text-primary mr-1.5"></i>
        <h3 class="text-base font-medium">3D模型预览</h3>
      </div>
      <div class="model-container bg-gray-50 rounded-lg border border-gray-100 shadow-sm overflow-hidden" ref="modelContainer">
        <div v-if="loading" class="flex items-center justify-center h-full">
          <div class="flex flex-col items-center">
            <div class="i-ant-design:loading-3-quarters-outlined text-xl text-primary animate-spin mb-1"></div>
            <span class="text-gray-500 text-sm">模型加载中...</span>
          </div>
        </div>
        <div v-if="error" class="flex items-center justify-center h-full">
          <div class="flex flex-col items-center">
            <div class="i-ant-design:warning-outlined text-xl text-red-500 mb-1"></div>
            <span class="text-red-500 text-sm">{{ error }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, watch } from 'vue';
  import * as THREE from 'three';
  import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
  import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js';
  import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader.js';
  import { MeshoptDecoder } from 'three/examples/jsm/libs/meshopt_decoder.module.js';
  import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
  import { useGlobSetting } from '/@/hooks/setting';
  import { debounce } from 'lodash-es';

  interface DeviceProps {
    device: {
      id: number;
      modelName: string;
      floor: string;
      deviceNumber: string;
      deviceType: string;
      modelPath: string;
      status: string;
      updateTime: string;
    };
  }

  const props = defineProps<DeviceProps>();
  const { modelUrl } = useGlobSetting();

  const modelContainer = ref<HTMLElement>();
  const loading = ref(true);
  const error = ref('');

  let scene: THREE.Scene;
  let camera: THREE.PerspectiveCamera;
  let renderer: THREE.WebGLRenderer;
  let controls: OrbitControls;
  let animationId: number;
  let dracoLoader: DRACOLoader;
  let ktx2Loader: KTX2Loader;
  let gltfLoader: GLTFLoader;
  let currentModel: THREE.Group | null = null;

  const initThreeJS = () => {
    if (!modelContainer.value) return;

    // 确保清理之前的资源
    cleanupThreeJS();

    // 创建场景
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x000000);

    // 创建相机
    const container = modelContainer.value;
    const width = container.clientWidth;
    const height = container.clientHeight || 400;

    camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    camera.position.set(5, 5, 5);

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(width, height);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    container.appendChild(renderer.domElement);

    // 创建控制器
    controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;

    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 1.2);
    scene.add(ambientLight);

    // 初始化加载器
    dracoLoader = new DRACOLoader();
    dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');

    ktx2Loader = new KTX2Loader();
    ktx2Loader.setTranscoderPath('https://cdn.jsdelivr.net/npm/three@latest/examples/jsm/libs/basis/');
    ktx2Loader.detectSupport(renderer);

    gltfLoader = new GLTFLoader();
    gltfLoader.setDRACOLoader(dracoLoader);
    gltfLoader.setKTX2Loader(ktx2Loader);
    gltfLoader.setMeshoptDecoder(MeshoptDecoder);
  };

  const cleanupThreeJS = () => {
    if (animationId) {
      cancelAnimationFrame(animationId);
      animationId = 0;
    }

    // 清理模型资源
    if (currentModel) {
      scene?.remove(currentModel);
      currentModel = null;
    }

    // 清理场景中的其他对象
    if (scene) {
      scene.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          if (object.geometry) {
            object.geometry.dispose();
          }

          if (Array.isArray(object.material)) {
            object.material.forEach((material) => material.dispose());
          } else if (object.material) {
            object.material.dispose();
          }
        }
      });
    }

    // 清理加载器
    if (dracoLoader) {
      dracoLoader.dispose();
      dracoLoader = null;
    }

    if (ktx2Loader) {
      ktx2Loader.dispose();
      ktx2Loader = null;
    }

    // 清理渲染器
    if (renderer) {
      if (modelContainer.value && modelContainer.value.contains(renderer.domElement)) {
        modelContainer.value.removeChild(renderer.domElement);
      }
      renderer.dispose();
      renderer = null;
    }

    // 清理控制器
    if (controls) {
      controls.dispose();
      controls = null;
    }

    // 清理场景
    if (scene) {
      while (scene.children.length > 0) {
        const object = scene.children[0];
        scene.remove(object);
      }
      scene = null;
    }
  };

  const loadModel = async () => {
    if (!props.device.modelPath) {
      error.value = '模型路径不存在';
      loading.value = false;
      return;
    }

    try {
      loading.value = true;
      error.value = '';

      // 清理旧模型
      if (currentModel) {
        scene.remove(currentModel);
        disposeObject(currentModel);
        currentModel = null;
      }

      // 构建完整的模型路径
      const fullModelPath = `${modelUrl}${props.device.modelPath}`;
      console.log('加载模型路径:', fullModelPath);

      // 加载模型
      const gltf = await new Promise<any>((resolve, reject) => {
        gltfLoader.load(fullModelPath, resolve, undefined, reject);
      });

      // 添加模型到场景
      const model = gltf.scene;
      currentModel = model;

      // 计算模型边界框并调整位置
      const box = new THREE.Box3().setFromObject(model);
      const center = box.getCenter(new THREE.Vector3());
      model.position.sub(center);

      // 调整模型大小
      const size = box.getSize(new THREE.Vector3());
      const maxDim = Math.max(size.x, size.y, size.z);
      const scale = 3 / maxDim;
      model.scale.setScalar(scale);

      // 启用阴影
      model.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh) {
          child.castShadow = true;
          child.receiveShadow = true;
        }
      });

      scene.add(model);
      loading.value = false;

      // 开始渲染循环（如果还未开始）
      if (!animationId) {
        animate();
      }
    } catch (err) {
      console.error('模型加载失败:', err);
      error.value = '模型加载失败';
      loading.value = false;
    }
  };

  const disposeObject = (obj: THREE.Object3D) => {
    if (!obj) return;

    obj.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (child.geometry) {
          child.geometry.dispose();
        }

        if (Array.isArray(child.material)) {
          child.material.forEach((material) => material.dispose());
        } else if (child.material) {
          child.material.dispose();
        }
      }
    });
  };

  const animate = () => {
    if (!scene || !camera || !renderer || !controls) return;

    animationId = requestAnimationFrame(animate);
    controls.update();
    renderer.render(scene, camera);
  };

  const handleResize = debounce(() => {
    if (!modelContainer.value || !camera || !renderer) return;

    const container = modelContainer.value;
    const width = container.clientWidth;
    const height = container.clientHeight || 400;

    camera.aspect = width / height;
    camera.updateProjectionMatrix();
    renderer.setSize(width, height);
  }, 200);

  // 监听设备变化，重新加载模型
  watch(
    () => props.device,
    () => {
      if (scene) {
        // 重新加载模型
        loadModel();
      }
    },
    { immediate: false, deep: true }
  );

  onMounted(() => {
    initThreeJS();
    loadModel();
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    cleanupThreeJS();
    window.removeEventListener('resize', handleResize);
  });
</script>

<style scoped>
  .device-preview {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .device-info-item {
    display: flex;
    flex-direction: column;
    padding: 4px 8px;
    background-color: #f8fafc;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .device-info-item:hover {
    background-color: #f1f5f9;
    transform: translateY(-1px);
  }

  .model-container {
    height: 360px;
    width: 100%;
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
  }

  .model-container:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .model-container canvas {
    display: block;
    width: 100%;
    height: 100%;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }
</style>
