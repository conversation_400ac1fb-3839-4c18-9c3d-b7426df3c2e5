import { ThunderboltOutlined, DashboardOutlined, Bar<PERSON><PERSON>Outlined, AppstoreOutlined, ScaleOutlined } from '@ant-design/icons-vue';
<template>
  <div class="h-full flex">
    <!-- 热力图类型选择按钮 -->
    <div class="flex-1 flex flex-wrap gap-[0.4vw]">
      <button
        v-for="(item, index) in heatmapTypes"
        :key="index"
        class="h-[2.2vw] px-[0.8vw] bg-[rgba(23,43,77,0.8)] border rounded-[0.3vw] cursor-pointer transition-all duration-200 flex items-center justify-center relative"
        :class="[
          activeType === item.value
            ? 'border-[rgba(36,108,249,0.8)] shadow-[0_0_8px_rgba(36,108,249,0.3)] bg-[rgba(36,108,249,0.2)]'
            : 'border-[rgba(36,108,249,0.3)] hover:border-[rgba(36,108,249,0.5)] hover:bg-[rgba(36,108,249,0.1)]',
        ]"
        @click="toggleHeatmap(item.value)"
      >
        <!-- 按钮文字 -->
        <span class="text-[0.65vw] text-white/90 font-medium">{{ item.name }}</span>
        <!-- 加载动画 -->
        <div
          v-if="applying && activeType === item.value"
          class="absolute -right-[0.15vw] -top-[0.15vw] w-[0.5vw] h-[0.5vw] border-2 border-t-transparent border-white/80 rounded-full animate-spin"
        ></div>
      </button>
    </div>
    <TechMessage ref="messageRef" />
  </div>
</template>

<script setup lang="ts">
  import { ref, onUnmounted, onMounted } from 'vue';
  import TechMessage from '/@/views/scene/components/TechMessage.vue';
  import { HeatmapManager } from '/@/views/scene/lib/effects/HeatmapManager';
  import { useGlobalThreeStore } from '/@/views/scene/store/globalThreeStore';
  import { HeatmapJSManager } from '/@/views/scene/lib/effects/HeatmapJSManager';
  import { GlobalHeatmapStateManager, HeatmapFunctionType } from '/@/views/scene/lib/effects/GlobalHeatmapStateManager';

  const activeType = ref<HeatmapFunctionType>('none');
  const applying = ref<boolean>(false);
  const isEffectActive = ref<boolean>(false);
  const globalThreeStore = useGlobalThreeStore();
  const messageRef = ref<InstanceType<typeof TechMessage> | null>(null);
  const globalHeatmapState = GlobalHeatmapStateManager.getInstance();

  interface HeatmapType {
    name: string;
    value: string;
  }

  const heatmapTypes: HeatmapType[] = [
    {
      name: '能耗分布',
      value: 'power',
    },
    {
      name: '温度分布',
      value: 'temperature',
    },
    {
      name: '设备负载',
      value: 'load',
    },
    {
      name: '机柜占用量',
      value: 'cabinet_occupancy',
    },
    {
      name: '设备重量',
      value: 'device_weight',
    },
  ];

  // 映射热力图类型到效果类型
  const effectTypeMap: Record<string, HeatmapFunctionType> = {
    power: 'power',
    temperature: 'temperature',
    load: 'load',
    cabinet_occupancy: 'cabinet_occupancy',
    device_weight: 'device_weight',
  };

  // 检查浏览器性能
  const checkBrowserPerformance = (): boolean => {
    try {
      // 检查WebGL支持
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) {
        messageRef.value?.showMessage('您的浏览器不支持WebGL，热力图功能可能无法正常工作', 'warning', {});
        return false;
      }

      // 检查内存使用情况
      if (performance && (performance as any).memory) {
        const memory = (performance as any).memory;
        if (memory && memory.jsHeapSizeLimit && memory.usedJSHeapSize) {
          const usedRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
          if (usedRatio > 0.8) {
            messageRef.value?.showMessage('系统内存使用率较高，热力图可能导致性能下降', 'warning', {});
            return true; // 仍然允许继续，但发出警告
          }
        }
      }

      return true;
    } catch (error) {
      console.error('检查浏览器性能时出错:', error);
      return true; // 出错时仍然允许继续
    }
  };

  // 切换热力图效果 - 使用全局状态管理器
  const toggleHeatmap = async (type: string): Promise<void> => {
    console.log(`[HeatmapPanel] 请求切换热力图效果: ${type}`);

    // 检查系统是否准备就绪
    if (!globalThreeStore.canUserInteract) {
      messageRef.value?.showMessage('系统正在加载中，请稍候...', 'info', {});
      return;
    }

    // 防止重复点击
    if (applying.value || globalHeatmapState.isInSwitchingState()) {
      messageRef.value?.showMessage('正在处理上一个请求，请稍候...', 'info', {});
      return;
    }

    try {
      applying.value = true;

      // 将字符串类型转换为HeatmapFunctionType
      const functionType = effectTypeMap[type] || 'none';

      // 如果是温度分布，先检查浏览器性能
      if (functionType === 'temperature' && !globalHeatmapState.isFunctionActive('temperature')) {
        if (!checkBrowserPerformance()) {
          messageRef.value?.showMessage('您的浏览器可能不支持此功能，请尝试更新浏览器', 'error', {});
          return;
        }
      }

      // 如果点击当前激活的类型，则清除效果
      if (globalHeatmapState.isFunctionActive(functionType)) {
        // 显示正在处理的消息
        messageRef.value?.showMessage('正在清除热力图效果...', 'info', {});

        // 清除所有热力图效果
        await globalHeatmapState.clearAllFunctions();

        // 更新UI状态
        isEffectActive.value = false;
        activeType.value = 'none';

        messageRef.value?.showMessage('已清除热力图效果', 'success', {});
        console.log(`[HeatmapPanel] 热力图效果已清除`);
      } else {
        // 显示正在处理的消息
        const effectName =
          type === 'temperature'
            ? '温度分布'
            : type === 'power'
              ? '能耗分布'
              : type === 'load'
                ? '设备负载'
                : type === 'cabinet_occupancy'
                  ? '机柜占用量'
                  : type === 'device_weight'
                    ? '设备重量'
                    : '热力图';

        messageRef.value?.showMessage(`正在应用${effectName}效果...`, 'info', {});

        try {
          // 使用全局状态管理器激活功能
          const success = await globalHeatmapState.activateFunction(functionType);

          if (success) {
            // 更新UI状态
            isEffectActive.value = true;
            activeType.value = functionType;

            messageRef.value?.showMessage(`${effectName}效果已应用`, 'success', {});
            console.log(`[HeatmapPanel] ${effectName}效果已应用成功`);
          } else {
            throw new Error(`激活${effectName}功能失败`);
          }
        } catch (error) {
          // 应用效果失败，恢复状态
          isEffectActive.value = false;
          activeType.value = 'none';

          // 显示更具体的错误信息
          const errorMessage = error instanceof Error ? error.message : '未知错误';
          messageRef.value?.showMessage(`应用热力图失败: ${errorMessage}`, 'error', {});
          console.error(`[HeatmapPanel] 应用热力图失败:`, error);

          // 确保清除所有可能的残留效果
          try {
            await globalHeatmapState.clearAllFunctions();
          } catch (cleanupError) {
            console.error('[HeatmapPanel] 清理残留效果时出错:', cleanupError);
          }

          throw error; // 重新抛出错误以便记录到控制台
        }
      }
    } catch (error) {
      console.error('[HeatmapPanel] 切换热力图时发生错误:', error);
      // 错误消息已在内部处理，这里不再显示
    } finally {
      applying.value = false;
    }
  };

  // 组件挂载时初始化
  onMounted(() => {
    console.log('[HeatmapPanel] 组件已挂载');

    // 同步组件状态与全局状态
    const currentFunction = globalHeatmapState.getActiveFunction();
    if (currentFunction !== 'none') {
      activeType.value = currentFunction;
      isEffectActive.value = true;
      console.log(`[HeatmapPanel] 检测到活动的热力图功能: ${currentFunction}`);
    }
  });

  // 确保在组件卸载时清理所有资源
  onUnmounted(async () => {
    console.log('[HeatmapPanel] 组件正在卸载');

    try {
      // 停止任何正在进行的操作
      applying.value = false;

      // 清理热力图效果
      if (globalHeatmapState.isAnyFunctionActive()) {
        console.log('[HeatmapPanel] 组件卸载，清理所有热力图效果');

        try {
          await globalHeatmapState.clearAllFunctions();
          console.log('[HeatmapPanel] 所有热力图效果已清理');
        } catch (clearError) {
          console.error('[HeatmapPanel] 清理热力图效果失败:', clearError);

          // 尝试单独清理每个功能
          try {
            console.log('[HeatmapPanel] 尝试单独清理每个热力图功能');

            // 清理温度分布功能
            const tempManager = HeatmapJSManager.getInstance();
            if (tempManager && tempManager.getStatus().active) {
              await tempManager.hide();
            }

            // 清理其他热力图功能
            const heatManager = HeatmapManager.getInstance();
            if (heatManager) {
              await heatManager.clearHeatmapAsync();
            }
          } catch (fallbackError) {
            console.error('[HeatmapPanel] 单独清理热力图功能也失败:', fallbackError);
          }
        }
      }

      // 重置状态
      isEffectActive.value = false;
      activeType.value = 'none';
    } catch (error) {
      console.error('[HeatmapPanel] 组件卸载时清理热力图资源出错:', error);
    }
  });
</script>
