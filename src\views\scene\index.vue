<template>
  <div class="relative w-full h-full overflow-hidden select-none">
    <!-- 全屏加载遮罩 -->
    <div v-if="!isAppReady" class="fixed inset-0 bg-black/85 z-1000 flex items-center justify-center pointer-events-auto select-none">
      <div class="text-center text-white flex flex-col items-center gap-5">
        <SceneLoading :value="overallLoadingProgress" />
        <div class="text-[1.2rem] tracking-[1px] flex flex-col items-center gap-2.5">
          <span>加载中... {{ overallLoadingProgress }}%</span>
          <div class="text-[0.9rem] text-[#aaa] flex gap-[15px]">
            <span>主模型: {{ loadingProgress }}%</span>
            <span>预加载模型: {{ silentLoadingProgress }}%</span>
            <span>{{ loadedModelsCount }}/{{ totalModelsToLoad }} 模型</span>
          </div>
          <!-- 调试信息 -->
          <div v-if="showDebugInfo" class="mt-2.5 text-[0.8rem] text-[#ff9800] text-left bg-black/50 p-2.5 rounded-[5px] max-w-[400px]">
            <div>正在等待模型加载完成...</div>
            <div>模型加载详情: {{ loadedModelsCount }}/{{ totalModelsToLoad }}</div>
            <div>状态: {{ loadingComplete ? '加载完成' : '加载中' }}, {{ appReady ? '准备就绪' : '未准备就绪' }}</div>
          </div>
        </div>

        <!-- 强制继续按钮 -->
        <div v-if="showForceLoadButton" class="mt-5 flex flex-col items-center p-2.5 rounded-[5px] bg-black/30">
          <a-button type="primary" @click="forceCompleteLoading">确认强制进入</a-button>
          <div class="text-xs text-gray-400 mt-2">
            <p>部分模型未能完全加载，强制进入可能导致场景不完整</p>
            <p>建议等待所有模型加载完成，或检查网络连接</p>
          </div>
        </div>
      </div>
    </div>

    <div
      ref="container"
      class="border border-[#ccc] relative z-0 transition-all duration-300"
      :class="pptDemonstrationActive ? 'absolute top-0 left-0 w-1/2 h-full' : 'w-full h-full'"
    ></div>

    <!-- 动态组件 - 使用新的显示逻辑 -->
    <component :is="currentDashboard" v-if="isAppReady && !store.sceneTransitionState.isTransitioning" />

    <!-- 场景转换遮罩 -->
    <SceneTransitionMask />

    <!-- 楼层切换效果 -->
    <FloorTransitionEffect />

    <!-- 调试面板 - 仅在应用准备好后显示 -->
    <DebugPanel ref="debugPanelRef" v-model:visible="debugPanelVisible" v-model:sceneType="currentSceneType" v-if="isDebugAllowed" />

    <!-- 转换测试面板 - 仅在开发模式下显示 -->
    <TransitionTestPanel v-if="showTransitionTestPanel" @close="showTransitionTestPanel = false" />

    <!-- 开发模式按钮 - 仅在开发环境显示 -->
    <div v-if="isDevelopment && isAppReady && isDebugAllowed" class="fixed top-4 left-4 z-[999] flex gap-2">
      <a-button @click="showTransitionTestPanel = !showTransitionTestPanel" type="primary" size="small">
        {{ showTransitionTestPanel ? '隐藏' : '显示' }}转换测试
      </a-button>
    </div>

    <a-button @click="handleEnableLoading" v-if="!loadingEnabled">加载模型</a-button>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onBeforeUnmount, onActivated, onDeactivated, provide, inject, watch, nextTick } from 'vue';
  import { storeToRefs } from 'pinia';
  import { useGlobalThreeStore } from './store/globalThreeStore';
  import SceneDashboard from './pages/dashboard/index.vue';
  import FeatureDash from './pages/featureDash/index.vue';
  import SceneLoading from './components/SceneLoading.vue';
  import DebugPanel from './components/DebugPanel.vue';
  import SceneTransitionMask from './components/SceneTransitionMask.vue';
  import FloorTransitionEffect from './components/FloorTransitionEffect.vue';
  import TransitionTestPanel from './components/TransitionTestPanel.vue';
  import { buildingData } from '../../data/buildingData';
  import { SceneController } from './lib/SceneController';
  import { SceneManager } from './lib/SceneManager';
  import { DebugGUIManager } from './lib/debug/DebugGUIManager';
  import { RenderingPipeline } from './lib/RenderingPipeline';
  import { CameraController } from './lib/CameraController';
  import type { DefineComponent } from 'vue';
  import './style/index.css';

  // 配置接口定义
  interface SceneConfig {
    showFPS: boolean;
    fontAwesome: {
      enabled: boolean;
      version: string;
    };
  }

  // 配置项
  const config: SceneConfig = {
    showFPS: true,
    fontAwesome: {
      enabled: true,
      version: '5.15.4',
    },
  };

  // ref 类型定义
  const container = ref<HTMLElement | null>(null);
  const fps = ref<number>(0);
  const showForceLoadButton = ref<boolean>(false);
  const loadingEnabled = ref<boolean>(false);
  const showDebugInfo = ref<boolean>(false);
  const debugPanelRef = ref<InstanceType<typeof DebugPanel> | null>(null);

  // 调试面板相关
  const debugPanelVisible = ref<boolean>(import.meta.env.VITE_ENABLE_DEBUG === 'true');
  const isDebugAllowed = computed(() => import.meta.env.VITE_ENABLE_DEBUG === 'true');
  const currentSceneType = ref<'interior' | 'exterior'>('exterior');
  // 开发模式检测 - 确保生产环境下不显示测试功能
  const isDevelopment = process.env.NODE_ENV === 'development' && import.meta.env.MODE !== 'production';

  // 转换测试面板
  const showTransitionTestPanel = ref(false);

  // 场景控制器和FPS间隔变量定义
  let sceneController: SceneController | null = null;
  let fpsInterval: number | null = null;

  // FontAwesome按需加载
  if (config.fontAwesome.enabled) {
    const fontAwesomeLink = (document.querySelector('link[href*="font-awesome"]') || document.createElement('link')) as HTMLLinkElement;
    if (!fontAwesomeLink.href) {
      fontAwesomeLink.rel = 'stylesheet';
      fontAwesomeLink.href = `https://cdnjs.cloudflare.com/ajax/libs/font-awesome/${config.fontAwesome.version}/css/all.min.css`;
      document.head.appendChild(fontAwesomeLink);
    }
  }

  // FPS监测函数
  const updateFPS = (): void => {
    let frameCount = 0;
    let lastTime = performance.now();

    fpsInterval = window.setInterval(() => {
      const now = performance.now();
      const delta = now - lastTime;
      fps.value = Math.round((frameCount * 1000) / delta);
      frameCount = 0;
      lastTime = now;
    }, 1000);

    const countFrame = (): void => {
      frameCount++;
      requestAnimationFrame(countFrame);
    };
    requestAnimationFrame(countFrame);
  };

  // Store相关
  const store = useGlobalThreeStore();
  const { loadingProgress, currentView, loadingComplete, appReady, silentLoadingProgress, totalModelsToLoad, loadedModelsCount } = storeToRefs(store);

  // computed属性
  const isAppReady = computed<boolean>(() => loadingComplete.value && appReady.value);

  const overallLoadingProgress = computed<number>(() => {
    if (loadingProgress.value > 0 && silentLoadingProgress.value > 0) {
      return Math.floor((loadingProgress.value + silentLoadingProgress.value) / 2);
    }
    return Math.max(loadingProgress.value, store.loadingPercentage);
  });

  const currentDashboard = computed(() => (currentView.value === 1 ? SceneDashboard : FeatureDash));

  const pptDemonstrationActive = computed<boolean>(() => store.pptDemonstration.isActive);

  // 方法定义
  const handleEnableLoading = (): void => {
    store.setEnableModelLoading(true);
    loadingEnabled.value = true;
  };

  const forceCompleteLoading = (): void => {
    console.log('用户手动强制完成加载');
    store.forceCompleteLoading();
  };

  const handleLoadingError = (error: Error | string): void => {
    if (window.$message) {
      window.$message.error(`加载失败: ${error}`);
    } else {
      console.error('加载失败:', error);
    }
  };

  const handleViewTransitionError = (error: Error): void => {
    console.error('视图切换错误:', error);
    if (window.$message) {
      window.$message.error('视图切换失败，请重试');
    }
  };

  const safeToggleView = async (): Promise<void> => {
    try {
      await store.toggleView();
    } catch (error) {
      handleViewTransitionError(error as Error);
    }
  };

  // 切换调试面板的方法
  const toggleDebugPanel = () => {
    if (isDebugAllowed.value) {
      if (debugPanelRef.value) {
        debugPanelRef.value.togglePanel();
      }
    }
  };

  // Provide/Inject
  provide('fps', fps);
  provide('safeToggleView', safeToggleView);
  provide('isAppReady', isAppReady);
  provide('toggleDebugPanel', toggleDebugPanel);

  // 增加处理透视状态的注入转发
  const resetTransparencyFromChild = inject<() => void>('resetTransparency', () => {});
  provide('resetTransparency', resetTransparencyFromChild);

  // Watchers
  watch(overallLoadingProgress, (newProgress: number) => {
    if (newProgress >= 100 && !isAppReady.value) {
      setTimeout(() => {
        if (!isAppReady.value) {
          console.log('模型已加载完成，但未自动进入，显示手动继续按钮');
          showForceLoadButton.value = true;
        }
      }, 20000);
    }
  });

  watch(
    () => store.isViewSwitching,
    async (isSwitching: boolean) => {
      if (!isSwitching) {
        await nextTick();
        if (sceneController) {
          sceneController.forceRender?.();
        }
      }
    }
  );

  watch(isAppReady, (newValue: boolean) => {
    if (newValue) {
      console.log('[Index] 应用程序已准备好，切换到主视图');

      // 应用程序准备好后，确保加载已保存的场景配置
      if (isDevelopment && debugPanelVisible.value) {
        nextTick(() => {
          setTimeout(() => {
            try {
              const debugManager = DebugGUIManager.getInstance();
              if (debugManager) {
                // 先应用当前场景类型的配置
                debugManager.setSceneType(currentSceneType.value);
                console.log('[Index] 已应用保存的场景配置');
              }
            } catch (err) {
              console.warn('[Index] 初始化时应用场景配置失败:', err);
            }
          }, 1000);
        });
      }
    }
  });

  // 监听场景类型变化
  watch(currentSceneType, (newType) => {
    if (sceneController) {
      const sceneManager = SceneManager.getInstance();
      if (sceneManager) {
        sceneManager.switchSceneType(newType);
        console.log(`[Index] 场景类型已切换为: ${newType}`);

        // 应用保存的配置
        if (debugPanelVisible.value && isDevelopment) {
          try {
            const debugManager = DebugGUIManager.getInstance();
            debugManager.setSceneType(newType);
            console.log(`[Index] 已为${newType}场景应用保存的配置`);
          } catch (err) {
            console.warn('[Index] 应用保存的场景配置失败:', err);
          }
        }
      }
    }
  });

  watch(
    [loadingProgress, silentLoadingProgress, loadedModelsCount, totalModelsToLoad],
    ([newLoadingProgress, newSilentProgress, newLoadedCount, newTotalCount]) => {
      // console.log(`[LoadingState] 进度更新 - 主模型:${newLoadingProgress}%, 静默:${newSilentProgress}%, 计数:${newLoadedCount}/${newTotalCount}`);

      if (newSilentProgress === 100 && newLoadingProgress === 100 && newLoadedCount >= newTotalCount && !isAppReady.value) {
        // console.warn(`[LoadingState] 看起来所有模型都已加载完成，但应用尚未准备就绪`);
      }
    }
  );

  // 执行3D场景重新渲染的核心逻辑
  const performSceneResize = (isActive: boolean) => {
    try {
      // 先检查容器尺寸变化
      if (container.value) {
        const currentWidth = container.value.clientWidth;
        const currentHeight = container.value.clientHeight;
        console.log(`[Scene] 容器当前尺寸: ${currentWidth}x${currentHeight} (PPT模式: ${isActive ? '开启' : '关闭'})`);
      }

      // 获取渲染管线实例并触发resize
      const renderingPipeline = RenderingPipeline.getInstance();
      if (renderingPipeline) {
        renderingPipeline.resize();
        renderingPipeline.render();
        console.log(`[Scene] 3D场景已重新调整尺寸 (PPT模式: ${isActive ? '开启' : '关闭'})`);
      }

      // 获取相机控制器并更新宽高比
      const cameraController = CameraController.getInstance();
      if (cameraController && container.value) {
        const newWidth = container.value.clientWidth;
        const newHeight = container.value.clientHeight;
        cameraController.updateContainerSize(newWidth, newHeight);
        console.log(`[Scene] 相机已更新尺寸: ${newWidth}x${newHeight}`);
      }

      // 获取场景管理器并强制渲染
      const sceneManager = SceneManager.getInstance();
      if (sceneManager) {
        sceneManager.needsRender = true;
        sceneManager.forceRender();
        console.log('[Scene] 场景管理器已强制重新渲染');
      }
    } catch (error) {
      console.error('[Scene] PPT模式变化时重新调整3D场景尺寸出错:', error);
    }
  };

  // PPT模式变化处理函数
  const handlePPTModeChange = (event: CustomEvent) => {
    const { isActive } = event.detail;
    console.log(`[Scene] PPT模式状态变化: ${isActive ? '激活' : '退出'}`);

    if (isActive) {
      // 激活PPT模式：较短延迟，因为从全屏到半屏的动画较快
      setTimeout(() => performSceneResize(isActive), 150);
    } else {
      // 退出PPT模式：使用transitionend事件监听动画完成
      const handleTransitionEnd = (e: TransitionEvent) => {
        // 确保是容器元素的过渡事件，而不是子元素的
        if (e.target === container.value && (e.propertyName === 'width' || e.propertyName === 'transform')) {
          console.log('[Scene] CSS过渡动画完成，执行最终渲染');
          container.value?.removeEventListener('transitionend', handleTransitionEnd);

          // 动画完成后立即渲染
          performSceneResize(isActive);

          // 额外的保险渲染，确保完全更新
          setTimeout(() => {
            console.log('[Scene] 执行保险渲染，确保完全更新');
            performSceneResize(isActive);
          }, 50);
        }
      };

      // 监听过渡动画完成事件
      if (container.value) {
        container.value.addEventListener('transitionend', handleTransitionEnd);
      }

      // 先进行一次立即渲染
      setTimeout(() => performSceneResize(isActive), 50);

      // 备用方案：如果事件没有触发，使用定时器作为后备
      setTimeout(() => {
        if (container.value) {
          container.value.removeEventListener('transitionend', handleTransitionEnd);
          console.log('[Scene] 备用方案：定时器触发最终渲染');
          performSceneResize(isActive);
        }
      }, 400); // 300ms动画 + 100ms缓冲
    }
  };

  onMounted(() => {
    sceneController = SceneController.getInstance();
    if (container.value) {
      sceneController.initialize(container.value, buildingData);
    }

    // 初始化视角预设数据
    store.loadViewPresetsFromStorage();
    // 初始化PPT视角绑定数据
    store.loadPPTViewBindingsFromStorage();

    // 添加PPT模式变化事件监听
    window.addEventListener('ppt-mode-changed', handlePPTModeChange as EventListener);

    if (config.showFPS) {
      updateFPS();
    }

    if (store.loadingErrors.length > 0) {
      store.loadingErrors.forEach(handleLoadingError);
    }

    setTimeout(() => {
      if (!isAppReady.value && loadedModelsCount.value > 0) {
        // console.warn('加载时间过长（10分钟），但仍在等待所有模型加载完成');
        if (window.$message) {
          window.$message.warning('加载时间较长，请耐心等待模型加载完成或刷新页面重试');
        }
        showForceLoadButton.value = true;
      }
    }, 600000);
  });

  onActivated(() => {
    if (sceneController) {
      sceneController.resume();
    }
  });

  onDeactivated(() => {
    if (sceneController) {
      sceneController.pause();
    }
  });

  onBeforeUnmount(() => {
    // 移除PPT模式变化事件监听
    window.removeEventListener('ppt-mode-changed', handlePPTModeChange as EventListener);

    if (sceneController) {
      sceneController.dispose();
      sceneController = null;
    }

    if (fpsInterval) {
      clearInterval(fpsInterval);
      fpsInterval = null;
    }
  });

  setTimeout(() => {
    if (!isAppReady.value) {
      // console.warn('加载时间超过30秒，显示调试信息');
      showDebugInfo.value = true;
    }
  }, 30000);

  // 声明全局类型
  declare global {
    interface Window {
      $message?: {
        error: (msg: string) => void;
        warning: (msg: string) => void;
      };
    }
  }
</script>
