/**
 * deviceIdentifier.ts
 * 提供统一的设备识别功能
 *
 * 统一管理所有3D场景中的设备识别逻辑，避免在多个地方重复定义
 *
 * 职责分工：
 * - deviceIdentifier.ts: 负责3D场景中的设备识别、楼层提取等核心识别逻辑
 * - deviceNameMapping.ts: 负责设备名称的解析和翻译，复用本模块的楼层提取功能
 */
import * as THREE from 'three';
import { throttle, debounce } from 'lodash-es';

/**
 * 设备识别缓存
 */
const deviceIdentifierCache = new Map<string, boolean>();
const CACHE_MAX_SIZE = 1000; // 最大缓存条目数量
const CACHE_CLEAR_THRESHOLD = 0.9; // 当缓存达到90%时清理

/**
 * 动画帧设备识别缓存 - 专门针对动画循环中的频繁调用
 */
const _animationFrameCache = new Map<string, { result: boolean; frameId: number }>();
let _currentFrameId = 0;

/**
 * 性能统计
 */
const _performanceStats = {
  totalCalls: 0,
  cacheHits: 0,
  animationFrameHits: 0,
  lastResetTime: performance.now(),
  resetInterval: 10000, // 10秒重置一次统计
};

/**
 * 更新动画帧ID - 每帧调用一次
 */
export function updateAnimationFrameId(): void {
  _currentFrameId++;
  // 每1000帧清理一次动画帧缓存，防止内存泄漏
  if (_currentFrameId % 1000 === 0) {
    _animationFrameCache.clear();
  }
}

/**
 * 性能统计
 */
export function getPerformanceStats() {
  const now = performance.now();
  if (now - _performanceStats.lastResetTime > _performanceStats.resetInterval) {
    const stats = { ..._performanceStats };
    // 重置统计
    _performanceStats.totalCalls = 0;
    _performanceStats.cacheHits = 0;
    _performanceStats.animationFrameHits = 0;
    _performanceStats.lastResetTime = now;
    return stats;
  }
  return _performanceStats;
}

/**
 * 设备识别配置
 */
export const DeviceIdentifierConfig = {
  /**
   * 楼层设备正则表达式 - 严格匹配以下模式：
   * - 1F_、2F_、3F_、4F_、5F_
   * - F1_、F2_、F3_、F4_、F5_
   * - BF_ (水泵房)
   * 必须带下划线，避免误匹配
   */
  floorDevicePattern: /^(([1-5]F|F[1-5]|BF)_)/,

  /**
   * 设备名称正则表达式
   */
  devicePatterns: {
    deviceNumber: /device_\d+/,
    server: /server/i,
    cabinet: /cabinet/i,
    meigui: /机柜/i,
    device: /设备/i,
  },

  /**
   * 可交互设备关键词
   */
  interactiveKeywords: ['server', 'cabinet', 'rack', '机柜', '设备', 'device', 'ups', 'pdu', 'ac', 'air', 'sensor', '传感器'],
};

/**
 * 检查对象是否为楼层设备 - 优化版本，支持动画帧缓存
 * @param name 对象名称
 * @param isInAnimationLoop 是否在动画循环中调用
 * @returns 是否为楼层设备
 */
export function isFloorDevice(name: string, isInAnimationLoop: boolean = false): boolean {
  if (!name) return false;

  _performanceStats.totalCalls++;

  // 在动画循环中，优先检查动画帧缓存
  if (isInAnimationLoop) {
    const animationCacheKey = name;
    const cachedEntry = _animationFrameCache.get(animationCacheKey);
    if (cachedEntry && cachedEntry.frameId === _currentFrameId) {
      _performanceStats.animationFrameHits++;
      return cachedEntry.result;
    }
  }

  // 检查普通缓存
  if (deviceIdentifierCache.has(name)) {
    _performanceStats.cacheHits++;
    const result = deviceIdentifierCache.get(name)!;

    // 在动画循环中，将结果存入动画帧缓存
    if (isInAnimationLoop) {
      _animationFrameCache.set(name, { result, frameId: _currentFrameId });
    }

    return result;
  }

  // 执行正则检查
  const result = DeviceIdentifierConfig.floorDevicePattern.test(name);

  // 存入普通缓存，但要控制缓存大小
  if (deviceIdentifierCache.size >= CACHE_MAX_SIZE * CACHE_CLEAR_THRESHOLD) {
    // 清理最旧的一些缓存条目（简单策略：清理前25%）
    const entriesToDelete = Math.floor(deviceIdentifierCache.size * 0.25);
    const iterator = deviceIdentifierCache.keys();
    for (let i = 0; i < entriesToDelete; i++) {
      const key = iterator.next().value;
      if (key) deviceIdentifierCache.delete(key);
    }
  }

  deviceIdentifierCache.set(name, result);

  // 在动画循环中，将结果存入动画帧缓存
  if (isInAnimationLoop) {
    _animationFrameCache.set(name, { result, frameId: _currentFrameId });
  }

  // 减少日志频率 - 只在开发环境且随机抽样时记录
  if (process.env.NODE_ENV === 'development' && (result || Math.random() < 0.001)) {
    console.debug(`[DeviceIdentifier] isFloorDevice 检查: "${name}" => ${result ? '匹配' : '不匹配'}`);
  }

  return result;
}

/**
 * 检查网格对象是否为设备网格 - 优化版本
 * @param mesh 要检查的网格对象
 * @param isInAnimationLoop 是否在动画循环中调用
 * @returns 是否为设备网格
 */
export function isDeviceMesh(mesh: THREE.Mesh, isInAnimationLoop: boolean = false): boolean {
  if (!mesh) return false;

  // 检查自身或父对象名称是否匹配楼层设备模式
  return isFloorDevice(mesh.name, isInAnimationLoop) || (mesh.parent ? isFloorDevice(mesh.parent.name, isInAnimationLoop) : false);
}

/**
 * 检查是否为设备对象（包括自身和父对象检查）- 优化版本
 * 这是一个通用的设备检查函数，可以替代大部分重复的设备识别逻辑
 * @param object 3D对象
 * @param isInAnimationLoop 是否在动画循环中调用
 * @returns 是否为设备对象
 */
export function isEquipmentObject(object: THREE.Object3D, isInAnimationLoop: boolean = false): boolean {
  if (!object) return false;

  // 检查自身名称
  if (isFloorDevice(object.name, isInAnimationLoop)) return true;

  // 检查父对象名称
  if (object.parent && isFloorDevice(object.parent.name, isInAnimationLoop)) return true;

  return false;
}

/**
 * 从设备名称中提取楼层号
 * @param deviceName 设备名称
 * @returns 楼层号，如果无法提取则返回null
 */
export function extractFloorNumber(deviceName: string): string | null {
  if (!deviceName) return null;

  const match = deviceName.match(/(B?\d+)F/i); // 添加对B1F格式的支持
  return match ? match[1] : null;
}

/**
 * 检查设备是否属于指定楼层
 * @param deviceName 设备名称
 * @param floorNumber 楼层号
 * @returns 是否属于指定楼层
 */
export function isDeviceOnFloor(deviceName: string, floorNumber: string | number): boolean {
  const extractedFloor = extractFloorNumber(deviceName);
  return extractedFloor === String(floorNumber);
}

/**
 * 检查对象是否为设备组 - 优化版本
 * @param object 3D对象
 * @param isInAnimationLoop 是否在动画循环中调用
 * @returns 是否为设备组
 */
export function isDeviceGroup(object: THREE.Object3D | null, isInAnimationLoop: boolean = false): boolean {
  if (!object || !object.name) return false;
  return isFloorDevice(object.name, isInAnimationLoop);
}

/**
 * 检查对象是否为可交互设备对象 - 优化版本
 * @param object 3D对象
 * @param isInAnimationLoop 是否在动画循环中调用
 * @returns 是否为可交互设备对象
 */
export function isInteractiveDevice(object: THREE.Object3D | null, isInAnimationLoop: boolean = false): boolean {
  if (!object || !object.name) return false;

  // 严格检查是否为楼层设备（以1F、2F、3F、4F或F1、F2、F3、F4开头）
  return isFloorDevice(object.name, isInAnimationLoop);
}

/**
 * 检查对象或其父对象是否包含设备 - 高性能优化版本
 * @param object 3D对象
 * @param isInAnimationLoop 是否在动画循环中调用
 * @returns 是否为包含设备的对象
 */
export function containsDevice(object: THREE.Object3D, isInAnimationLoop: boolean = false): boolean {
  // 使用对象UUID和名称组合作为缓存键，包含更多上下文信息
  const cacheKey = `${object.uuid}_${object.name}_${object.parent?.name || 'no-parent'}`;

  // 在动画循环中，优先检查动画帧缓存
  if (isInAnimationLoop) {
    const animationCacheEntry = _animationFrameCache.get(cacheKey);
    if (animationCacheEntry && animationCacheEntry.frameId === _currentFrameId) {
      _performanceStats.animationFrameHits++;
      return animationCacheEntry.result;
    }
  }

  const cachedResult = deviceIdentifierCache.get(cacheKey);
  if (cachedResult !== undefined) {
    _performanceStats.cacheHits++;
    // 在动画循环中，将结果存入动画帧缓存
    if (isInAnimationLoop) {
      _animationFrameCache.set(cacheKey, { result: cachedResult, frameId: _currentFrameId });
    }
    return cachedResult;
  }

  // 检查对象自身名称是否匹配楼层设备模式
  const selfMatch = object instanceof THREE.Mesh && isFloorDevice(object.name, isInAnimationLoop);
  // 检查父对象名称是否匹配楼层设备模式
  const parentMatch = object instanceof THREE.Mesh && object.parent ? isFloorDevice(object.parent.name, isInAnimationLoop) : false;

  const result = selfMatch || parentMatch;

  // 存入缓存
  if (deviceIdentifierCache.size < CACHE_MAX_SIZE) {
    deviceIdentifierCache.set(cacheKey, result);
  }

  // 在动画循环中，将结果存入动画帧缓存
  if (isInAnimationLoop) {
    _animationFrameCache.set(cacheKey, { result, frameId: _currentFrameId });
  }

  // 记录识别结果，但限制频率
  if (result && process.env.NODE_ENV === 'development') {
    console.debug(
      `[DeviceIdentifier] 识别到楼层设备: ${object.name}, 父对象: ${object.parent?.name || '无'}, 匹配模式: ${selfMatch ? '自身名称' : '父对象名称'}`
    );
  }

  return result;
}

/**
 * 创建节流版本的设备识别函数，专门用于动画循环中的高频调用
 */
export const throttledContainsDevice = throttle(
  (object: THREE.Object3D) => containsDevice(object, true),
  16 // 60fps下每帧16ms
);

/**
 * 创建防抖版本的设备识别函数，用于用户交互事件
 */
export const debouncedContainsDevice = debounce((object: THREE.Object3D) => containsDevice(object, false), 100);

/**
 * 批量设备识别函数 - 优化大量对象的识别
 * @param objects 对象数组
 * @param isInAnimationLoop 是否在动画循环中调用
 * @returns 设备对象数组
 */
export function batchIdentifyDevices(objects: THREE.Object3D[], isInAnimationLoop: boolean = false): THREE.Object3D[] {
  // 更新动画帧ID
  if (isInAnimationLoop) {
    updateAnimationFrameId();
  }

  const devices: THREE.Object3D[] = [];
  const processedUUIDs = new Set<string>();

  for (const object of objects) {
    // 避免重复处理相同的对象
    if (processedUUIDs.has(object.uuid)) continue;
    processedUUIDs.add(object.uuid);

    if (containsDevice(object, isInAnimationLoop)) {
      devices.push(object);
    }
  }

  return devices;
}

/**
 * 清理缓存 - 用于内存管理
 */
export function clearDeviceIdentifierCaches(): void {
  deviceIdentifierCache.clear();
  _animationFrameCache.clear();
  _currentFrameId = 0;
  _performanceStats.totalCalls = 0;
  _performanceStats.cacheHits = 0;
  _performanceStats.animationFrameHits = 0;
  _performanceStats.lastResetTime = performance.now();
}
