<template>
  <a-modal v-model:visible="visible" title="车位详情" width="600px" :footer="null">
    <div v-if="space">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="车位号">{{ space.spaceNumber }}</a-descriptions-item>
        <a-descriptions-item label="楼层">{{ space.floor }}</a-descriptions-item>
        <a-descriptions-item label="区域">{{ space.area }}</a-descriptions-item>
        <a-descriptions-item label="类型">{{ space.type }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(space.status)">
            {{ getStatusText(space.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="车牌号">{{ space.vehicleNumber || '-' }}</a-descriptions-item>
        <a-descriptions-item label="车主">{{ space.ownerName || '-' }}</a-descriptions-item>
        <a-descriptions-item label="联系电话">{{ space.ownerPhone || '-' }}</a-descriptions-item>
        <a-descriptions-item label="停车时间">{{ space.parkTime || '-' }}</a-descriptions-item>
        <a-descriptions-item label="预计离开">{{ space.expectedLeaveTime || '-' }}</a-descriptions-item>
        <a-descriptions-item label="费用">{{ space.fee ? `¥${space.fee}` : '-' }}</a-descriptions-item>
      </a-descriptions>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import type { ParkingSpace } from '/@/api/operations/parking';

  interface Props {
    visible: boolean;
    space: ParkingSpace | null;
  }

  const props = defineProps<Props>();

  const emit = defineEmits<{
    'update:visible': [value: boolean];
  }>();

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const getStatusColor = (status: string) => {
    const colors = {
      available: 'green',
      occupied: 'red',
      reserved: 'blue',
      maintenance: 'orange',
    };
    return colors[status] || 'gray';
  };

  const getStatusText = (status: string) => {
    const texts = {
      available: '可用',
      occupied: '已占用',
      reserved: '已预约',
      maintenance: '维护中',
    };
    return texts[status] || status;
  };
</script>
