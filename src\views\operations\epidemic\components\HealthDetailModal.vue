<template>
  <a-modal v-model:visible="visible" title="健康详情" width="600px" :footer="null">
    <div v-if="healthInfo">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="姓名">{{ healthInfo.name }}</a-descriptions-item>
        <a-descriptions-item label="电话">{{ healthInfo.phone }}</a-descriptions-item>
        <a-descriptions-item label="部门">{{ healthInfo.department || '-' }}</a-descriptions-item>
        <a-descriptions-item label="人员类型">
          {{ healthInfo.isEmployee ? '员工' : '访客' }}
        </a-descriptions-item>
        <a-descriptions-item label="健康码">
          <a-tag :color="getHealthCodeColor(healthInfo.healthCode)">
            {{ getHealthCodeText(healthInfo.healthCode) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="体温">
          <span :class="healthInfo.temperature > 37.3 ? 'text-red-500' : 'text-green-500'"> {{ healthInfo.temperature }}°C </span>
        </a-descriptions-item>
        <a-descriptions-item label="疫苗状态">
          <a-tag :color="getVaccineStatusColor(healthInfo.vaccineStatus)">
            {{ getVaccineStatusText(healthInfo.vaccineStatus) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="核酸结果">
          <a-tag v-if="healthInfo.nucleicTestResult" :color="getNucleicTestColor(healthInfo.nucleicTestResult)">
            {{ getNucleicTestText(healthInfo.nucleicTestResult) }}
          </a-tag>
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item label="检查时间">{{ healthInfo.checkTime }}</a-descriptions-item>
        <a-descriptions-item label="检查地点">{{ healthInfo.checkLocation }}</a-descriptions-item>
      </a-descriptions>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import type { HealthInfo } from '/@/api/operations/epidemic';

  interface Props {
    visible: boolean;
    healthInfo: HealthInfo | null;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{ 'update:visible': [value: boolean] }>();

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const getHealthCodeColor = (code: string) => {
    const colors = { green: 'green', yellow: 'orange', red: 'red' };
    return colors[code] || 'gray';
  };

  const getHealthCodeText = (code: string) => {
    const texts = { green: '绿码', yellow: '黄码', red: '红码' };
    return texts[code] || code;
  };

  const getVaccineStatusColor = (status: string) => {
    const colors = { none: 'red', partial: 'orange', full: 'green', booster: 'blue' };
    return colors[status] || 'gray';
  };

  const getVaccineStatusText = (status: string) => {
    const texts = { none: '未接种', partial: '部分接种', full: '完全接种', booster: '加强针' };
    return texts[status] || status;
  };

  const getNucleicTestColor = (result: string) => {
    const colors = { negative: 'green', positive: 'red', pending: 'orange' };
    return colors[result] || 'gray';
  };

  const getNucleicTestText = (result: string) => {
    const texts = { negative: '阴性', positive: '阳性', pending: '待出结果' };
    return texts[result] || result;
  };
</script>
