# JeecgBoot 企业级低代码开发平台

[![License](https://img.shields.io/badge/license-Apache%20License%202.0-blue.svg)](https://github.com/zhangdaiscott/jeecg-boot/blob/master/LICENSE)
[![Version](https://img.shields.io/badge/version-3.7.1-brightgreen.svg)](https://github.com/zhangdaiscott/jeecg-boot)
[![GitHub stars](https://img.shields.io/github/stars/zhangdaiscott/jeecg-boot.svg?style=social&label=Stars)](https://github.com/zhangdaiscott/jeecg-boot)

> 基于 Vue3 + Vite + Ant Design Vue + TypeScript 的企业级低代码开发平台  
> 强大的代码生成器让前后端代码一键生成，帮助解决Java项目70%的重复工作

## 🚀 快速开始

### 环境要求
- Node.js 20+ (推荐 v20.15.0)
- npm / pnpm

### 安装运行

```bash
# 克隆项目
git clone https://github.com/jeecgboot/JeecgBoot.git

# 进入前端目录
cd JeecgBoot/jeecgboot-vue3

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

### 配置接口地址

编辑 `.env.development` 文件：

```bash
VITE_PROXY = [["/jeecgboot","http://localhost:8080/jeecg-boot"],["/upload","http://localhost:3300/upload"]]
VITE_GLOB_DOMAIN_URL=http://localhost:8080/jeecg-boot
```

> 将 `http://localhost:8080/jeecg-boot` 替换为您的后端服务地址

## 📚 文档与资源

- [官方文档](https://help.jeecg.com)
- [快速入门](http://jeecg.com/doc/quickstart)
- [常见问题](http://help.jeecg.com/qa.html)
- [在线演示](http://boot3.jeecg.com) | [获取演示账号](http://jeecg.com/doc/demo)

## 💬 技术交流

- QQ交流群：⑩716488839、⑨808791225

## 🛠 技术栈

- **前端框架**: Vue 3.0
- **构建工具**: Vite
- **UI组件库**: Ant Design Vue 4
- **开发语言**: TypeScript
- **状态管理**: Pinia
- **路由管理**: Vue Router

## 📖 学习资源

建议在开发前了解以下技术：

- [Vue3 文档](https://cn.vuejs.org/)
- [Ant Design Vue](https://www.antdv.com/docs/vue/introduce-cn/)
- [TypeScript](https://www.typescriptlang.org/)
- [Vite](https://cn.vitejs.dev/guide/)

## 🌐 浏览器支持

- ✅ Chrome (最新版)
- ✅ Firefox (最新2个版本)
- ✅ Safari (最新2个版本)
- ✅ Edge (最新2个版本)
- ❌ IE (不支持)

---

**当前版本**: 3.7.2 (2024-09-12)
