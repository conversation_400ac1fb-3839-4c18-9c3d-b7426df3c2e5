import { HeatmapManager } from './HeatmapManager';
import { HeatmapJSManager } from './HeatmapJSManager';

/**
 * 热力图功能类型
 */
export type HeatmapFunctionType = 'power' | 'temperature' | 'load' | 'cabinet_occupancy' | 'device_weight' | 'none';

/**
 * 全局热力图状态管理器
 * 用于协调不同热力图功能之间的状态，确保它们完全隔离且不会相互干扰
 */
export class GlobalHeatmapStateManager {
  private static instance: GlobalHeatmapStateManager | null = null;

  // 当前激活的热力图功能
  private activeFunction: HeatmapFunctionType = 'none';

  // 是否正在切换功能
  private isSwitching: boolean = false;

  // 各功能管理器
  private heatmapManager: HeatmapManager;
  private heatmapJSManager: HeatmapJSManager;

  /**
   * 获取单例实例
   */
  public static getInstance(): GlobalHeatmapStateManager {
    if (!GlobalHeatmapStateManager.instance) {
      GlobalHeatmapStateManager.instance = new GlobalHeatmapStateManager();
    }
    return GlobalHeatmapStateManager.instance;
  }

  /**
   * 私有构造函数，确保单例模式
   */
  private constructor() {
    this.heatmapManager = HeatmapManager.getInstance();
    this.heatmapJSManager = HeatmapJSManager.getInstance();
    console.log('全局热力图状态管理器已初始化');
  }

  /**
   * 获取当前激活的热力图功能
   */
  public getActiveFunction(): HeatmapFunctionType {
    return this.activeFunction;
  }

  /**
   * 检查是否有热力图功能处于活动状态
   */
  public isAnyFunctionActive(): boolean {
    return this.activeFunction !== 'none';
  }

  /**
   * 检查指定功能是否处于活动状态
   */
  public isFunctionActive(type: HeatmapFunctionType): boolean {
    return this.activeFunction === type;
  }

  /**
   * 检查是否正在切换功能
   */
  public isInSwitchingState(): boolean {
    return this.isSwitching;
  }

  /**
   * 激活指定热力图功能
   * @param type 热力图功能类型
   * @returns 是否成功激活
   */
  public async activateFunction(type: HeatmapFunctionType): Promise<boolean> {
    console.log(`[GlobalHeatmapStateManager] 请求激活热力图功能: ${type}, 当前功能: ${this.activeFunction}`);

    // 如果已经是当前功能，则不做任何操作
    if (type === this.activeFunction) {
      console.log(`[GlobalHeatmapStateManager] 热力图功能 ${type} 已经处于激活状态`);
      return true;
    }

    // 如果正在切换，则拒绝新的请求
    if (this.isSwitching) {
      console.warn(`[GlobalHeatmapStateManager] 正在切换热力图功能，拒绝新的请求: ${type}`);
      return false;
    }

    try {
      this.isSwitching = true;

      // 先清除所有当前激活的功能
      await this.clearAllFunctions();

      // 根据类型激活相应功能
      if (type === 'temperature') {
        await this.heatmapJSManager.show();
      } else if (type === 'power' || type === 'load' || type === 'cabinet_occupancy' || type === 'device_weight') {
        await this.heatmapManager.applyHeatmapAsync(type);
      } else if (type === 'none') {
        // 不需要激活任何功能
      } else {
        throw new Error(`未知的热力图功能类型: ${type}`);
      }

      // 更新当前激活的功能
      this.activeFunction = type;
      console.log(`[GlobalHeatmapStateManager] 热力图功能 ${type} 已激活`);

      return true;
    } catch (error) {
      console.error(`[GlobalHeatmapStateManager] 激活热力图功能 ${type} 失败:`, error);
      // 出错时重置状态
      this.activeFunction = 'none';
      return false;
    } finally {
      this.isSwitching = false;
    }
  }

  /**
   * 清除所有热力图功能
   */
  public async clearAllFunctions(): Promise<void> {
    console.log(`[GlobalHeatmapStateManager] 清除所有热力图功能, 当前功能: ${this.activeFunction}`);

    try {
      // 清除温度分布功能
      if (this.heatmapJSManager.getStatus().active) {
        console.log('[GlobalHeatmapStateManager] 清除温度分布功能');
        await this.heatmapJSManager.hide();
      }

      // 清除其他热力图功能
      console.log('[GlobalHeatmapStateManager] 清除能耗分布和设备负载功能');
      await this.heatmapManager.clearHeatmapAsync();

      // 更新状态
      this.activeFunction = 'none';
      console.log('[GlobalHeatmapStateManager] 所有热力图功能已清除');
    } catch (error) {
      console.error('[GlobalHeatmapStateManager] 清除热力图功能时发生错误:', error);
      throw error;
    }
  }
}
