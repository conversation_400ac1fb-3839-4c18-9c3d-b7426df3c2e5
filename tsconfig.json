{"compilerOptions": {"target": "es2020", "module": "esnext", "moduleResolution": "node", "strict": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "strictFunctionTypes": true, "jsx": "preserve", "baseUrl": ".", "allowJs": false, "sourceMap": true, "esModuleInterop": true, "resolveJsonModule": true, "noUnusedLocals": true, "noUnusedParameters": true, "experimentalDecorators": true, "downlevelIteration": true, "lib": ["dom", "es2020", "esnext"], "typeRoots": ["./node_modules/@types/", "./types", "./node_modules"], "noImplicitAny": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "checkJs": false, "paths": {"/@/*": ["src/*"], "/#/*": ["types/*"], "@/*": ["src/*"], "#/*": ["types/*"]}}, "include": ["tests/**/*.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/**/*.ts", "vite.config.ts"], "exclude": ["node_modules", "tests/server/**/*.ts", "dist", "**/*.js", "src/components/Form/src/utils/areaDataUtil.js", "src/utils/dict/DictColors.js", "src/utils/dict/JDictSelectUtil.js", "src/utils/encryption/signMd5Utils.js", "src/views/sys/login/useLogin.js"]}