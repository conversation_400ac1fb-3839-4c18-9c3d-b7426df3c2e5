import * as THREE from 'three';
import { useGlobalThreeStore } from '../store/globalThreeStore';
import { MeshBVH, MeshBVHOptions } from 'three-mesh-bvh';
import { SkyManager } from './SkyManager';
import { LightingManager } from './LightingManager';
import { GridFloorManager } from './effects/GridFloorManager';
import { debounce } from 'lodash-es';

// 定义内部光照接口 - 使用环境光、方向光和半球光
interface InteriorLights {
  ambient: THREE.AmbientLight;
  directional: THREE.DirectionalLight;
}

// 定义外部光照接口
interface ExteriorLights {
  ambient: THREE.AmbientLight;
  directional: THREE.DirectionalLight;
}

// 定义场景类型
type SceneType = 'exterior' | 'interior';

// 扩展网格几何体接口以支持 boundsTree
interface ExtendedBufferGeometry extends THREE.BufferGeometry {
  boundsTree?: MeshBVH;
}

// 新增: 用于LOD管理的接口
interface LODInfo {
  object: THREE.LOD;
  levels: {
    object: THREE.Object3D;
    distance: number;
    originalDistance: number;
  }[];
}

// 定义可释放资源的接口
interface DisposableResource {
  geometry?: THREE.BufferGeometry;
  material?: THREE.Material | THREE.Material[];
  textures?: THREE.Texture[];
}

export class SceneManager {
  private static instance: SceneManager | null = null;
  public scene: THREE.Scene = new THREE.Scene();
  private boundResizeRenderer: () => void = this.resizeRenderer.bind(this);
  public sceneType: SceneType = 'exterior';
  private ambientLight: THREE.AmbientLight = new THREE.AmbientLight(0xffffff, 0.8);
  private directionalLight: THREE.DirectionalLight = new THREE.DirectionalLight(0xffffff, 1);
  private interiorLights: InteriorLights = {
    ambient: new THREE.AmbientLight(0xffffff, 0.8), // 适当的环境光强度
    directional: new THREE.DirectionalLight(0xffffff, 1.0), // 方向光以增强细节
  };
  private exteriorLights: ExteriorLights = {
    ambient: this.ambientLight,
    directional: this.directionalLight,
  };
  public skyManager: SkyManager = SkyManager.getInstance();
  private gridFloorManager: GridFloorManager | null = null;
  private lastTime: number = performance.now();
  // 移除未使用的优化开关变量
  public needsAnimate: boolean = true;
  private updateCallbacks: Set<(deltaTime: number) => void> = new Set();
  public renderer: THREE.WebGLRenderer | null = null;
  public camera: THREE.Camera | null = null;
  public needsRender: boolean = false;
  private interactableObjectsCache: THREE.Object3D[] = [];
  private lastCacheUpdateTime: number = 0;
  private cacheUpdateInterval: number = 5000; // 增加到5秒更新一次缓存，减少频率
  private cacheInvalidated: boolean = true;
  private lodObjectsCache: LODInfo[] = [];
  private disposableObjects: Map<string, DisposableResource> = new Map();
  private debouncedResourceCleanup: () => void = debounce(this.cleanupUnusedResources.bind(this), 5000);
  private isRoamingMode: boolean = false;

  constructor() {
    if (SceneManager.instance) {
      return SceneManager.instance;
    }
    const globalThreeStore = useGlobalThreeStore();
    const container = globalThreeStore.containerRef;

    if (container) {
      container.addEventListener('resize', this.boundResizeRenderer);
    }

    this.directionalLight.position.set(100, 100, 100);
    this.scene.add(this.ambientLight);
    this.scene.add(this.directionalLight);

    this.scene.traverse((object) => {
      if ((object as THREE.Mesh).isMesh) {
        this.optimizeMesh(object as THREE.Mesh);
      }
    });

    this.skyManager.addToScene(this.scene);

    // 初始化GridFloorManager的场景引用（避免循环依赖）
    setTimeout(() => {
      this.getGridFloorManager().setScene(this.scene);
    }, 0);

    SceneManager.instance = this;
  }

  public static getInstance(): SceneManager {
    if (!SceneManager.instance) {
      SceneManager.instance = new SceneManager();
    }
    return SceneManager.instance;
  }

  /**
   * 获取GridFloorManager实例（延迟初始化以避免循环依赖）
   */
  private getGridFloorManager(): GridFloorManager {
    if (!this.gridFloorManager) {
      this.gridFloorManager = GridFloorManager.getInstance();
    }
    return this.gridFloorManager;
  }

  public switchSceneType(type: SceneType): void {
    if (this.sceneType === type) return;

    console.log(`切换场景类型: ${this.sceneType} -> ${type}`);
    this.sceneType = type;

    if (type === 'exterior') {
      // 移除所有内部光源
      this.scene.remove(this.interiorLights.ambient);
      this.scene.remove(this.interiorLights.directional);
      // 移除半球光的引用已被删除

      // 添加外部光源
      if (!this.scene.children.includes(this.exteriorLights.ambient)) {
        this.scene.add(this.exteriorLights.ambient);
      }
      if (!this.scene.children.includes(this.exteriorLights.directional)) {
        this.scene.add(this.exteriorLights.directional);
      }

      // 外景模式：隐藏网格地板（因为有建筑模型）
      this.getGridFloorManager().hide();

      this.skyManager.showSky();
      this.needsAnimate = true;

      if (this.renderer) {
        this.renderer.setClearColor(0x87ceeb);
      }

      const lightingManager = LightingManager.getInstance();
      if (lightingManager) {
        lightingManager.setEnvironmentType('exterior');
        // 应用外部场景光照设置
        lightingManager.adjustLightingForWeather('clear');
      }

      console.log('[SceneManager] 已切换到外景模式，网格地板已隐藏');
    } else if (type === 'interior') {
      // 移除所有外部光源
      this.scene.remove(this.exteriorLights.ambient);
      this.scene.remove(this.exteriorLights.directional);

      // 添加内部光源
      this.scene.add(this.interiorLights.ambient);
      this.scene.add(this.interiorLights.directional);
      // 移除半球光的引用

      // 配置内部方向光位置
      this.interiorLights.directional.position.set(10, 10, 10);
      this.interiorLights.directional.target.position.set(0, 0, 0);
      this.scene.add(this.interiorLights.directional.target);

      // 确保内景时完全隐藏天空和云朵
      this.skyManager.hideSky();
      this.needsAnimate = false;

      // 注意：网格地板的显示由BuildingManager控制，因为需要根据楼层模型设置正确高度
      // 这里不直接调用 this.gridFloorManager.show()

      if (this.renderer) {
        this.renderer.setClearColor(0x111111, 1); // 使用深灰色而不是纯黑色
      }

      const lightingManager = LightingManager.getInstance();
      if (lightingManager) {
        lightingManager.setEnvironmentType('interior');
        // 应用简化的内部场景光照设置
        this.setInteriorNoneEnvironment();
        lightingManager.setInteriorNoneEnvironment();
      }

      console.log('[SceneManager] 已切换到内景模式，天空已隐藏，网格地板由BuildingManager控制');
    }
  }

  public toggleEnvironment(isDay: boolean): void {
    if (this.sceneType === 'exterior') {
      if (isDay) {
        this.skyManager.setDaytime();
        this.setDayLighting();
      } else {
        this.skyManager.setNighttime();
        this.setNightLighting();
      }
    } else {
      this.setInteriorLighting(isDay);
    }
  }

  public setInteriorLighting(isDay: boolean): void {
    if (isDay) {
      // 只使用环境光
      this.interiorLights.ambient.intensity = 1.0;
      this.interiorLights.ambient.color.set(0xffffff);
    } else {
      // 夜间模式下稍微降低环境光强度
      this.interiorLights.ambient.intensity = 0.8;
      this.interiorLights.ambient.color.set(0xf0f0ff);
    }
  }

  /**
   * 设置内景场景为简化的光照模式
   * 此方法只影响内部场景，不会影响外部场景
   */
  public setInteriorNoneEnvironment(): void {
    // 严格检查当前是否为内部场景，如果不是则直接返回
    if (this.sceneType !== 'interior') {
      console.log('当前不是内部场景，跳过内部场景设置');
      return;
    }

    // 设置场景背景色为深灰色而不是纯黑色，以提高可见度
    this.scene.background = new THREE.Color(0x111111);

    if (this.renderer) {
      this.renderer.setClearColor(0x111111, 1);
    }

    // 配置内部光源 - 禁用阴影以提高性能
    this.interiorLights.directional.castShadow = false;

    // 通过LightingManager来管理光照
    const lightingManager = LightingManager.getInstance();
    if (lightingManager) {
      // 使用统一的内部光照设置
      lightingManager.setInteriorNoneEnvironment();
    } else {
      // 如果没有LightingManager实例，则使用默认设置
      this.interiorLights.ambient.intensity = 0.8;
      this.interiorLights.ambient.color.set(0xffffff);
      this.interiorLights.directional.intensity = 1.0;
      this.interiorLights.directional.color.set(0xffffff);
    }

    console.log('已设置内景场景为简化的光照模式');
  }

  public setDayLighting(): void {
    this.ambientLight.intensity = 0.5;
    this.directionalLight.intensity = 1;

    if (this.renderer) {
      this.renderer.setClearColor(0x87ceeb);
    }
  }

  public setNightLighting(): void {
    this.ambientLight.intensity = 0.1;
    this.directionalLight.intensity = 0.05;

    if (this.renderer) {
      this.renderer.setClearColor(0x000000, 1);
    }
  }

  public optimizeMesh(mesh: THREE.Mesh): void {
    const geometry = mesh.geometry as ExtendedBufferGeometry;

    if (!geometry.boundsTree) {
      const options: MeshBVHOptions = {
        strategy: 0,
        maxLeafTris: 10,
        verbose: false,
      };
      geometry.boundsTree = new MeshBVH(geometry, options);
    }

    if (mesh.material) {
      if (Array.isArray(mesh.material)) {
        mesh.material.forEach((mat) => {
          // 对所有材质类型都设置高精度，不仅限于非基础材质
          (mat as any).precision = 'highp';
        });
      } else {
        // 对所有材质类型都设置高精度，不仅限于非基础材质
        (mesh.material as any).precision = 'highp';
      }
    }
  }

  public optimizeAllMeshes(): void {
    this.lodObjectsCache = [];

    this.scene.traverse((object) => {
      if ((object as THREE.Mesh).isMesh) {
        this.optimizeMesh(object as THREE.Mesh);

        const mesh = object as THREE.Mesh;
        if (mesh.geometry && !mesh.geometry.boundingSphere) {
          mesh.geometry.computeBoundingSphere();
        }

        object.userData.canCull = true;

        if (this._isNonCullableObject(mesh)) {
          object.userData.canCull = false;
        }
      }

      if (object instanceof THREE.LOD) {
        const lodInfo: LODInfo = {
          object: object,
          levels: object.levels.map((level) => ({
            object: level.object,
            distance: level.distance,
            originalDistance: level.distance,
          })),
        };
        this.lodObjectsCache.push(lodInfo);

        object.userData.canCull = false;
      }
    });

    this.optimizeSceneTraversal();
  }

  public updateLODConfiguration(distanceMultiplier: number, fps: number = 60): void {
    if (this.lodObjectsCache.length === 0) {
      this.optimizeAllMeshes();
    }

    // 根据帧率动态调整距离倍数，帧率低时增加倍数
    let dynamicMultiplier = distanceMultiplier;
    if (fps < 30) {
      dynamicMultiplier = distanceMultiplier * 1.5;
    } else if (fps < 45) {
      dynamicMultiplier = distanceMultiplier * 1.2;
    }

    this.lodObjectsCache.forEach((lodInfo) => {
      lodInfo.levels.forEach((level, index) => {
        if (index > 0) {
          const levelFactor = 1.0 - index * 0.05;
          const adjustedDistance = level.originalDistance * dynamicMultiplier * levelFactor;

          const lodLevel = lodInfo.object.levels.find((l) => l.object === level.object);
          if (lodLevel) {
            lodLevel.distance = adjustedDistance;
          }
        }
      });
    });
  }

  // 移除未使用的LOD相关方法

  private _isNonCullableObject(object: THREE.Object3D): boolean {
    const name = object.name.toLowerCase();
    if (
      name.includes('sky') ||
      name.includes('cloud') ||
      name.includes('环境') ||
      name.includes('天空') ||
      name.includes('sun') ||
      name.includes('moon')
    ) {
      return true;
    }

    if (object instanceof THREE.Mesh && object.geometry && object.geometry.boundingSphere) {
      const radius = object.geometry.boundingSphere.radius;
      if (radius > 400) {
        return true;
      }
    }

    if (object instanceof THREE.Mesh) {
      const material = object.material as THREE.Material | THREE.Material[];

      if (Array.isArray(material)) {
        for (const mat of material) {
          if (this._isEnvironmentMaterial(mat)) {
            return true;
          }
        }
      } else if (this._isEnvironmentMaterial(material)) {
        return true;
      }
    }

    return false;
  }

  private _isEnvironmentMaterial(material: THREE.Material): boolean {
    if (material instanceof THREE.ShaderMaterial || material.name.toLowerCase().includes('sky') || material.name.toLowerCase().includes('cloud')) {
      return true;
    }

    if (!material.depthWrite) {
      return true;
    }

    return false;
  }

  public optimizeSceneTraversal(): void {
    this.updateInteractableObjectsCache();
    console.info(`已缓存 ${this.interactableObjectsCache.length} 个可交互对象用于视锥体剔除`);
  }

  // 移除未使用的材质优化方法

  public disposeScene(renderer?: THREE.WebGLRenderer): void {
    this.scene.traverse((object) => {
      if ((object as THREE.Mesh).isMesh) {
        const mesh = object as THREE.Mesh;
        if (mesh.geometry) {
          mesh.geometry.dispose();
        }
        if (Array.isArray(mesh.material)) {
          mesh.material.forEach((material) => {
            this.disposeMaterial(material);
          });
        } else if (mesh.material) {
          this.disposeMaterial(mesh.material);
        }
      } else if ((object as any).dispose && typeof (object as any).dispose === 'function') {
        (object as any).dispose();
      }
    });

    this.disposableObjects.forEach((resource) => {
      this.disposeResource(resource);
    });
    this.disposableObjects.clear();

    while (this.scene.children.length > 0) {
      this.scene.remove(this.scene.children[0]);
    }

    if (renderer) {
      renderer.dispose();
      renderer.forceContextLoss();
      (renderer as any).domElement = null;
    }

    SceneManager.instance = null;
  }

  private disposeMaterial(material: THREE.Material): void {
    const textures = this.collectTexturesFromMaterial(material);
    textures.forEach((texture) => {
      if (texture && texture.dispose) {
        texture.dispose();
      }
    });

    material.dispose();
  }

  public dispose(): void {
    if (this.boundResizeRenderer) {
      const globalThreeStore = useGlobalThreeStore();
      const container = globalThreeStore.containerRef;
      if (container) {
        container.removeEventListener('resize', this.boundResizeRenderer);
      }
    }

    if (this.renderer) {
      this.renderer.dispose();
      this.renderer = null;
    }

    this.cleanupUnusedResources();
    this.disposableObjects.clear();

    this.interactableObjectsCache = [];
    this.lastCacheUpdateTime = 0;
    this.cacheInvalidated = true;

    if (this.scene) {
      this.scene.traverse((object: THREE.Object3D) => {
        if (object instanceof THREE.Mesh) {
          if (object.geometry) object.geometry.dispose();
          if (object.material) {
            if (Array.isArray(object.material)) {
              object.material.forEach((mat) => this.disposeMaterial(mat));
            } else {
              this.disposeMaterial(object.material);
            }
          }
        }
      });
    }
  }

  public resizeRenderer(): void {
    const width = window.innerWidth;
    const height = window.innerHeight;
    if (this.camera) {
      const perspectiveCamera = this.camera as THREE.PerspectiveCamera;
      if (perspectiveCamera.isPerspectiveCamera) {
        perspectiveCamera.aspect = width / height;
        perspectiveCamera.updateProjectionMatrix();
      }
    }
    if (this.renderer) {
      this.renderer.setSize(width, height);
    }
  }

  public setMeshOpacity(mesh: THREE.Mesh, opacity: number): void {
    if (mesh.material) {
      if (Array.isArray(mesh.material)) {
        mesh.material.forEach((material) => {
          material.transparent = true;
          material.opacity = opacity;
        });
      } else {
        mesh.material.transparent = true;
        mesh.material.opacity = opacity;
      }
    }
  }

  public addUpdateCallback(callback: (deltaTime: number) => void): void {
    if (typeof callback === 'function') {
      this.updateCallbacks.add(callback);
    }
  }

  public removeUpdateCallback(callback: (deltaTime: number) => void): void {
    if (this.updateCallbacks.has(callback)) {
      this.updateCallbacks.delete(callback);
    }
  }

  // 性能优化：添加静态计数器和更新控制
  private static updateCounter: number = 0;

  /**
   * 设置漫游模式
   * @param enabled 是否启用漫游模式
   */
  public setRoamingMode(enabled: boolean): void {
    if (this.isRoamingMode === enabled) return;

    this.isRoamingMode = enabled;

    if (enabled) {
      // 漫游模式下优化LOD距离
      this.updateLODConfiguration(1.5, 60);

      // 开启持续动画
      this.needsAnimate = true;
    } else {
      // 恢复默认LOD设置
      this.updateLODConfiguration(1.0, 60);
    }

    console.log(`[SceneManager] 漫游模式已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 更新场景 - 极度性能优化版本
   * 使用计数器控制更新频率，减少不必要的计算
   */
  public update(): void {
    // 增加更新计数器
    SceneManager.updateCounter++;

    const currentTime = performance.now();
    const deltaTime = currentTime - this.lastTime;
    this.lastTime = currentTime;

    // 极度性能优化：只在需要时更新天空
    if (this.skyManager && this.needsAnimate && this.sceneType === 'exterior') {
      // 降低云层动画更新频率，每3帧更新一次
      // 漫游模式下进一步降低频率
      const skyUpdateInterval = this.isRoamingMode ? 5 : 3;
      if (SceneManager.updateCounter % skyUpdateInterval === 0) {
        this.skyManager.animateClouds(deltaTime * skyUpdateInterval); // 调整时间步长以保持视觉效果
      }
    }

    // 极度性能优化：批量处理回调
    // 每帧最多处理10个回调，避免长时间阻塞主线程
    const callbacks = Array.from(this.updateCallbacks);
    const maxCallbacksPerFrame = 10;
    const startIndex = (SceneManager.updateCounter * maxCallbacksPerFrame) % Math.max(callbacks.length, 1);
    const endIndex = Math.min(startIndex + maxCallbacksPerFrame, callbacks.length);

    // 处理部分回调
    for (let i = startIndex; i < endIndex; i++) {
      try {
        callbacks[i](deltaTime);
      } catch (error) {
        console.error('Error in update callback:', error);
      }
    }

    // 如果所有回调都已处理，再处理剩余的回调
    if (callbacks.length > 0 && endIndex === callbacks.length && startIndex > 0) {
      for (let i = 0; i < Math.min(startIndex, maxCallbacksPerFrame); i++) {
        try {
          callbacks[i](deltaTime);
        } catch (error) {
          console.error('Error in update callback:', error);
        }
      }
    }

    // 降低资源清理频率，每100帧执行一次
    if (SceneManager.updateCounter % 100 === 0) {
      this.debouncedResourceCleanup();
    }
  }

  /**
   * 强制更新场景
   * 用于配置应用后安全地触发场景更新
   */
  public forceUpdate(): void {
    try {
      // 标记场景需要更新
      this.scene.traverse((object) => {
        if (object.type === 'Mesh' || object.type === 'SkinnedMesh') {
          object.matrixWorldNeedsUpdate = true;
        }
      });

      // 触发灯光更新
      if (this.interiorLights && this.interiorLights.directional) {
        this.interiorLights.directional.shadow.needsUpdate = true;
      }
      if (this.exteriorLights && this.exteriorLights.directional) {
        this.exteriorLights.directional.shadow.needsUpdate = true;
      }

      console.log('[SceneManager] 场景强制更新已触发');
    } catch (error) {
      console.error('[SceneManager] 强制更新场景时出错:', error);
    }
  }

  /**
   * 获取当前场景类型
   * @returns 当前场景类型
   */
  public getCurrentSceneType(): SceneType {
    return this.sceneType;
  }

  /**
   * 清除所有摄像机视锥体
   * 保留此方法以兼容现有代码，但简化实现
   */
  public clearAllCameraFrustums(): void {
    // 简化实现，不再使用视锥体功能
    console.log('[SceneManager] clearAllCameraFrustums called - 功能已简化');
  }

  public render(): void {
    if (this.renderer && this.scene && this.camera) {
      this.renderer.render(this.scene, this.camera);
    }
  }

  public forceRender(): void {
    if (this.renderer && this.scene && this.camera) {
      this.needsRender = true;
      this.render();

      for (let i = 0; i < 3; i++) {
        setTimeout(() => {
          this.needsRender = true;
          this.render();
        }, i * 100);
      }
    }
  }

  public addTrackedObject(object: THREE.Object3D, id?: string): void {
    this.scene.add(object);
    const resourceId = id || object.uuid;
    this.trackObjectResources(object, resourceId);
    this.invalidateObjectsCache();
  }

  private trackObjectResources(object: THREE.Object3D, id: string): void {
    object.traverse((child) => {
      if ((child as THREE.Mesh).isMesh) {
        const mesh = child as THREE.Mesh;
        const resource: DisposableResource = {};

        if (mesh.geometry) {
          resource.geometry = mesh.geometry;
        }

        if (mesh.material) {
          resource.material = mesh.material;
          resource.textures = this.collectTexturesFromMaterial(mesh.material);
        }

        if (resource.geometry || resource.material) {
          this.disposableObjects.set(`${id}_${child.uuid}`, resource);
        }
      }
    });
  }

  private collectTexturesFromMaterial(material: THREE.Material | THREE.Material[]): THREE.Texture[] {
    const textures: THREE.Texture[] = [];
    const processMaterial = (mat: THREE.Material) => {
      const keys = Object.keys(mat);
      for (const key of keys) {
        const value = (mat as any)[key];
        if (value && value.isTexture) {
          textures.push(value);
        }
      }
    };

    if (Array.isArray(material)) {
      material.forEach(processMaterial);
    } else {
      processMaterial(material);
    }

    return textures;
  }

  public removeTrackedObject(object: THREE.Object3D): void {
    const objectId = object.uuid;
    this.scene.remove(object);

    this.disposableObjects.forEach((resource, key) => {
      if (key.startsWith(objectId)) {
        this.disposeResource(resource);
        this.disposableObjects.delete(key);
      }
    });

    this.invalidateObjectsCache();
  }

  private disposeResource(resource: DisposableResource): void {
    if (resource.geometry) {
      resource.geometry.dispose();
    }

    if (resource.material) {
      if (Array.isArray(resource.material)) {
        resource.material.forEach((mat) => mat.dispose());
      } else {
        resource.material.dispose();
      }
    }

    if (resource.textures) {
      resource.textures.forEach((texture) => texture.dispose());
    }
  }

  public cleanupUnusedResources(): void {
    const objectsInScene = new Set<string>();

    this.scene.traverse((object) => {
      objectsInScene.add(object.uuid);
    });

    const keysToRemove: string[] = [];
    this.disposableObjects.forEach((resource, key) => {
      const objectId = key.split('_')[0];
      if (!objectsInScene.has(objectId)) {
        this.disposeResource(resource);
        keysToRemove.push(key);
      }
    });

    keysToRemove.forEach((key) => {
      this.disposableObjects.delete(key);
    });
  }

  public getInteractableObjects(): THREE.Object3D[] {
    const now = performance.now();

    if (this.cacheInvalidated || now - this.lastCacheUpdateTime > this.cacheUpdateInterval) {
      this.updateInteractableObjectsCache();
    }

    return this.interactableObjectsCache;
  }
  private updateInteractableObjectsCache(): void {
    const previousCount = this.interactableObjectsCache.length;
    this.interactableObjectsCache = [];

    this.scene.traverse((object) => {
      if (object instanceof THREE.Mesh || object instanceof THREE.Group) {
        if (this.isInteractableObject(object)) {
          this.interactableObjectsCache.push(object);
        }
      }
    });

    this.lastCacheUpdateTime = performance.now();
    this.cacheInvalidated = false;

    // 只在开发模式下且对象数量变化时打印日志，避免无意义的重复输出
    if (process.env.NODE_ENV === 'development' && this.interactableObjectsCache.length !== previousCount) {
      console.debug(`[SceneManager] 已更新可交互对象缓存，共 ${this.interactableObjectsCache.length} 个对象`);
    }
  }

  private isInteractableObject(object: THREE.Object3D): boolean {
    if (object instanceof THREE.LOD) {
      return true;
    }

    const name = object.name.toLowerCase();
    if (name.includes('server') || name.includes('device') || name.includes('cabinet') || name.includes('机柜') || name.includes('设备')) {
      return true;
    }

    if (object.userData && (object.userData.interactive === true || object.userData.type === 'device' || object.userData.category === 'equipment')) {
      return true;
    }

    return false;
  }

  public invalidateObjectsCache(): void {
    this.cacheInvalidated = true;
  }

  public getInteriorLights(): InteriorLights | null {
    return this.interiorLights || null;
  }

  public getExteriorLights(): ExteriorLights | null {
    return this.exteriorLights || null;
  }
}
