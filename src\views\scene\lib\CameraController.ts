import * as THREE from 'three';
import { useGlobalThreeStore } from '../store/globalThreeStore';
import { SceneManager } from './SceneManager';

interface Position {
  x: number;
  y: number;
  z: number;
}

export class CameraController {
  private static instance: CameraController | null = null;
  public camera!: THREE.PerspectiveCamera;
  private defaultTarget!: THREE.Vector3;
  public currentTarget!: THREE.Vector3;
  private width: number = 0;
  private height: number = 0;
  private isMoving: boolean = false;
  private currentAnimationId: number | null = null;
  private currentMoveCallback: (() => void) | null = null;

  constructor() {
    if (CameraController.instance) {
      return CameraController.instance;
    }

    const globalThreeStore = useGlobalThreeStore();
    const container = globalThreeStore.containerRef;
    if (!container) {
      throw new Error('Container ref is not available in globalThreeStore.');
    }
    this.width = container.clientWidth;
    this.height = container.clientHeight;
    this.camera = new THREE.PerspectiveCamera(75, this.width / this.height, 0.1, 1000);

    // 初始相机位置
    const cameraPosition: Position = {
      x: -45.45,
      y: 45.2,
      z: -70.2,
    };
    this.camera.position.set(cameraPosition.x, cameraPosition.y, cameraPosition.z);

    // 设置相机朝向
    const targetPoint = new THREE.Vector3(0, 10, 0);
    this.camera.lookAt(targetPoint);

    this.defaultTarget = targetPoint.clone();
    this.currentTarget = targetPoint.clone();

    CameraController.instance = this;
  }

  static getInstance(): CameraController {
    if (!CameraController.instance) {
      CameraController.instance = new CameraController();
    }
    return CameraController.instance!;
  }

  updateAspect(width?: number, height?: number): void {
    if (width && height) {
      this.width = width;
      this.height = height;
    }
    this.camera.aspect = this.width / this.height;
    this.camera.updateProjectionMatrix();

    // 强制渲染
    this.forceRender();
  }

  // 更新容器尺寸
  updateContainerSize(width: number, height: number): void {
    this.width = width;
    this.height = height;
    this.updateAspect();
  }

  getCameraPosition(): THREE.Vector3 {
    const position = new THREE.Vector3();
    this.camera.getWorldPosition(position);
    return position;
  }

  // 设置相机位置（不带动画）
  setCameraPosition(x: number, y: number, z: number): void {
    this.camera.position.set(x, y, z);
    this.forceRender();
  }

  // 设置相机目标（不带动画）
  setCameraTarget(x: number, y: number, z: number): void {
    this.currentTarget.set(x, y, z);
    this.camera.lookAt(this.currentTarget);
    this.forceRender();
  }

  forceRender(): boolean {
    const sceneManager = SceneManager.getInstance();
    if (sceneManager && sceneManager.renderer) {
      sceneManager.needsRender = true;
      // 请求多个渲染帧确保更新
      for (let i = 0; i < 3; i++) {
        setTimeout(() => {
          if (sceneManager) {
            sceneManager.needsRender = true;
            sceneManager.render();
          }
        }, i * 100);
      }
      return true;
    }
    return false;
  }

  moveToPosition(position: Position, target: Position, duration: number = 1000, callback?: () => void): void {
    console.log('[CameraController] 开始移动相机到目标位置');

    // 如果正在移动，先中断当前移动
    if (this.isMoving) {
      console.log('[CameraController] 中断当前移动，开始新的移动');
      this.stopCurrentMovement();
    }

    // 设置移动状态
    this.isMoving = true;
    this.currentMoveCallback = callback || null;

    // 动态导入 ControlManager 避免循环依赖
    import('./control/ControlManager')
      .then(({ ControlManager }) => {
        let controlManager: any = null;
        let orbitControls: any = null;
        let originalControlsEnabled = false;

        try {
          controlManager = ControlManager.getInstance();
          orbitControls = controlManager.getOrbitControls();
          originalControlsEnabled = orbitControls?.enabled || false;

          // 临时禁用 OrbitControls 以避免干扰相机移动
          if (orbitControls) {
            orbitControls.enabled = false;
            console.log('[CameraController] 已禁用 OrbitControls');
          }
        } catch (error) {
          console.warn('[CameraController] 无法访问 ControlManager，继续执行移动:', error);
        }

        const startPosition = this.camera.position.clone();
        const startTarget = this.currentTarget.clone();
        const endPosition = new THREE.Vector3(position.x, position.y, position.z);
        const endTarget = new THREE.Vector3(target.x, target.y, target.z);

        console.log('[CameraController] 移动参数:', {
          from: startPosition,
          to: endPosition,
          targetFrom: startTarget,
          targetTo: endTarget,
          duration,
        });

        const startTime = performance.now();

        const animate = (): void => {
          // 检查是否应该停止动画（被新的移动中断）
          if (!this.isMoving) {
            console.log('[CameraController] 移动被中断');
            return;
          }

          const currentTime = performance.now();
          const elapsed = currentTime - startTime;
          const progress = Math.min(elapsed / duration, 1);

          const easeProgress = this.easeInOutCubic(progress);

          // 更新相机位置
          this.camera.position.lerpVectors(startPosition, endPosition, easeProgress);

          // 更新相机目标并应用
          const currentTarget = new THREE.Vector3();
          currentTarget.lerpVectors(startTarget, endTarget, easeProgress);
          this.currentTarget.copy(currentTarget);
          this.camera.lookAt(currentTarget);

          // 更新 OrbitControls 的目标（如果存在），但不调用 update()
          if (orbitControls && orbitControls.target) {
            orbitControls.target.copy(currentTarget);
            // 强制设置相机的矩阵为已更新状态
            this.camera.updateMatrixWorld(true);
          }

          // 强制渲染
          const sceneManager = SceneManager.getInstance();
          if (sceneManager) {
            sceneManager.needsRender = true;
            sceneManager.render();
          }

          if (progress < 1) {
            this.currentAnimationId = requestAnimationFrame(animate);
          } else {
            // 移动完成
            this.finishMovement(endTarget, orbitControls, originalControlsEnabled);
          }
        };

        this.currentAnimationId = requestAnimationFrame(animate);
      })
      .catch((error) => {
        console.error('[CameraController] 导入 ControlManager 失败:', error);

        // 如果无法导入 ControlManager，执行简化版本的移动
        const startPosition = this.camera.position.clone();
        const startTarget = this.currentTarget.clone();
        const endPosition = new THREE.Vector3(position.x, position.y, position.z);
        const endTarget = new THREE.Vector3(target.x, target.y, target.z);

        const startTime = performance.now();

        const animate = (): void => {
          // 检查是否应该停止动画
          if (!this.isMoving) {
            console.log('[CameraController] 移动被中断（简化版本）');
            return;
          }

          const currentTime = performance.now();
          const elapsed = currentTime - startTime;
          const progress = Math.min(elapsed / duration, 1);

          const easeProgress = this.easeInOutCubic(progress);

          this.camera.position.lerpVectors(startPosition, endPosition, easeProgress);

          const currentTarget = new THREE.Vector3();
          currentTarget.lerpVectors(startTarget, endTarget, easeProgress);
          this.camera.lookAt(currentTarget);

          const sceneManager = SceneManager.getInstance();
          if (sceneManager) {
            sceneManager.render();
          }

          if (progress < 1) {
            this.currentAnimationId = requestAnimationFrame(animate);
          } else {
            this.finishMovement(endTarget);
          }
        };

        this.currentAnimationId = requestAnimationFrame(animate);
      });
  }

  // 停止当前移动
  private stopCurrentMovement(): void {
    if (this.currentAnimationId !== null) {
      cancelAnimationFrame(this.currentAnimationId);
      this.currentAnimationId = null;
    }
    this.isMoving = false;
    this.currentMoveCallback = null;
  }

  // 完成移动
  private finishMovement(endTarget: THREE.Vector3, orbitControls?: any, originalControlsEnabled?: boolean): void {
    this.currentTarget.copy(endTarget);

    // 确保相机朝向目标
    this.camera.lookAt(endTarget);
    // 强制更新相机变换矩阵
    this.camera.updateMatrixWorld(true);

    this.isMoving = false;
    this.currentAnimationId = null;

    console.log('[CameraController] 相机移动完成，位置:', this.camera.position, '目标:', endTarget);

    // 强制渲染以确保视觉更新
    const sceneManager = SceneManager.getInstance();
    if (sceneManager) {
      sceneManager.needsRender = true;
      sceneManager.render();
    }

    // 重新启用 OrbitControls，但延迟一帧以确保相机位置稳定
    if (orbitControls && originalControlsEnabled) {
      // 先设置目标，但不立即启用
      orbitControls.target.copy(endTarget);

      // 使用 setTimeout 延迟重新启用，确保相机位置不被覆盖
      setTimeout(() => {
        if (orbitControls) {
          orbitControls.enabled = true;
          // 不调用 update() 以避免覆盖相机位置
          console.log('[CameraController] 已重新启用 OrbitControls（延迟）');

          // 再次强制渲染
          if (sceneManager) {
            sceneManager.needsRender = true;
            sceneManager.render();
          }
        }
      }, 50); // 50ms 延迟
    }

    // 执行回调
    if (this.currentMoveCallback) {
      const callback = this.currentMoveCallback;
      this.currentMoveCallback = null;
      callback();
    }
  }

  // 获取移动状态
  public isCurrentlyMoving(): boolean {
    return this.isMoving;
  }

  private easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  getDefaultTarget(): THREE.Vector3 {
    return this.defaultTarget;
  }
}
