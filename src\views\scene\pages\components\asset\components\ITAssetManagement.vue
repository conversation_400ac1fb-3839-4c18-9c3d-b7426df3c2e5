<template>
  <div class="h-full flex flex-col bg-gradient-to-br from-[#0a1628] to-[#1a2332] p-[1vw]">
    <!-- 顶部统计面板 -->
    <div class="grid grid-cols-5 gap-[0.8vw] mb-[1vw]">
      <div v-for="stat in itStats" :key="stat.label" class="bg-[rgba(59,142,230,0.1)] p-[0.8vw] rounded-lg border border-[rgba(59,142,230,0.2)]">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-[1.2vw] font-bold" :class="stat.valueClass">{{ stat.value }}</div>
            <div class="text-[0.7vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
          </div>
          <component :is="stat.icon" class="text-[1.5vw]" :class="stat.iconClass" />
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 grid grid-cols-3 gap-[1vw]">
      <!-- 左侧：IT设备分类 -->
      <div class="bg-[rgba(59,142,230,0.1)] p-[0.8vw] rounded-lg border border-[rgba(59,142,230,0.2)]">
        <div class="text-[0.8vw] text-white mb-[0.8vw] flex items-center">
          <DesktopOutlined class="mr-[0.4vw] text-blue-400" />
          IT设备分类
        </div>
        <div class="space-y-[0.4vw] max-h-[calc(100%-3vw)] overflow-y-auto">
          <div
            v-for="category in itCategories"
            :key="category.name"
            class="bg-black/20 p-[0.6vw] rounded cursor-pointer hover:bg-black/30 transition-all"
            :class="{ 'bg-blue-500/20 border border-blue-400': selectedCategory === category.name }"
            @click="selectCategory(category.name)"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <component :is="category.icon" class="mr-[0.3vw] text-[0.8vw]" :class="category.iconClass" />
                <span class="text-[0.7vw] text-white">{{ category.name }}</span>
              </div>
              <span class="text-[0.6vw] text-gray-400">{{ category.count }}台</span>
            </div>
            <div class="mt-[0.2vw] text-[0.6vw] text-gray-400">{{ category.description }}</div>
          </div>
        </div>
      </div>

      <!-- 中间：设备列表 -->
      <div class="bg-[rgba(59,142,230,0.1)] p-[0.8vw] rounded-lg border border-[rgba(59,142,230,0.2)]">
        <div class="text-[0.8vw] text-white mb-[0.8vw] flex items-center justify-between">
          <div class="flex items-center">
            <UnorderedListOutlined class="mr-[0.4vw] text-green-400" />
            {{ selectedCategory || '全部设备' }}
          </div>
          <div class="text-[0.6vw] text-gray-400">{{ filteredDevices.length }}台</div>
        </div>
        <div class="space-y-[0.4vw] max-h-[calc(100%-3vw)] overflow-y-auto">
          <div
            v-for="device in filteredDevices"
            :key="device.id"
            class="bg-black/20 p-[0.6vw] rounded cursor-pointer hover:bg-black/30 transition-all"
            @click="selectDevice(device)"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-[0.4vw] h-[0.4vw] rounded-full mr-[0.3vw]" :class="device.status === 'online' ? 'bg-green-400' : 'bg-red-400'"></div>
                <span class="text-[0.7vw] text-white">{{ device.name }}</span>
              </div>
              <span class="text-[0.6vw]" :class="device.status === 'online' ? 'text-green-400' : 'text-red-400'">
                {{ device.status === 'online' ? '在线' : '离线' }}
              </span>
            </div>
            <div class="mt-[0.2vw] text-[0.6vw] text-gray-400">{{ device.location }}</div>
            <div class="mt-[0.2vw] flex justify-between">
              <span class="text-[0.6vw] text-gray-400">{{ device.model }}</span>
              <span class="text-[0.6vw] text-blue-400">{{ device.ip }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：设备详情 -->
      <div class="bg-[rgba(59,142,230,0.1)] p-[0.8vw] rounded-lg border border-[rgba(59,142,230,0.2)]">
        <div class="text-[0.8vw] text-white mb-[0.8vw] flex items-center">
          <InfoCircleOutlined class="mr-[0.4vw] text-orange-400" />
          设备详情
        </div>
        <div v-if="selectedDevice" class="space-y-[0.6vw]">
          <!-- 基本信息 -->
          <div class="bg-black/20 p-[0.6vw] rounded">
            <div class="text-[0.7vw] text-blue-400 mb-[0.4vw]">基本信息</div>
            <div class="space-y-[0.3vw]">
              <div class="flex justify-between">
                <span class="text-[0.6vw] text-gray-400">设备名称:</span>
                <span class="text-[0.6vw] text-white">{{ selectedDevice.name }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-[0.6vw] text-gray-400">设备类型:</span>
                <span class="text-[0.6vw] text-white">{{ selectedDevice.category }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-[0.6vw] text-gray-400">IP地址:</span>
                <span class="text-[0.6vw] text-white">{{ selectedDevice.ip }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-[0.6vw] text-gray-400">MAC地址:</span>
                <span class="text-[0.6vw] text-white">{{ selectedDevice.mac }}</span>
              </div>
            </div>
          </div>

          <!-- 性能监控 -->
          <div class="bg-black/20 p-[0.6vw] rounded">
            <div class="text-[0.7vw] text-green-400 mb-[0.4vw]">性能监控</div>
            <div class="space-y-[0.3vw]">
              <div class="flex justify-between items-center">
                <span class="text-[0.6vw] text-gray-400">CPU使用率:</span>
                <div class="flex items-center">
                  <div class="w-[3vw] h-[0.3vw] bg-gray-600 rounded mr-[0.3vw]">
                    <div class="h-full bg-blue-400 rounded" :style="{ width: selectedDevice.cpuUsage + '%' }"></div>
                  </div>
                  <span class="text-[0.6vw] text-white">{{ selectedDevice.cpuUsage }}%</span>
                </div>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-[0.6vw] text-gray-400">内存使用率:</span>
                <div class="flex items-center">
                  <div class="w-[3vw] h-[0.3vw] bg-gray-600 rounded mr-[0.3vw]">
                    <div class="h-full bg-green-400 rounded" :style="{ width: selectedDevice.memoryUsage + '%' }"></div>
                  </div>
                  <span class="text-[0.6vw] text-white">{{ selectedDevice.memoryUsage }}%</span>
                </div>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-[0.6vw] text-gray-400">磁盘使用率:</span>
                <div class="flex items-center">
                  <div class="w-[3vw] h-[0.3vw] bg-gray-600 rounded mr-[0.3vw]">
                    <div class="h-full bg-orange-400 rounded" :style="{ width: selectedDevice.diskUsage + '%' }"></div>
                  </div>
                  <span class="text-[0.6vw] text-white">{{ selectedDevice.diskUsage }}%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 网络信息 -->
          <div class="bg-black/20 p-[0.6vw] rounded">
            <div class="text-[0.7vw] text-purple-400 mb-[0.4vw]">网络信息</div>
            <div class="space-y-[0.3vw]">
              <div class="flex justify-between">
                <span class="text-[0.6vw] text-gray-400">网络流量:</span>
                <span class="text-[0.6vw] text-white">{{ selectedDevice.networkTraffic }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-[0.6vw] text-gray-400">连接数:</span>
                <span class="text-[0.6vw] text-white">{{ selectedDevice.connections }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-[0.6vw] text-gray-400">延迟:</span>
                <span class="text-[0.6vw] text-white">{{ selectedDevice.latency }}ms</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex gap-[0.4vw] mt-[0.8vw]">
            <button class="flex-1 bg-blue-500 text-white text-[0.6vw] py-[0.3vw] rounded hover:bg-blue-600 transition-colors"> 远程连接 </button>
            <button class="flex-1 bg-green-500 text-white text-[0.6vw] py-[0.3vw] rounded hover:bg-green-600 transition-colors"> 重启设备 </button>
            <button class="flex-1 bg-orange-500 text-white text-[0.6vw] py-[0.3vw] rounded hover:bg-orange-600 transition-colors"> 配置管理 </button>
          </div>
        </div>
        <div v-else class="h-full flex items-center justify-center">
          <span class="text-[0.7vw] text-gray-400">请选择设备查看详情</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import {
    DesktopOutlined,
    UnorderedListOutlined,
    InfoCircleOutlined,
    DatabaseOutlined,
    CloudServerOutlined,
    WifiOutlined,
    SecurityScanOutlined,
    MonitorOutlined,
  } from '@ant-design/icons-vue';

  // IT设备统计数据
  const itStats = ref([
    { label: '服务器', value: '45台', valueClass: 'text-blue-400', icon: DatabaseOutlined, iconClass: 'text-blue-400' },
    { label: '网络设备', value: '28台', valueClass: 'text-green-400', icon: WifiOutlined, iconClass: 'text-green-400' },
    { label: '存储设备', value: '12台', valueClass: 'text-purple-400', icon: CloudServerOutlined, iconClass: 'text-purple-400' },
    { label: '安全设备', value: '8台', valueClass: 'text-orange-400', icon: SecurityScanOutlined, iconClass: 'text-orange-400' },
    { label: '在线率', value: '96.8%', valueClass: 'text-white', icon: MonitorOutlined, iconClass: 'text-white' },
  ]);

  // IT设备分类
  const itCategories = ref([
    {
      name: '服务器',
      count: 45,
      description: '物理服务器、虚拟机',
      icon: DatabaseOutlined,
      iconClass: 'text-blue-400',
    },
    {
      name: '网络设备',
      count: 28,
      description: '交换机、路由器、防火墙',
      icon: WifiOutlined,
      iconClass: 'text-green-400',
    },
    {
      name: '存储设备',
      count: 12,
      description: 'SAN、NAS、磁盘阵列',
      icon: CloudServerOutlined,
      iconClass: 'text-purple-400',
    },
    {
      name: '安全设备',
      count: 8,
      description: '入侵检测、防病毒网关',
      icon: SecurityScanOutlined,
      iconClass: 'text-orange-400',
    },
  ]);

  // 选中的分类
  const selectedCategory = ref('');

  // 选中的设备
  const selectedDevice = ref(null);

  // 模拟IT设备数据
  const itDevices = ref([
    // 服务器
    {
      id: 1,
      name: 'Web服务器-01',
      category: '服务器',
      status: 'online',
      location: '1楼-主机房-A01',
      model: 'Dell R740',
      ip: '************',
      mac: '00:1B:21:12:34:56',
      cpuUsage: 45,
      memoryUsage: 67,
      diskUsage: 32,
      networkTraffic: '1.2GB/s',
      connections: 1250,
      latency: 2,
    },
    {
      id: 2,
      name: '数据库服务器-01',
      category: '服务器',
      status: 'online',
      location: '1楼-主机房-A02',
      model: 'HP DL380',
      ip: '************',
      mac: '00:1B:21:12:34:57',
      cpuUsage: 78,
      memoryUsage: 85,
      diskUsage: 56,
      networkTraffic: '2.1GB/s',
      connections: 890,
      latency: 1,
    },
    {
      id: 3,
      name: '应用服务器-01',
      category: '服务器',
      status: 'offline',
      location: '1楼-主机房-A03',
      model: 'IBM x3650',
      ip: '************',
      mac: '00:1B:21:12:34:58',
      cpuUsage: 0,
      memoryUsage: 0,
      diskUsage: 0,
      networkTraffic: '0MB/s',
      connections: 0,
      latency: 0,
    },

    // 网络设备
    {
      id: 4,
      name: '核心交换机-01',
      category: '网络设备',
      status: 'online',
      location: '1楼-网络机房-N01',
      model: 'Cisco 9500',
      ip: '***********',
      mac: '00:1B:21:12:34:59',
      cpuUsage: 23,
      memoryUsage: 34,
      diskUsage: 12,
      networkTraffic: '5.6GB/s',
      connections: 2400,
      latency: 0.5,
    },
    {
      id: 5,
      name: '汇聚交换机-01',
      category: '网络设备',
      status: 'online',
      location: '2楼-网络机房-N02',
      model: 'Huawei S5700',
      ip: '***********',
      mac: '00:1B:21:12:34:60',
      cpuUsage: 18,
      memoryUsage: 28,
      diskUsage: 8,
      networkTraffic: '3.2GB/s',
      connections: 1800,
      latency: 1,
    },

    // 存储设备
    {
      id: 6,
      name: 'SAN存储-01',
      category: '存储设备',
      status: 'online',
      location: '1楼-存储机房-S01',
      model: 'EMC VNX5600',
      ip: '************',
      mac: '00:1B:21:12:34:61',
      cpuUsage: 35,
      memoryUsage: 45,
      diskUsage: 78,
      networkTraffic: '8.9GB/s',
      connections: 156,
      latency: 3,
    },

    // 安全设备
    {
      id: 7,
      name: '防火墙-01',
      category: '安全设备',
      status: 'online',
      location: '1楼-网络机房-F01',
      model: 'Fortinet 600D',
      ip: '*************',
      mac: '00:1B:21:12:34:62',
      cpuUsage: 42,
      memoryUsage: 38,
      diskUsage: 15,
      networkTraffic: '1.8GB/s',
      connections: 5600,
      latency: 2,
    },
  ]);

  // 根据选中分类过滤设备
  const filteredDevices = computed(() => {
    if (!selectedCategory.value) {
      return itDevices.value;
    }
    return itDevices.value.filter((device) => device.category === selectedCategory.value);
  });

  // 选择分类
  const selectCategory = (category) => {
    selectedCategory.value = selectedCategory.value === category ? '' : category;
    selectedDevice.value = null;
  };

  // 选择设备
  const selectDevice = (device) => {
    selectedDevice.value = device;
  };
</script>

<style scoped>
  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(21, 39, 77, 0.3);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.5);
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.7);
  }
</style>
