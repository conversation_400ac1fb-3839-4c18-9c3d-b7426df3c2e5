import { defHttp } from '/@/utils/http/axios';

/**
 * 资产机柜接口参数类型
 */
export interface AssetCabinetParams {
  pageNo?: number;
  pageSize?: number;
  id?: number;
  name?: string;
  code?: string;
  roomId?: number;
  totalU?: string;
  useU?: string;
  unuseU?: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
}

/**
 * 资产机柜数据类型
 */
export interface AssetCabinetModel {
  id: number;
  name: string;
  code: string;
  roomId: number;
  totalU: string;
  useU: string;
  unuseU: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
}

/**
 * 资产机柜列表响应类型
 */
export interface AssetCabinetListResult {
  records: AssetCabinetModel[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * API响应基础类型
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  code: number;
  result: T;
  timestamp: number;
}

/**
 * 资产机柜API接口
 */
enum Api {
  CABINET_LIST = '/assetCabinet',
  CABINET_GET = '/assetCabinet',
  CABINET_ADD = '/assetCabinet',
  CABINET_UPDATE = '/assetCabinet',
  CABINET_DELETE = '/assetCabinet',
}

/**
 * 获取资产机柜列表
 * @param params 查询参数
 * @returns 机柜列表数据
 */
export function getAssetCabinetList(params: AssetCabinetParams) {
  return defHttp.get<AssetCabinetListResult>({
    url: Api.CABINET_LIST,
    params,
  });
}

/**
 * 获取单个资产机柜详情
 * @param id 机柜ID
 * @returns 机柜详情数据
 */
export function getAssetCabinetById(id: number) {
  return defHttp.get<AssetCabinetModel>({
    url: `${Api.CABINET_GET}/${id}`,
  });
}

/**
 * 添加资产机柜
 * @param data 机柜数据
 * @returns 添加结果
 */
export function addAssetCabinet(data: Omit<AssetCabinetModel, 'id'>) {
  return defHttp.post<ApiResponse>({
    url: Api.CABINET_ADD,
    data,
  });
}

/**
 * 更新资产机柜
 * @param data 机柜数据
 * @returns 更新结果
 */
export function updateAssetCabinet(data: AssetCabinetModel) {
  return defHttp.put<ApiResponse>({
    url: Api.CABINET_UPDATE,
    data,
  });
}

/**
 * 删除资产机柜
 * @param idList 机柜ID列表
 * @returns 删除结果
 */
export function deleteAssetCabinet(idList: number[]) {
  return defHttp.delete<ApiResponse>({
    url: Api.CABINET_DELETE,
    params: { idList: idList.join(',') },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 机柜绑定设备参数类型
 */
export interface CabinetBindDeviceParams {
  assDeviceId: number;
  cabinetId: number;
  startU: number;
}

/**
 * 机柜绑定设备
 * @param data 绑定参数
 * @returns 绑定结果
 */
export function bindDeviceToCabinet(data: CabinetBindDeviceParams) {
  return defHttp.post<ApiResponse>({
    url: '/assetDeviceCabinet/add',
    data,
  });
}

/**
 * 机柜绑定的设备信息类型
 */
export interface CabinetDeviceInfo {
  id?: number;
  assDeviceId?: number; // 保留旧字段以兼容
  deviceId: string; // 实际API返回的字段名
  cabinetId: number;
  startU: number;
  endU?: number;
  deviceName: string;
  models?: string;
  deviceType?: string;
  uSize?: number;
  u?: string; // API返回的U位字符串，如 "1,2"
  createTime?: string;
  updateTime?: string;
}

/**
 * 根据机柜ID获取绑定的设备信息
 * @param cabinetId 机柜ID
 * @returns 设备信息列表
 */
export function getCabinetDevicesByCabinetId(cabinetId: number) {
  return defHttp.get<CabinetDeviceInfo[]>({
    url: '/assetDeviceCabinet/getByCabinetId',
    params: { cabinetId },
  });
}

/**
 * 解绑设备参数类型
 */
export interface UnbindDeviceParams {
  cabinetId: number;
  deviceId?: string; // 设备ID，多个用逗号分隔，如 "1,2,3"
}

/**
 * 解绑机柜设备
 * @param params 解绑参数
 * @returns 解绑结果
 */
export function unbindCabinetDevices(params: UnbindDeviceParams) {
  return defHttp.delete<ApiResponse>(
    {
      url: '/assetDeviceCabinet',
      params,
      // @ts-ignore: paramsInUrl 不在标准类型中，但用于特殊处理
      paramsInUrl: true, // 强制参数只在URL中，不在请求体中
    },
    {
      isTransformResponse: false,
    }
  );
}
