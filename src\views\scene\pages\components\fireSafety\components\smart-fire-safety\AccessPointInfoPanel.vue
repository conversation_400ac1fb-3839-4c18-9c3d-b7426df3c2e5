<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 门禁点位概览 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-door-open mr-[0.4vw] text-blue-400"></i>
        门禁点位概览
      </div>
      <div class="grid grid-cols-5 gap-[0.6vw]">
        <div v-for="stat in accessStats" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
          <div v-if="stat.status" class="text-[0.5vw] mt-[0.2vw]" :class="stat.statusClass">
            {{ stat.status }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：门禁点位列表 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-list mr-[0.4vw] text-blue-400"></i>
            门禁点位列表
          </div>
          <div class="flex gap-[0.4vw]">
            <button
              v-for="filter in statusFilters"
              :key="filter.key"
              @click="activeFilter = filter.key"
              :class="[
                'px-[0.6vw] py-[0.2vw] rounded text-[0.6vw] transition-all',
                activeFilter === filter.key ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-gray-300 hover:bg-black/30',
              ]"
            >
              {{ filter.label }}
            </button>
          </div>
        </div>

        <div class="flex-1 overflow-y-auto custom-scrollbar">
          <div class="space-y-[0.4vw]">
            <div
              v-for="point in filteredAccessPoints"
              :key="point.id"
              class="bg-[#15274D]/30 p-[0.6vw] rounded hover:bg-[#15274D]/50 transition-all cursor-pointer"
              @click="selectAccessPoint(point)"
            >
              <div class="flex items-center justify-between mb-[0.3vw]">
                <div class="flex items-center">
                  <i :class="point.icon" class="mr-[0.6vw] text-blue-400"></i>
                  <span class="text-[0.65vw] text-white font-medium">{{ point.name }}</span>
                </div>
                <span class="text-[0.6vw]" :class="getStatusClass(point.status)">
                  {{ getStatusText(point.status) }}
                </span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400 mb-[0.3vw]">
                <span>位置：{{ point.location }}</span>
                <span>类型：{{ point.type }}</span>
              </div>
              <div class="flex justify-between text-[0.6vw]">
                <span class="text-gray-400">今日通行：{{ point.todayCount }}次</span>
                <span class="text-gray-400">最后活动：{{ point.lastActivity }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：点位详情和统计 -->
      <div class="w-[40%] flex flex-col gap-[0.8vw]">
        <!-- 选中点位详情 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-info-circle mr-[0.4vw] text-blue-400"></i>
            点位详情
          </div>

          <div v-if="selectedPoint" class="space-y-[0.4vw]">
            <div class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="text-[0.6vw] text-white font-medium mb-[0.2vw]">{{ selectedPoint.name }}</div>
              <div class="text-[0.5vw] text-gray-400 mb-[0.3vw]">{{ selectedPoint.description }}</div>
              <div class="grid grid-cols-2 gap-[0.3vw] text-[0.5vw]">
                <div class="text-gray-400"
                  >设备ID：<span class="text-white">{{ selectedPoint.deviceId }}</span></div
                >
                <div class="text-gray-400"
                  >IP地址：<span class="text-white">{{ selectedPoint.ipAddress }}</span></div
                >
                <div class="text-gray-400"
                  >安装时间：<span class="text-white">{{ selectedPoint.installDate }}</span></div
                >
                <div class="text-gray-400"
                  >维护周期：<span class="text-white">{{ selectedPoint.maintenanceCycle }}</span></div
                >
              </div>
            </div>

            <div class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="text-[0.6vw] text-white mb-[0.3vw]">实时状态</div>
              <div class="space-y-[0.2vw] text-[0.5vw]">
                <div class="flex justify-between">
                  <span class="text-gray-400">连接状态</span>
                  <span :class="getStatusClass(selectedPoint.status)">{{ getStatusText(selectedPoint.status) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-400">信号强度</span>
                  <span class="text-green-400">{{ selectedPoint.signalStrength }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-400">电池电量</span>
                  <span class="text-green-400">{{ selectedPoint.batteryLevel }}</span>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="text-center text-gray-400 text-[0.6vw] py-[2vw]"> 请选择一个门禁点位查看详情 </div>
        </div>

        <!-- 通行统计 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-chart-line mr-[0.4vw] text-blue-400"></i>
            通行统计
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="stat in accessStatistics" :key="stat.period" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ stat.period }}</span>
                <span class="text-[0.6vw] text-blue-400">{{ stat.count }}次</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>进入：{{ stat.inCount }}次</span>
                <span>离开：{{ stat.outCount }}次</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 异常记录 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-exclamation-triangle mr-[0.4vw] text-blue-400"></i>
            异常记录
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="record in anomalyRecords" :key="record.id" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ record.time }}</span>
                <span class="text-[0.5vw]" :class="getAnomalyLevelClass(record.level)">{{ record.level }}</span>
              </div>
              <div class="text-[0.5vw] text-gray-400">{{ record.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  // 门禁点位概览数据
  const accessStats = ref([
    {
      label: '总点位数',
      value: '24',
      valueClass: 'text-blue-400',
      status: '正常',
      statusClass: 'text-green-400',
    },
    {
      label: '在线点位',
      value: '23',
      valueClass: 'text-green-400',
      status: '正常',
      statusClass: 'text-green-400',
    },
    {
      label: '离线点位',
      value: '1',
      valueClass: 'text-yellow-400',
      status: '维护中',
      statusClass: 'text-yellow-400',
    },
    {
      label: '今日通行',
      value: '1,256',
      valueClass: 'text-blue-400',
      status: '正常',
      statusClass: 'text-green-400',
    },
    {
      label: '异常次数',
      value: '0',
      valueClass: 'text-green-400',
      status: '正常',
      statusClass: 'text-green-400',
    },
  ]);

  // 状态筛选器
  const statusFilters = ref([
    { key: 'all', label: '全部' },
    { key: 'online', label: '在线' },
    { key: 'offline', label: '离线' },
    { key: 'maintenance', label: '维护' },
  ]);

  const activeFilter = ref('all');

  // 门禁点位数据
  const accessPoints = ref([
    {
      id: 1,
      name: '主入口门禁',
      location: '1号楼主入口',
      type: '刷卡门禁',
      status: 'online',
      icon: 'fas fa-door-open',
      todayCount: 456,
      lastActivity: '2分钟前',
      deviceId: 'AC001',
      ipAddress: '*************',
      installDate: '2023-06-15',
      maintenanceCycle: '6个月',
      signalStrength: '95%',
      batteryLevel: '98%',
      description: '主要出入口门禁系统，支持刷卡和人脸识别',
    },
    {
      id: 2,
      name: '员工通道门禁',
      location: '1号楼侧门',
      type: '人脸识别',
      status: 'online',
      icon: 'fas fa-user-check',
      todayCount: 234,
      lastActivity: '5分钟前',
      deviceId: 'AC002',
      ipAddress: '*************',
      installDate: '2023-06-15',
      maintenanceCycle: '6个月',
      signalStrength: '92%',
      batteryLevel: '95%',
      description: '员工专用通道，支持人脸识别和指纹验证',
    },
    {
      id: 3,
      name: '访客登记门禁',
      location: '接待大厅',
      type: '访客登记',
      status: 'online',
      icon: 'fas fa-clipboard-check',
      todayCount: 89,
      lastActivity: '10分钟前',
      deviceId: 'AC003',
      ipAddress: '*************',
      installDate: '2023-06-15',
      maintenanceCycle: '6个月',
      signalStrength: '88%',
      batteryLevel: '92%',
      description: '访客登记和临时卡发放系统',
    },
    {
      id: 4,
      name: '车库入口门禁',
      location: '地下车库入口',
      type: '车牌识别',
      status: 'maintenance',
      icon: 'fas fa-car',
      todayCount: 0,
      lastActivity: '2小时前',
      deviceId: 'AC004',
      ipAddress: '*************',
      installDate: '2023-06-15',
      maintenanceCycle: '6个月',
      signalStrength: '0%',
      batteryLevel: '0%',
      description: '车库入口车牌识别系统，当前维护中',
    },
  ]);

  // 筛选后的门禁点位
  const filteredAccessPoints = computed(() => {
    if (activeFilter.value === 'all') {
      return accessPoints.value;
    }
    return accessPoints.value.filter((point) => point.status === activeFilter.value);
  });

  // 选中的门禁点位
  const selectedPoint = ref(null);

  // 通行统计
  const accessStatistics = ref([
    { period: '今日', count: 1256, inCount: 628, outCount: 628 },
    { period: '昨日', count: 1189, inCount: 595, outCount: 594 },
    { period: '本周', count: 8456, inCount: 4228, outCount: 4228 },
    { period: '本月', count: 35678, inCount: 17839, outCount: 17839 },
  ]);

  // 异常记录
  const anomalyRecords = ref([
    { id: 1, time: '2024-02-28 14:30', level: '正常', description: '所有门禁点位运行正常' },
    { id: 2, time: '2024-02-28 10:15', level: '正常', description: '定期系统自检完成' },
    { id: 3, time: '2024-02-28 08:45', level: '正常', description: '夜间模式切换正常' },
  ]);

  // 选择门禁点位
  const selectAccessPoint = (point) => {
    selectedPoint.value = point;
  };

  // 获取状态样式
  const getStatusClass = (status) => {
    switch (status) {
      case 'online':
        return 'text-green-400';
      case 'offline':
        return 'text-red-400';
      case 'maintenance':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取状态文本
  const getStatusText = (status) => {
    switch (status) {
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'maintenance':
        return '维护中';
      default:
        return '未知';
    }
  };

  // 获取异常等级样式
  const getAnomalyLevelClass = (level) => {
    switch (level) {
      case '正常':
        return 'text-green-400';
      case '警告':
        return 'text-yellow-400';
      case '严重':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
