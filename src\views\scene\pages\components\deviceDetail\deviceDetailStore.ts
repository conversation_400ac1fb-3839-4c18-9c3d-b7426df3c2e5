import { defineStore } from 'pinia/dist/pinia';
import { ref } from 'vue';
import { getDeviceDetailByCode, type DeviceDetailData } from '/@/api/scene'; // 导入API函数和类型

// 折线图面板接口
export interface ChartPanel {
  id: string;
  deviceId: string;
  deviceType: number;
  deviceName: string;
  deviceRemark: string;
  position: { x: number; y: number };
  sourcePosition: 'left' | 'right'; // 来源位置，用于计算弹窗位置
}

// 简化的设备详情状态管理
export const useDeviceDetailStore = defineStore('deviceDetail', () => {
  // 状态
  const deviceData = ref<DeviceDetailData[] | null>(null); // 使用导入的类型
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 折线图面板状态
  const chartPanels = ref<ChartPanel[]>([]);

  // 加载设备数据
  const loadDeviceData = async (code: string): Promise<boolean> => {
    if (!code) {
      console.error('[DeviceDetailStore] 无效的设备编码');
      error.value = '无效的设备编码';
      return false;
    }

    loading.value = true;
    error.value = null;
    deviceData.value = null; // 清除旧数据

    try {
      console.log(`[DeviceDetailStore] 开始加载设备数据: ${code}`);
      const response = await getDeviceDetailByCode(code); // 使用导入的API函数

      console.log('[DeviceDetailStore] 接收到API响应:', response);

      if (response && Array.isArray(response)) {
        if (response.length > 0) {
          console.log('[DeviceDetailStore] 设置数据到store...');
          deviceData.value = response.map((item) => ({
            ...item,
            id: item.id || null,
            remark: item.remark,
            valueData: String(item.valueData),
          }));
          console.log(`[DeviceDetailStore] 成功加载设备数据，共 ${deviceData.value.length} 条记录`);
          return true;
        } else {
          console.warn('[DeviceDetailStore] API返回的数据为空数组:', response);
          error.value = '获取设备数据失败: 返回数据为空';
          deviceData.value = null;
          return false;
        }
      } else {
        console.warn('[DeviceDetailStore] API返回的数据格式异常或为null:', response);
        error.value = '获取设备数据失败: 返回数据格式异常';
        deviceData.value = null;
        return false;
      }
    } catch (err) {
      console.error('[DeviceDetailStore] 获取设备数据出错:', err);
      error.value = '获取设备数据出错';
      deviceData.value = null;
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 清除设备数据
  const clearDeviceData = () => {
    deviceData.value = null;
    error.value = null;
    loading.value = false;
  };

  // 计算弹窗位置
  const calculatePopupPosition = (sourcePosition: 'left' | 'right', existingPanels: ChartPanel[]) => {
    const baseOffset = 50; // 基础偏移量
    const panelWidth = window.innerWidth * 0.22; // 22vw
    const panelHeight = window.innerHeight * 0.16; // 16vw

    // 计算已有面板的数量，用于错开显示
    const existingCount = existingPanels.length;
    const offsetMultiplier = existingCount * 30; // 每个面板错开30px

    let x: number, y: number;

    if (sourcePosition === 'left') {
      // 左侧栏，弹窗显示在右侧
      x = window.innerWidth * 0.2 + baseOffset + offsetMultiplier; // 左侧栏宽度 + 偏移
      y = baseOffset + offsetMultiplier;
    } else {
      // 右侧栏，弹窗显示在左侧
      x = window.innerWidth * 0.8 - panelWidth - baseOffset - offsetMultiplier; // 右侧栏左边界 - 面板宽度 - 偏移
      y = baseOffset + offsetMultiplier;
    }

    // 确保弹窗不超出屏幕边界
    x = Math.max(0, Math.min(x, window.innerWidth - panelWidth));
    y = Math.max(0, Math.min(y, window.innerHeight - panelHeight));

    return { x, y };
  };

  // 添加折线图面板（每次只能展示一个）
  const addChartPanel = (deviceItem: DeviceDetailData, sourcePosition: 'left' | 'right') => {
    const deviceId = deviceItem.id?.toString() || '';
    const deviceType = deviceItem.type;

    // 清除所有现有面板，确保只显示一个
    chartPanels.value = [];

    const panelId = `${deviceId}-${deviceType}-${Date.now()}`;
    const position = calculatePopupPosition(sourcePosition, []);

    const newPanel: ChartPanel = {
      id: panelId,
      deviceId,
      deviceType,
      deviceName: deviceItem.name,
      deviceRemark: deviceItem.remark,
      position,
      sourcePosition,
    };

    chartPanels.value.push(newPanel);

    // 开发环境日志
    if (process.env.NODE_ENV === 'development') {
      console.log('[DeviceDetailStore] 创建新的折线图面板:', panelId);
    }

    return panelId;
  };

  // 移除折线图面板
  const removeChartPanel = (panelId: string) => {
    const index = chartPanels.value.findIndex((panel: ChartPanel) => panel.id === panelId);
    if (index > -1) {
      chartPanels.value.splice(index, 1);
    }
  };

  // 清除所有折线图面板
  const clearChartPanels = () => {
    chartPanels.value = [];
  };

  // 根据来源位置获取折线图面板
  const getChartPanelsBySourcePosition = (sourcePosition: 'left' | 'right') => {
    return chartPanels.value.filter((panel: ChartPanel) => panel.sourcePosition === sourcePosition);
  };

  return {
    deviceData,
    loading,
    error,
    chartPanels,
    loadDeviceData,
    clearDeviceData,
    addChartPanel,
    removeChartPanel,
    clearChartPanels,
    getChartPanelsBySourcePosition,
  };
});
