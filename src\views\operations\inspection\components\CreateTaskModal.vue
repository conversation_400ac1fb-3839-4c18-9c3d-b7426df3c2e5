<template>
  <a-modal v-model:visible="visible" title="创建巡检任务" width="700px" @ok="handleSubmit" @cancel="handleCancel">
    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="任务标题" name="title">
            <a-input v-model:value="formData.title" placeholder="请输入任务标题" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="任务类型" name="type">
            <a-select v-model:value="formData.type" placeholder="选择任务类型">
              <a-select-option value="routine">例行巡检</a-select-option>
              <a-select-option value="special">专项巡检</a-select-option>
              <a-select-option value="emergency">应急巡检</a-select-option>
              <a-select-option value="maintenance">维护巡检</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="优先级" name="priority">
            <a-select v-model:value="formData.priority" placeholder="选择优先级">
              <a-select-option value="low">低</a-select-option>
              <a-select-option value="medium">中</a-select-option>
              <a-select-option value="high">高</a-select-option>
              <a-select-option value="urgent">紧急</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="执行人" name="assigneeId">
            <a-select v-model:value="formData.assigneeId" placeholder="选择执行人">
              <a-select-option value="1">张三</a-select-option>
              <a-select-option value="2">李四</a-select-option>
              <a-select-option value="3">王五</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="任务描述" name="description">
        <a-textarea v-model:value="formData.description" placeholder="请输入任务描述" :rows="3" />
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="位置" name="location">
            <a-input v-model:value="formData.location" placeholder="位置" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="楼层" name="floor">
            <a-select v-model:value="formData.floor" placeholder="选择楼层">
              <a-select-option value="1F">1F</a-select-option>
              <a-select-option value="2F">2F</a-select-option>
              <a-select-option value="3F">3F</a-select-option>
              <a-select-option value="B1">B1</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="区域" name="area">
            <a-input v-model:value="formData.area" placeholder="区域" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="计划执行时间" name="scheduledTime">
        <a-date-picker
          v-model:value="formData.scheduledTime"
          show-time
          format="YYYY-MM-DD HH:mm"
          placeholder="选择计划执行时间"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="检查设备">
        <a-select v-model:value="formData.deviceIds" mode="multiple" placeholder="选择需要检查的设备" style="width: 100%">
          <a-select-option value="1">空调设备-001</a-select-option>
          <a-select-option value="2">消防设备-002</a-select-option>
          <a-select-option value="3">电力设备-003</a-select-option>
          <a-select-option value="4">网络设备-004</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import dayjs, { Dayjs } from 'dayjs';
  import { createInspectionTask } from '/@/api/operations/inspection';

  interface Props {
    visible: boolean;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{ 'update:visible': [value: boolean]; success: [] }>();

  const formRef = ref();
  const formData = reactive({
    title: '',
    type: '',
    priority: 'medium',
    assigneeId: null as number | null,
    description: '',
    location: '',
    floor: '',
    area: '',
    scheduledTime: null as Dayjs | null,
    deviceIds: [] as number[],
  });

  const rules = {
    title: [{ required: true, message: '请输入任务标题', trigger: 'blur' }],
    type: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
    priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
    assigneeId: [{ required: true, message: '请选择执行人', trigger: 'change' }],
    description: [{ required: true, message: '请输入任务描述', trigger: 'blur' }],
    location: [{ required: true, message: '请输入位置', trigger: 'blur' }],
    floor: [{ required: true, message: '请选择楼层', trigger: 'change' }],
    area: [{ required: true, message: '请输入区域', trigger: 'blur' }],
    scheduledTime: [{ required: true, message: '请选择计划执行时间', trigger: 'change' }],
  };

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const handleSubmit = async () => {
    try {
      await formRef.value.validate();

      await createInspectionTask({
        title: formData.title,
        type: formData.type as any,
        priority: formData.priority,
        assigneeId: formData.assigneeId!,
        description: formData.description,
        location: formData.location,
        floor: formData.floor,
        area: formData.area,
        deviceIds: formData.deviceIds,
        checkPointIds: [], // 这里应该根据设备选择相应的检查点
        scheduledTime: formData.scheduledTime!.format('YYYY-MM-DD HH:mm:ss'),
      });

      message.success('巡检任务创建成功');
      emit('success');
    } catch (error) {
      message.error('创建失败');
    }
  };

  const handleCancel = () => {
    visible.value = false;
  };
</script>
