/* 3D场景运营数据管理系统 - 标题栏统一样式 */

/* 基础标题栏样式 */
.title-header {
  height: 1.6vw;
  flex-shrink: 0;
  position: relative;
  min-height: 24px; /* 最小高度确保在小屏幕上可见 */
}

/* 背景图片样式 */
.title-header-bg {
  width: auto;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  object-fit: contain;
}

/* 标题文字样式 */
.title-header-text {
  position: absolute;
  left: 1vw;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  font-size: 0.7vw;
  font-weight: 500;
  z-index: 10;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  white-space: nowrap;
  min-font-size: 12px; /* 最小字体大小 */
}

/* 详情管理按钮样式 */
.title-header-button {
  position: absolute;
  right: 0.5vw;
  top: 50%;
  transform: translateY(-50%);
  padding: 0.25vw 0.6vw;
  background: rgba(59, 142, 230, 0.2);
  border: 1px solid rgba(59, 142, 230, 0.3);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.6vw;
  color: #3b8ee6;
  z-index: 10;
  white-space: nowrap;
  min-font-size: 10px; /* 最小字体大小 */
}

.title-header-button:hover {
  background: rgba(59, 142, 230, 0.3);
  border-color: rgba(59, 142, 230, 0.5);
  transform: translateY(-50%) scale(1.02);
}

/* 响应式设计 */
@media (max-width: 1920px) {
  .title-header-text {
    left: 0.8vw;
    font-size: 0.65vw;
  }

  .title-header-button {
    font-size: 0.55vw;
    padding: 0.2vw 0.5vw;
  }
}

@media (max-width: 1366px) {
  .title-header-text {
    left: 0.6vw;
    font-size: 0.6vw;
  }

  .title-header-button {
    font-size: 0.5vw;
    padding: 0.2vw 0.5vw;
    right: 0.4vw;
  }
}

@media (max-width: 1024px) {
  .title-header {
    height: 2.5vw;
    min-height: 32px;
  }

  .title-header-text {
    font-size: 1vw;
    left: 0.5vw;
  }

  .title-header-button {
    font-size: 0.9vw;
    padding: 0.3vw 0.6vw;
    right: 0.3vw;
  }
}

@media (max-width: 768px) {
  .title-header {
    height: 3vw;
    min-height: 40px;
  }

  .title-header-text {
    font-size: 1.2vw;
    left: 0.4vw;
  }

  .title-header-button {
    font-size: 1vw;
    padding: 0.4vw 0.8vw;
    right: 0.2vw;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .title-header-text {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
    font-weight: 600;
  }

  .title-header-button {
    border-width: 2px;
    background: rgba(59, 142, 230, 0.3);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .title-header-button {
    transition: none;
  }

  .title-header-button:hover {
    transform: translateY(-50%);
  }
}

/* 暗色主题优化 */
@media (prefers-color-scheme: dark) {
  .title-header-text {
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
  }

  .title-header-button {
    background: rgba(59, 142, 230, 0.25);
    border-color: rgba(59, 142, 230, 0.4);
    color: #4a9eff;
  }

  .title-header-button:hover {
    background: rgba(59, 142, 230, 0.35);
    border-color: rgba(59, 142, 230, 0.6);
  }
}

/* 确保文字在各种背景下都清晰可见 */
.title-header-text,
.title-header-button {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 防止文字选择 */
.title-header-text,
.title-header-button {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 确保按钮在触摸设备上有足够的点击区域 */
@media (pointer: coarse) {
  .title-header-button {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
