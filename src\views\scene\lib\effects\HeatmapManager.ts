import * as THREE from 'three';
import { ModelLoaderManager } from '../load/ModelLoaderManager';
import { SceneManager } from '../SceneManager';
import { useGlobalThreeStore } from '../../store/globalThreeStore';
import { gsap } from 'gsap';
import { containsDevice } from '../../utils/deviceIdentifier';

// 定义热力图效果类型
type HeatmapEffectType = 'temperature' | 'power' | 'load' | 'network' | 'alarm' | 'cabinet_occupancy' | 'device_weight';

// 材质备份接口
interface MaterialBackup {
  material?: THREE.Material;
  color?: THREE.Color;
  emissive?: THREE.Color;
  emissiveIntensity?: number;
  opacity?: number;
  transparent?: boolean;
  depthWrite?: boolean;
  map?: THREE.Texture | null;
  normalMap?: THREE.Texture | null;
  roughnessMap?: THREE.Texture | null;
  metalnessMap?: THREE.Texture | null;
  aoMap?: THREE.Texture | null;
  emissiveMap?: THREE.Texture | null;
  colorSpace?: THREE.ColorSpace;
  mapRepeat?: { x: number; y: number };
  mapOffset?: { x: number; y: number };
}

// 材质池接口
interface MaterialPool {
  temperature: Map<string, THREE.MeshStandardMaterial>;
  power: Map<string, THREE.MeshStandardMaterial>;
  load: Map<string, THREE.MeshStandardMaterial>;
  network: Map<string, THREE.MeshStandardMaterial>;
  alarm: Map<string, THREE.MeshStandardMaterial>;
  cabinet_occupancy: Map<string, THREE.MeshStandardMaterial>;
  device_weight: Map<string, THREE.MeshStandardMaterial>;
}

// 扩展材质接口
interface ExtendedMaterial extends THREE.Material {
  color: THREE.Color;
  userData: {
    isHeatmapMaterial?: boolean;
    heatmapValue?: number;
    effectType?: string;
    needsUpdate?: boolean;
    animFrameId?: number;
  };
  map?: THREE.Texture;
  normalMap?: THREE.Texture;
  roughnessMap?: THREE.Texture;
  metalnessMap?: THREE.Texture;
  aoMap?: THREE.Texture;
  emissiveMap?: THREE.Texture;
}

// 添加材质类型守卫函数
function isStandardMaterial(material: THREE.Material): material is THREE.MeshStandardMaterial {
  return material && 'color' in material && 'emissive' in material && 'emissiveIntensity' in material;
}

function hasColorProperty(material: THREE.Material): boolean {
  return 'color' in material;
}

function hasEmissiveProperty(material: THREE.Material): boolean {
  return 'emissive' in material;
}

interface HeatmapOptions {
  deviceOnly?: boolean;
}

export class HeatmapManager {
  private static instance: HeatmapManager | null = null;
  private modelLoader!: ModelLoaderManager;
  private scene!: THREE.Scene;
  private currentEffect: HeatmapEffectType | null = null;
  private originalMaterials!: Map<string, MaterialBackup>;
  private affectedMeshes!: THREE.Mesh[];
  private animationFrameIds!: Set<number>;
  private materialUpdateThrottle!: number;
  private lastMaterialUpdate!: number;
  private materialPool!: MaterialPool;
  private globalMaterialBackup!: Map<string, MaterialBackup>;
  private hasTextureBackup!: boolean;
  private isTransitioning!: boolean;
  private pendingEffect: HeatmapEffectType | null = null;
  private transitionPromise: Promise<void> | null = null;
  private alarmMaterials?: Set<THREE.Material>;
  private uuidCache?: Map<string, THREE.Object3D>;

  constructor() {
    if (HeatmapManager.instance) {
      return HeatmapManager.instance;
    }

    this.modelLoader = ModelLoaderManager.getInstance();
    this.scene = SceneManager.getInstance().scene;
    this.currentEffect = null;
    this.originalMaterials = new Map();
    this.affectedMeshes = [];
    this.animationFrameIds = new Set();
    this.materialUpdateThrottle = 100;
    this.lastMaterialUpdate = 0;

    this.materialPool = {
      temperature: new Map(),
      power: new Map(),
      load: new Map(),
      network: new Map(),
      alarm: new Map(),
      cabinet_occupancy: new Map(),
      device_weight: new Map(),
    };

    this.globalMaterialBackup = new Map();
    this.hasTextureBackup = false;
    this.isTransitioning = false;
    this.pendingEffect = null;
    this.transitionPromise = null;

    HeatmapManager.instance = this;
  }

  public static getInstance(): HeatmapManager {
    if (!HeatmapManager.instance) {
      HeatmapManager.instance = new HeatmapManager();
    }
    return HeatmapManager.instance;
  }

  private isInFloorView(): boolean {
    const globalThreeStore = useGlobalThreeStore();
    return globalThreeStore.currentFloorId !== null;
  }

  public async applyHeatmapAsync(type: HeatmapEffectType, _options: HeatmapOptions = {}): Promise<boolean> {
    console.log(`[HeatmapManager] 异步应用热力图: ${type}, 当前效果: ${this.currentEffect}, 是否过渡中: ${this.isTransitioning}`);

    return new Promise((resolve, reject) => {
      try {
        // 检查是否有其他热力图管理器处于活动状态
        this.checkOtherHeatmapManagers();

        const result = this.applyHeatmap(type);
        if (this.isTransitioning && this.transitionPromise) {
          console.log(`[HeatmapManager] 等待热力图过渡完成...`);
          this.transitionPromise.then(() => resolve(result));
        } else {
          requestAnimationFrame(() => {
            setTimeout(() => resolve(result), 50);
          });
        }
      } catch (error) {
        console.error(`[HeatmapManager] 应用热力图失败:`, error);
        reject(error);
      }
    });
  }

  // 检查其他热力图管理器是否处于活动状态
  private checkOtherHeatmapManagers(): void {
    try {
      // 检查温度分布热力图管理器
      const tempManager = (window as any).HeatmapJSManager || (globalThis as any).HeatmapJSManager;

      if (tempManager && typeof tempManager.getInstance === 'function') {
        const instance = tempManager.getInstance();
        if (instance && instance.getStatus && instance.getStatus().active) {
          console.warn('[HeatmapManager] 检测到温度分布热力图处于活动状态，这可能导致冲突');
        }
      }
    } catch (error) {
      console.warn('[HeatmapManager] 检查其他热力图管理器时出错:', error);
    }
  }

  private applyHeatmap(type: HeatmapEffectType): boolean {
    console.log(`开始应用热力图: ${type}，当前效果: ${this.currentEffect}`);

    if (this.isTransitioning) {
      console.log('热力图效果切换正在进行中，缓存新请求');
      this.pendingEffect = type;
      return true;
    }

    if (this.currentEffect === type) {
      this.clearHeatmap();
      return true;
    }

    this.isTransitioning = true;

    this.transitionPromise = new Promise((resolve) => {
      if (this.currentEffect) {
        this.clearHeatmap(true);
      }

      setTimeout(() => {
        this.currentEffect = type;
        const _deviceCount = this.applyToDevices(type);
        this.isTransitioning = false;
        resolve();
        this._checkPendingEffect();
      }, 50);
    });

    return true;
  }

  private applyToDevices(type: HeatmapEffectType): number {
    let deviceCount = 0;
    const deviceMeshes = new Set<THREE.Mesh>();

    // 记录处理的设备和跳过的设备，用于调试
    const processedDevices: string[] = [];
    const skippedDevices: string[] = [];

    console.log(`[HeatmapManager] 开始应用热力图效果到设备，类型: ${type}`);

    this.modelLoader.getCurrentModels().forEach((model) => {
      if (model.userData.type !== 'floor') return;

      console.log(`[HeatmapManager] 检查模型: ${model.name}, 类型: ${model.userData.type}`);

      model.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh) {
          const meshChild = child as THREE.Mesh;
          const material = meshChild.material as ExtendedMaterial | ExtendedMaterial[];
          // 使用严格的设备识别逻辑，只处理以楼层标识开头的设备 - 使用动画循环优化版本
          const isDevice = containsDevice(meshChild, true);

          // 添加调试信息，特别关注F1开头的设备
          if (meshChild.name.startsWith('F1_')) {
            console.log(`[HeatmapManager] 检查设备: ${meshChild.name}, 是设备: ${isDevice}, 父对象: ${meshChild.parent?.name || '无'}`);
          }

          if (isDevice) {
            deviceCount++;
            deviceMeshes.add(meshChild);
            processedDevices.push(meshChild.name);

            // 保存原始材质
            if (!this.originalMaterials.has(meshChild.uuid)) {
              if (!Array.isArray(material) && material && !material.userData?.isHeatmapMaterial) {
                const materialBackup = this.createMaterialBackup(material);
                this.originalMaterials.set(meshChild.uuid, materialBackup);
                this.globalMaterialBackup.set(meshChild.uuid, materialBackup);
                this.hasTextureBackup = true;
              }
            }

            this.affectedMeshes.push(meshChild);

            const value = this.getValueForEffect(type, meshChild);
            const heatmapMaterial = this.createHeatmapMaterial(value, type);
            meshChild.material = heatmapMaterial;

            // 强制材质更新
            if ('needsUpdate' in heatmapMaterial) {
              (heatmapMaterial as THREE.MeshStandardMaterial).needsUpdate = true;
            }
          } else {
            // 记录跳过的设备，但限制数量以避免日志过多
            if (skippedDevices.length < 20) {
              skippedDevices.push(meshChild.name);
            }
          }
        }
      });
    });

    // 输出调试信息
    console.log(`[HeatmapManager] 热力图效果应用完成，处理了 ${deviceCount} 个设备`);
    if (processedDevices.length > 0) {
      console.log(`[HeatmapManager] 处理的设备(部分): ${processedDevices.slice(0, 10).join(', ')}${processedDevices.length > 10 ? '...' : ''}`);
    }
    if (skippedDevices.length > 0) {
      console.log(`[HeatmapManager] 跳过的设备(部分): ${skippedDevices.slice(0, 10).join(', ')}${skippedDevices.length > 10 ? '...' : ''}`);
    }

    // 强制场景重新渲染
    this.forceSceneRender();

    return deviceCount;
  }

  // 添加强制场景渲染方法
  private forceSceneRender(): void {
    try {
      const sceneManager = SceneManager.getInstance();
      if (sceneManager && sceneManager.renderer) {
        sceneManager.needsRender = true;
        // 连续渲染几帧确保效果显示
        for (let i = 0; i < 5; i++) {
          setTimeout(() => {
            if (sceneManager) {
              sceneManager.needsRender = true;
              sceneManager.render();
            }
          }, i * 50);
        }
      }
    } catch (error) {
      console.error(`[HeatmapManager] 强制渲染时出错:`, error);
    }
  }

  private registerAlarmMaterial(material: THREE.MeshStandardMaterial): void {
    if (!this.alarmMaterials) {
      this.alarmMaterials = new Set();
      this.startBatchAlarmUpdate();
    }
    this.alarmMaterials.add(material);
  }

  private startBatchAlarmUpdate(): void {
    const updateAllAlarms = () => {
      const now = Date.now();
      if (now - this.lastMaterialUpdate > this.materialUpdateThrottle) {
        this.lastMaterialUpdate = now;

        const pulse = (now % 2000) / 2000;
        const intensity = 0.7 + 0.3 * Math.sin(pulse * Math.PI * 2);

        this.alarmMaterials?.forEach((material) => {
          if (material.userData?.needsUpdate && isStandardMaterial(material)) {
            material.emissive.setRGB(intensity, 0, 0);
            material.color.setRGB(intensity, 0, 0);
          }
        });
      }

      if (this.alarmMaterials && this.alarmMaterials.size > 0) {
        const frameId = requestAnimationFrame(updateAllAlarms);
        this.animationFrameIds.add(frameId);
      }
    };

    updateAllAlarms();
  }

  private performFinalMaterialCheck(): void {
    if (!this.modelLoader) return;

    console.log('执行最终材质恢复检查');

    this.modelLoader.getCurrentModels().forEach((model) => {
      if (!model) return;

      model.traverse((object: THREE.Object3D) => {
        if (object instanceof THREE.Mesh && object.material) {
          const globalBackup = this.globalMaterialBackup?.get(object.uuid);
          if (globalBackup) {
            this.restoreFromGlobalBackup(object, globalBackup);
          }
        }
      });
    });

    console.log('全局材质恢复检查完成');
  }

  private restoreFromGlobalBackup(object: THREE.Mesh, backup: MaterialBackup): void {
    if (Array.isArray(object.material)) return;

    if (backup.material) {
      object.material = backup.material;
    } else {
      const material = object.material as ExtendedMaterial;

      if (backup.color && hasColorProperty(material)) {
        (material as THREE.MeshStandardMaterial).color.copy(backup.color);
      }

      if (backup.emissive && hasEmissiveProperty(material)) {
        const stdMaterial = material as THREE.MeshStandardMaterial;
        stdMaterial.emissive.copy(backup.emissive);
        stdMaterial.emissiveIntensity = backup.emissiveIntensity || 0;
      }

      if (this.hasTextureBackup && isStandardMaterial(material)) {
        this.safelyRestoreTexture(material, 'map', backup.map || null);
        this.safelyRestoreTexture(material, 'normalMap', backup.normalMap || null);
        this.safelyRestoreTexture(material, 'roughnessMap', backup.roughnessMap || null);
        this.safelyRestoreTexture(material, 'metalnessMap', backup.metalnessMap || null);
        this.safelyRestoreTexture(material, 'aoMap', backup.aoMap || null);
        this.safelyRestoreTexture(material, 'emissiveMap', backup.emissiveMap || null);
      }

      material.opacity = backup.opacity || 1;
      if ('transparent' in material) {
        (material as THREE.MeshStandardMaterial).transparent = backup.transparent || false;
      }
      if ('depthWrite' in material) {
        (material as THREE.MeshStandardMaterial).depthWrite = backup.depthWrite !== undefined ? backup.depthWrite : true;
      }
      if ('needsUpdate' in material) {
        (material as THREE.MeshStandardMaterial).needsUpdate = true;
      }
    }

    object.visible = true;
  }

  private _checkPendingEffect(): void {
    if (this.pendingEffect !== null) {
      const pendingType = this.pendingEffect;
      this.pendingEffect = null;
      console.log(`应用待处理的热力图效果: ${pendingType}`);
      setTimeout(() => {
        this.applyHeatmap(pendingType);
      }, 50);
    }
  }

  private getValueForEffect(type: HeatmapEffectType, mesh: THREE.Mesh): number {
    switch (type) {
      case 'temperature':
        return (Math.sin(mesh.position.x * 0.5) + Math.cos(mesh.position.z * 0.5) + 2) / 4;
      case 'power':
        return Math.pow(Math.random(), 0.5);
      case 'load':
        return 0.3 + Math.random() * 0.4;
      case 'network':
        const val = Math.random();
        if (val > 0.7) return 0.9 + Math.random() * 0.1;
        if (val > 0.3) return 0.4 + Math.random() * 0.3;
        return Math.random() * 0.3;
      case 'alarm':
        return Math.random() > 0.85 ? 0.9 + Math.random() * 0.1 : Math.random() * 0.3;
      case 'cabinet_occupancy':
        // 机柜占用量：0-0.3 空闲(绿色)，0.3-0.7 部分占用(黄色)，0.7-1.0 高占用(红色)
        return this.getCabinetOccupancyValue(mesh);
      case 'device_weight':
        // 设备重量：0-0.4 轻量(蓝色)，0.4-0.7 中等(绿色)，0.7-1.0 重型(红色)
        return this.getDeviceWeightValue(mesh);
      default:
        return Math.random();
    }
  }

  // 获取机柜占用量数值
  private getCabinetOccupancyValue(mesh: THREE.Mesh): number {
    // 检查是否是机柜相关的设备
    const meshName = mesh.name.toLowerCase();
    const isCabinet = meshName.includes('cabinet') || meshName.includes('rack') || meshName.includes('机柜');

    if (isCabinet) {
      // 模拟机柜占用率：基于位置生成稳定的占用率
      const positionHash = Math.abs(mesh.position.x * 100 + mesh.position.z * 100) % 100;
      return positionHash / 100;
    } else {
      // 非机柜设备：根据设备类型模拟占用情况
      if (meshName.includes('server') || meshName.includes('服务器')) {
        return 0.7 + Math.random() * 0.3; // 服务器通常高占用
      } else if (meshName.includes('ups') || meshName.includes('power')) {
        return 0.4 + Math.random() * 0.4; // 电源设备中等占用
      } else {
        return Math.random() * 0.6; // 其他设备随机占用
      }
    }
  }

  // 获取设备重量数值
  private getDeviceWeightValue(mesh: THREE.Mesh): number {
    // 检查mesh.userData中是否有weight属性
    const userData = mesh.userData;
    if (userData && typeof userData.weight === 'number') {
      // 假设weight范围是50-250kg，映射到0-1
      return Math.min(1, Math.max(0, (userData.weight - 50) / 200));
    }

    // 根据设备名称和大小估算重量
    const meshName = mesh.name.toLowerCase();
    const boundingBox = new THREE.Box3().setFromObject(mesh);
    const size = boundingBox.getSize(new THREE.Vector3());
    const volume = size.x * size.y * size.z;

    let baseWeight = 0.5; // 默认中等重量

    // 根据设备类型调整重量
    if (meshName.includes('server') || meshName.includes('服务器')) {
      baseWeight = 0.6 + Math.random() * 0.3; // 服务器通常较重
    } else if (meshName.includes('ups') || meshName.includes('电源')) {
      baseWeight = 0.7 + Math.random() * 0.3; // UPS设备很重
    } else if (meshName.includes('switch') || meshName.includes('交换机')) {
      baseWeight = 0.2 + Math.random() * 0.4; // 交换机相对较轻
    } else if (meshName.includes('storage') || meshName.includes('存储')) {
      baseWeight = 0.6 + Math.random() * 0.4; // 存储设备较重
    }

    // 根据体积微调重量
    const volumeFactor = Math.min(1, volume / 100); // 假设100为大设备的体积
    return Math.min(1, baseWeight + volumeFactor * 0.1);
  }

  private createHeatmapMaterial(value: number, type: HeatmapEffectType): THREE.MeshStandardMaterial {
    const quantizedValue = Math.floor(value * 10) / 10;
    const poolKey = `${type}_${quantizedValue}`;

    if (this.materialPool[type] && this.materialPool[type].has(poolKey)) {
      return this.materialPool[type].get(poolKey)!.clone();
    }

    const color = new THREE.Color();

    switch (type) {
      case 'temperature':
        if (value < 0.33) color.setRGB(0, value * 3, 1);
        else if (value < 0.66) color.setRGB(value * 3 - 1, 1, 2 - value * 3);
        else color.setRGB(1, 3 - value * 3, 0);
        break;
      case 'power':
        if (value < 0.5) color.setRGB(0, 0.5 + value, value * 2);
        else color.setRGB(value * 2 - 1, 1, 1);
        break;
      case 'load':
        if (value < 0.33) color.setRGB(0.4 * value * 3, 1, 0);
        else if (value < 0.66) color.setRGB(1, 2 - value * 3, 0);
        else color.setRGB(1, 0, 0);
        break;
      case 'network':
        if (value < 0.33) color.setRGB(1, value * 3, 0);
        else if (value < 0.66) color.setRGB(2 - value * 3, 1, 0);
        else color.setRGB(0, 1, value - 0.66);
        break;
      case 'alarm':
        if (value > 0.7) {
          const pulse = (Date.now() % 2000) / 2000;
          const intensity = 0.7 + 0.3 * Math.sin(pulse * Math.PI * 2);
          color.setRGB(intensity, 0, 0);
        } else {
          color.setRGB(0, 0.8, 0.2);
        }
        break;
      case 'cabinet_occupancy':
        // 机柜占用量：绿色(空闲) -> 黄色(部分占用) -> 红色(高占用)
        if (value < 0.3) {
          // 空闲：绿色系
          color.setRGB(0, 0.8 + value * 0.2, 0.2);
        } else if (value < 0.7) {
          // 部分占用：绿色到黄色渐变
          const t = (value - 0.3) / 0.4;
          color.setRGB(t * 0.8, 1, 0.2 * (1 - t));
        } else {
          // 高占用：黄色到红色渐变
          const t = (value - 0.7) / 0.3;
          color.setRGB(0.8 + t * 0.2, 1 - t * 0.5, 0);
        }
        break;
      case 'device_weight':
        // 设备重量：蓝色(轻) -> 绿色(中) -> 红色(重)
        if (value < 0.4) {
          // 轻量：蓝色系
          const t = value / 0.4;
          color.setRGB(0, 0.5 + t * 0.3, 0.8 + t * 0.2);
        } else if (value < 0.7) {
          // 中等：蓝绿色到绿色渐变
          const t = (value - 0.4) / 0.3;
          color.setRGB(t * 0.3, 0.8 + t * 0.2, 1 - t * 0.5);
        } else {
          // 重型：绿色到红色渐变
          const t = (value - 0.7) / 0.3;
          color.setRGB(0.3 + t * 0.7, 1 - t * 0.8, 0.5 * (1 - t));
        }
        break;
      default:
        if (value < 0.33) color.setRGB(0, value * 3, 1);
        else if (value < 0.66) color.setRGB(value * 3 - 1, 1, 2 - value * 3);
        else color.setRGB(1, 3 - value * 3, 0);
    }

    const material = new THREE.MeshStandardMaterial({
      color: color,
      transparent: true,
      opacity: 0.8,
      emissive: color,
      emissiveIntensity: 0.7,
    });

    material.userData = {
      heatmapValue: value,
      effectType: type,
      isHeatmapMaterial: true,
    };

    if (this.materialPool[type] && this.materialPool[type].size < 20) {
      this.materialPool[type].set(poolKey, material.clone());
    }

    if (type === 'alarm' && value > 0.7) {
      material.userData.needsUpdate = true;
      this.registerAlarmMaterial(material);
    }

    return material;
  }

  private createMaterialBackup(material: ExtendedMaterial): MaterialBackup {
    const backup: MaterialBackup = {
      material: material,
      opacity: material.opacity || 1,
    };

    if ('transparent' in material) {
      backup.transparent = (material as THREE.MeshStandardMaterial).transparent || false;
    }
    if ('depthWrite' in material) {
      backup.depthWrite = (material as THREE.MeshStandardMaterial).depthWrite;
    }

    if (hasColorProperty(material)) {
      backup.color = (material as THREE.MeshStandardMaterial).color?.clone();
    }

    if (hasEmissiveProperty(material)) {
      const stdMaterial = material as THREE.MeshStandardMaterial;
      backup.emissive = stdMaterial.emissive?.clone();
      backup.emissiveIntensity = stdMaterial.emissiveIntensity || 0;
    }

    if (material.map) {
      backup.map = material.map;
      backup.colorSpace = material.map.colorSpace as THREE.ColorSpace;
      backup.mapRepeat = { x: material.map.repeat.x, y: material.map.repeat.y };
      backup.mapOffset = { x: material.map.offset.x, y: material.map.offset.y };
    }

    if (isStandardMaterial(material)) {
      backup.normalMap = material.normalMap || null;
      backup.roughnessMap = material.roughnessMap || null;
      backup.metalnessMap = material.metalnessMap || null;
      backup.aoMap = material.aoMap || null;
      backup.emissiveMap = material.emissiveMap || null;
    }

    return backup;
  }

  private safelyRestoreTexture(
    material: THREE.MeshStandardMaterial,
    mapType: keyof Pick<THREE.MeshStandardMaterial, 'map' | 'normalMap' | 'roughnessMap' | 'metalnessMap' | 'aoMap' | 'emissiveMap'>,
    originalMap: THREE.Texture | null
  ): void {
    if (originalMap && originalMap.image && (originalMap.image.width > 0 || originalMap.image.data)) {
      material[mapType] = originalMap;
      if (material[mapType]) {
        material[mapType].needsUpdate = true;
      }
    } else {
      // 使用类型安全的方式设置为 null
      try {
        material[mapType] = null;
      } catch (error) {
        console.warn(`无法将 ${String(mapType)} 设置为 null`, error);
      }
    }
  }

  public async clearHeatmapAsync(): Promise<boolean> {
    console.log(`[HeatmapManager] 异步清除热力图, 当前效果: ${this.currentEffect}, 是否过渡中: ${this.isTransitioning}`);

    return new Promise((resolve) => {
      const result = this.clearHeatmap();

      if (this.isTransitioning && this.transitionPromise) {
        console.log(`[HeatmapManager] 等待热力图过渡完成后清理...`);
        this.transitionPromise.then(() => {
          this.forceRefreshMaterials();
          setTimeout(() => {
            console.log(`[HeatmapManager] 热力图清理完成并刷新材质`);
            resolve(result);
          }, 350);
        });
      } else {
        setTimeout(() => {
          this.forceRefreshMaterials();
          console.log(`[HeatmapManager] 热力图清理完成并刷新材质`);
          resolve(result);
        }, 350);
      }
    });
  }

  private clearHeatmap(isSwitching = false): boolean {
    if (!this.currentEffect) {
      console.log('[HeatmapManager] 没有活动的热力图效果，无需清除');
      return false;
    }

    console.log(`[HeatmapManager] 开始清除热力图效果，当前类型: ${this.currentEffect}`);

    this.isTransitioning = true;
    this.pendingEffect = null;

    // 取消所有动画帧
    const animationCount = this.animationFrameIds.size;
    this.animationFrameIds.forEach((id) => cancelAnimationFrame(id));
    this.animationFrameIds.clear();
    console.log(`[HeatmapManager] 已取消 ${animationCount} 个动画帧`);

    // 停止所有材质动画
    let tweenCount = 0;
    this.affectedMeshes.forEach((mesh) => {
      if (mesh && mesh.material) {
        if (Array.isArray(mesh.material)) {
          mesh.material.forEach((mat) => {
            gsap.killTweensOf(mat);
            tweenCount++;
          });
        } else {
          gsap.killTweensOf(mesh.material);
          tweenCount++;
        }
      }
    });
    console.log(`[HeatmapManager] 已停止 ${tweenCount} 个材质动画`);

    // 恢复受影响网格的原始材质
    let restoredCount = 0;
    this.affectedMeshes.forEach((mesh) => {
      if (mesh && this.originalMaterials.has(mesh.uuid)) {
        this.restoreMeshMaterial(mesh);
        restoredCount++;
      }
    });
    console.log(`[HeatmapManager] 已恢复 ${restoredCount} 个网格的原始材质`);

    // 查找并恢复场景中的所有原始材质
    if (this.scene) {
      let sceneRestoredCount = 0;
      this.originalMaterials.forEach((originalData, uuid) => {
        const mesh = this.findObjectByUuid(this.scene, uuid);
        if (mesh && mesh instanceof THREE.Mesh) {
          this.restoreMeshMaterial(mesh);
          sceneRestoredCount++;
        }
      });
      console.log(`[HeatmapManager] 已在场景中恢复 ${sceneRestoredCount} 个网格的原始材质`);
    }

    if (!isSwitching) {
      setTimeout(() => {
        const materialCount = this.originalMaterials.size;
        this.originalMaterials.clear();

        const meshCount = this.affectedMeshes.length;
        this.affectedMeshes = [];

        this.currentEffect = null;

        this.performFinalMaterialCheck();
        this.forceRefreshMaterials();

        this.isTransitioning = false;
        this._checkPendingEffect();

        console.log(`[HeatmapManager] 热力图效果已完全清除并恢复原始材质 (清理了 ${materialCount} 个材质和 ${meshCount} 个网格)`);
      }, 300);
    } else {
      this.currentEffect = null;
      console.log('[HeatmapManager] 热力图效果已切换，准备应用新效果');
    }

    return true;
  }

  private restoreMeshMaterial(mesh: THREE.Mesh): void {
    if (!mesh || !this.originalMaterials.has(mesh.uuid)) return;

    const originalData = this.originalMaterials.get(mesh.uuid)!;

    if (originalData.material) {
      if (mesh.material !== originalData.material) {
        mesh.material = originalData.material;
      }
    } else {
      const material = mesh.material as ExtendedMaterial;

      if (originalData.color && hasColorProperty(material)) {
        (material as THREE.MeshStandardMaterial).color.copy(originalData.color);
      }

      if (originalData.emissive && hasEmissiveProperty(material)) {
        const stdMaterial = material as THREE.MeshStandardMaterial;
        stdMaterial.emissive.copy(originalData.emissive);
        stdMaterial.emissiveIntensity = originalData.emissiveIntensity || 0;
      }

      if (this.hasTextureBackup) {
        if (originalData.map) {
          material.map = undefined;
          setTimeout(() => {
            if (originalData.map && originalData.map.image && (originalData.map.image.width > 0 || originalData.map.image.data)) {
              material.map = originalData.map;

              if (originalData.mapRepeat) {
                material.map.repeat.set(originalData.mapRepeat.x, originalData.mapRepeat.y);
              }
              if (originalData.mapOffset) {
                material.map.offset.set(originalData.mapOffset.x, originalData.mapOffset.y);
              }

              material.map.needsUpdate = true;
            }
            if ('needsUpdate' in material) {
              (material as THREE.MeshStandardMaterial).needsUpdate = true;
            }
          }, 20);
        }

        this.safelyRestoreTexture(material as THREE.MeshStandardMaterial, 'normalMap', originalData.normalMap || null);
        this.safelyRestoreTexture(material as THREE.MeshStandardMaterial, 'roughnessMap', originalData.roughnessMap || null);
        this.safelyRestoreTexture(material as THREE.MeshStandardMaterial, 'metalnessMap', originalData.metalnessMap || null);
        this.safelyRestoreTexture(material as THREE.MeshStandardMaterial, 'aoMap', originalData.aoMap || null);
        this.safelyRestoreTexture(material as THREE.MeshStandardMaterial, 'emissiveMap', originalData.emissiveMap || null);
      }

      material.opacity = originalData.opacity || 1;
      if ('transparent' in material) {
        (material as THREE.MeshStandardMaterial).transparent = originalData.transparent || false;
      }
      if ('depthWrite' in material) {
        (material as THREE.MeshStandardMaterial).depthWrite = originalData.depthWrite !== undefined ? originalData.depthWrite : true;
      }
    }

    if ('needsUpdate' in mesh.material) {
      (mesh.material as THREE.MeshStandardMaterial).needsUpdate = true;
    }
    mesh.visible = mesh.visible;
  }

  private forceRefreshMaterials(): void {
    console.log('执行强制材质刷新');
    if (!this.scene) return;

    this.scene.traverse((object: THREE.Object3D) => {
      if (object instanceof THREE.Mesh && object.material) {
        if (Array.isArray(object.material)) {
          object.material.forEach((mat) => {
            if (mat && 'needsUpdate' in mat) {
              (mat as THREE.MeshStandardMaterial).needsUpdate = true;
              if (mat.map && mat.map.image && (mat.map.image.width > 0 || mat.map.image.data)) {
                mat.map.needsUpdate = true;
              }
            }
          });
        } else if ('needsUpdate' in object.material) {
          (object.material as THREE.MeshStandardMaterial).needsUpdate = true;
          if (object.material.map && object.material.map.image && (object.material.map.image.width > 0 || object.material.map.image.data)) {
            object.material.map.needsUpdate = true;
          }
        }

        object.visible = object.visible;
      }
    });

    if (this.scene.userData.needsRender) {
      this.scene.userData.needsRender = true;
    }

    const sceneManager = SceneManager.getInstance();
    if (sceneManager && sceneManager.renderer) {
      sceneManager.needsRender = true;
      for (let i = 0; i < 3; i++) {
        setTimeout(() => {
          if (sceneManager) {
            sceneManager.needsRender = true;
            sceneManager.render();
          }
        }, i * 100);
      }
    }
  }

  findObjectByUuid(scene: THREE.Scene, uuid: string): THREE.Object3D | null {
    if (!this.uuidCache) {
      this.uuidCache = new Map<string, THREE.Object3D>();
      scene.traverse((object: THREE.Object3D) => {
        this.uuidCache?.set(object.uuid, object);
      });
    }
    return this.uuidCache.get(uuid) || null;
  }

  public dispose(): void {
    this.animationFrameIds.forEach((id) => cancelAnimationFrame(id));
    this.animationFrameIds.clear();

    Object.values(this.materialPool).forEach((pool) => pool.clear());
    this.materialPool = null!;

    if (this.alarmMaterials) {
      this.alarmMaterials.clear();
      this.alarmMaterials = undefined;
    }

    if (this.uuidCache) {
      this.uuidCache.clear();
      this.uuidCache = undefined;
    }

    if (this.globalMaterialBackup) {
      this.globalMaterialBackup.clear();
      this.globalMaterialBackup.clear();
    }

    this.clearHeatmap();
    this.affectedMeshes = null!;
    this.originalMaterials.clear();
    this.scene = null!;
    this.modelLoader = null!;
    HeatmapManager.instance = null;

    this.performFinalMaterialCheck();
  }
}
