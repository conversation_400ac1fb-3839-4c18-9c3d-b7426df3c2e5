<template>
  <div class="p-[1vw] space-y-[1vw]">
    <div class="bg-blue-500/10 border border-blue-500/20 rounded p-[0.8vw]">
      <h3 class="text-[0.7vw] text-blue-400 font-semibold mb-[0.4vw]">申请信息</h3>
      <div class="grid grid-cols-2 gap-[0.8vw] text-[0.6vw]">
        <div
          ><span class="text-gray-400">申请人：</span><span class="text-white">{{ application?.applicant }}</span></div
        >
        <div
          ><span class="text-gray-400">部门：</span><span class="text-white">{{ application?.department }}</span></div
        >
        <div
          ><span class="text-gray-400">资产类型：</span><span class="text-white">{{ application?.assetType }}</span></div
        >
        <div
          ><span class="text-gray-400">申请数量：</span><span class="text-white">{{ application?.quantity }}</span></div
        >
      </div>
    </div>

    <div>
      <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">审批结果 *</label>
      <select
        v-model="formData.result"
        class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400"
      >
        <option value="">请选择审批结果</option>
        <option value="approved">批准</option>
        <option value="rejected">拒绝</option>
        <option value="pending">需要更多信息</option>
      </select>
    </div>

    <div>
      <label class="block text-[0.6vw] text-gray-400 mb-[0.3vw]">审批意见</label>
      <textarea
        v-model="formData.comments"
        rows="4"
        class="w-full bg-black/20 border border-white/10 rounded px-[0.6vw] py-[0.4vw] text-white text-[0.6vw] outline-none focus:border-blue-400 resize-none"
        placeholder="请输入审批意见"
      ></textarea>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue';

  const props = defineProps({
    formData: { type: Object, default: () => ({}) },
    application: { type: Object, default: null },
  });

  const emit = defineEmits(['update:formData']);

  const formData = ref({
    result: '',
    comments: '',
    ...props.formData,
  });

  watch(
    formData,
    (newValue) => {
      emit('update:formData', newValue);
    },
    { deep: true }
  );
</script>
