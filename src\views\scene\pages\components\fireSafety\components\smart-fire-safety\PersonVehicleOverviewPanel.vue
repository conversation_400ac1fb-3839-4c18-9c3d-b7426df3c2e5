<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 人车概况统计 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-users mr-[0.4vw] text-blue-400"></i>
        人车概况统计
      </div>
      <div class="grid grid-cols-5 gap-[0.6vw]">
        <div v-for="stat in overviewStats" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
          <div v-if="stat.trend" class="text-[0.5vw] mt-[0.2vw]" :class="stat.trendClass">
            {{ stat.trend }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：实时人员状态 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-user-friends mr-[0.4vw] text-blue-400"></i>
            实时人员状态
          </div>
          <div class="flex gap-[0.4vw]">
            <button
              v-for="filter in personFilters"
              :key="filter.key"
              @click="activePersonFilter = filter.key"
              :class="[
                'px-[0.6vw] py-[0.2vw] rounded text-[0.6vw] transition-all',
                activePersonFilter === filter.key ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-gray-300 hover:bg-black/30',
              ]"
            >
              {{ filter.label }}
            </button>
          </div>
        </div>

        <div class="flex-1 overflow-y-auto custom-scrollbar">
          <div class="space-y-[0.4vw]">
            <div v-for="person in filteredPersons" :key="person.id" class="bg-[#15274D]/30 p-[0.6vw] rounded hover:bg-[#15274D]/50 transition-all">
              <div class="flex items-center justify-between mb-[0.3vw]">
                <div class="flex items-center">
                  <div class="w-[2vw] h-[2vw] rounded-full bg-blue-400/20 flex items-center justify-center mr-[0.6vw]">
                    <i class="fas fa-user text-blue-400"></i>
                  </div>
                  <div>
                    <div class="text-[0.65vw] text-white font-medium">{{ person.name }}</div>
                    <div class="text-[0.6vw] text-gray-400">{{ person.department }}</div>
                  </div>
                </div>
                <span class="text-[0.6vw]" :class="getPersonStatusClass(person.status)">
                  {{ getPersonStatusText(person.status) }}
                </span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400">
                <span>工号：{{ person.employeeId }}</span>
                <span>{{ person.status === 'in' ? '进入时间' : '离开时间' }}：{{ person.lastTime }}</span>
              </div>
              <div v-if="person.location" class="text-[0.6vw] text-gray-400 mt-[0.2vw]"> 当前位置：{{ person.location }} </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：车辆状态和统计 -->
      <div class="w-[40%] flex flex-col gap-[0.8vw]">
        <!-- 车辆状态 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-car mr-[0.4vw] text-blue-400"></i>
            车辆状态
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="vehicle in vehicleStatus" :key="vehicle.id" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ vehicle.plateNumber }}</span>
                <span class="text-[0.6vw]" :class="getVehicleStatusClass(vehicle.status)">
                  {{ getVehicleStatusText(vehicle.status) }}
                </span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>车主：{{ vehicle.owner }}</span>
                <span>{{ vehicle.status === 'in' ? '进入时间' : '离开时间' }}：{{ vehicle.time }}</span>
              </div>
              <div v-if="vehicle.parkingSpace" class="text-[0.5vw] text-gray-400 mt-[0.2vw]"> 停车位：{{ vehicle.parkingSpace }} </div>
            </div>
          </div>
        </div>

        <!-- 区域分布 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-map-marker-alt mr-[0.4vw] text-blue-400"></i>
            区域分布
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="area in areaDistribution" :key="area.name" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ area.name }}</span>
                <span class="text-[0.6vw] text-blue-400">{{ area.personCount }}人</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400">
                <span>车辆：{{ area.vehicleCount }}辆</span>
                <span>容量：{{ area.capacity }}人</span>
              </div>
              <div class="mt-[0.2vw]">
                <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                  <div
                    class="bg-blue-400 h-[0.3vw] rounded-full transition-all"
                    :style="{ width: (area.personCount / area.capacity) * 100 + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 今日统计 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-chart-bar mr-[0.4vw] text-blue-400"></i>
            今日统计
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="stat in todayStats" :key="stat.label" class="flex justify-between items-center">
              <span class="text-[0.6vw] text-gray-400">{{ stat.label }}</span>
              <span class="text-[0.6vw] text-white font-medium">{{ stat.value }}</span>
            </div>
          </div>
        </div>

        <!-- 异常情况 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-exclamation-circle mr-[0.4vw] text-blue-400"></i>
            异常情况
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="anomaly in anomalies" :key="anomaly.id" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ anomaly.time }}</span>
                <span class="text-[0.5vw]" :class="getAnomalyLevelClass(anomaly.level)">{{ anomaly.level }}</span>
              </div>
              <div class="text-[0.5vw] text-gray-400">{{ anomaly.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  // 人车概况统计数据
  const overviewStats = ref([
    {
      label: '在场人员',
      value: '156',
      valueClass: 'text-green-400',
      trend: '↑ 12',
      trendClass: 'text-green-400',
    },
    {
      label: '在场车辆',
      value: '89',
      valueClass: 'text-blue-400',
      trend: '↑ 5',
      trendClass: 'text-green-400',
    },
    {
      label: '今日进入',
      value: '234',
      valueClass: 'text-yellow-400',
      trend: '↑ 18',
      trendClass: 'text-green-400',
    },
    {
      label: '今日离开',
      value: '198',
      valueClass: 'text-orange-400',
      trend: '↑ 15',
      trendClass: 'text-green-400',
    },
    {
      label: '异常次数',
      value: '0',
      valueClass: 'text-green-400',
      trend: '→ 0',
      trendClass: 'text-green-400',
    },
  ]);

  // 人员筛选器
  const personFilters = ref([
    { key: 'all', label: '全部' },
    { key: 'in', label: '在场' },
    { key: 'out', label: '离场' },
  ]);

  const activePersonFilter = ref('in');

  // 人员数据
  const persons = ref([
    {
      id: 1,
      name: '张工程师',
      department: '技术部',
      employeeId: 'T001',
      status: 'in',
      lastTime: '08:30',
      location: '1F机房',
    },
    {
      id: 2,
      name: '李经理',
      department: '管理部',
      employeeId: 'M001',
      status: 'in',
      lastTime: '09:15',
      location: '3F办公室',
    },
    {
      id: 3,
      name: '王技师',
      department: '运维部',
      employeeId: 'O001',
      status: 'in',
      lastTime: '08:45',
      location: '2F配电室',
    },
    {
      id: 4,
      name: '赵主管',
      department: '安全部',
      employeeId: 'S001',
      status: 'out',
      lastTime: '17:30',
      location: null,
    },
    {
      id: 5,
      name: '陈工',
      department: '技术部',
      employeeId: 'T002',
      status: 'in',
      lastTime: '09:00',
      location: '1F监控室',
    },
  ]);

  // 筛选后的人员
  const filteredPersons = computed(() => {
    if (activePersonFilter.value === 'all') {
      return persons.value;
    }
    return persons.value.filter((person) => person.status === activePersonFilter.value);
  });

  // 车辆状态
  const vehicleStatus = ref([
    {
      id: 1,
      plateNumber: '京A12345',
      owner: '张工程师',
      status: 'in',
      time: '08:25',
      parkingSpace: 'A-01',
    },
    {
      id: 2,
      plateNumber: '京B67890',
      owner: '李经理',
      status: 'in',
      time: '09:10',
      parkingSpace: 'A-05',
    },
    {
      id: 3,
      plateNumber: '京C11111',
      owner: '王技师',
      status: 'in',
      time: '08:40',
      parkingSpace: 'B-03',
    },
    {
      id: 4,
      plateNumber: '京D22222',
      owner: '访客',
      status: 'out',
      time: '16:45',
      parkingSpace: null,
    },
  ]);

  // 区域分布
  const areaDistribution = ref([
    { name: '1F机房区', personCount: 45, vehicleCount: 0, capacity: 60 },
    { name: '2F办公区', personCount: 78, vehicleCount: 0, capacity: 100 },
    { name: '3F会议区', personCount: 23, vehicleCount: 0, capacity: 40 },
    { name: '地下车库', personCount: 10, vehicleCount: 89, capacity: 120 },
  ]);

  // 今日统计
  const todayStats = ref([
    { label: '人员进入总数', value: '234人' },
    { label: '人员离开总数', value: '198人' },
    { label: '车辆进入总数', value: '156辆' },
    { label: '车辆离开总数', value: '142辆' },
    { label: '访客登记数', value: '28人' },
    { label: '平均停留时间', value: '7.5小时' },
  ]);

  // 异常情况
  const anomalies = ref([
    { id: 1, time: '2024-02-28 14:30', level: '正常', description: '所有人员车辆进出正常' },
    { id: 2, time: '2024-02-28 10:15', level: '正常', description: '访客登记流程正常' },
    { id: 3, time: '2024-02-28 08:45', level: '正常', description: '员工考勤正常' },
  ]);

  // 获取人员状态样式
  const getPersonStatusClass = (status) => {
    return status === 'in' ? 'text-green-400' : 'text-orange-400';
  };

  // 获取人员状态文本
  const getPersonStatusText = (status) => {
    return status === 'in' ? '在场' : '离场';
  };

  // 获取车辆状态样式
  const getVehicleStatusClass = (status) => {
    return status === 'in' ? 'text-green-400' : 'text-orange-400';
  };

  // 获取车辆状态文本
  const getVehicleStatusText = (status) => {
    return status === 'in' ? '在场' : '离场';
  };

  // 获取异常等级样式
  const getAnomalyLevelClass = (level) => {
    switch (level) {
      case '正常':
        return 'text-green-400';
      case '警告':
        return 'text-yellow-400';
      case '严重':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
