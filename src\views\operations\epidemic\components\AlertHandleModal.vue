<template>
  <a-modal v-model:visible="visible" title="告警处理" width="500px" @ok="handleSubmit" @cancel="handleCancel">
    <div v-if="healthInfo" class="mb-4 p-3 bg-red-50 border border-red-200 rounded">
      <h4 class="text-red-600">告警信息</h4>
      <p>姓名: {{ healthInfo.name }}</p>
      <p>电话: {{ healthInfo.phone }}</p>
      <p v-if="healthInfo.temperature > 37.3" class="text-red-500"> 体温异常: {{ healthInfo.temperature }}°C </p>
      <p v-if="healthInfo.healthCode !== 'green'" class="text-red-500"> 健康码异常: {{ healthInfo.healthCode }} </p>
    </div>

    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <a-form-item label="处理措施" name="action">
        <a-select v-model:value="formData.action" placeholder="选择处理措施">
          <a-select-option value="isolate">隔离观察</a-select-option>
          <a-select-option value="test">核酸检测</a-select-option>
          <a-select-option value="monitor">健康监测</a-select-option>
          <a-select-option value="clear">解除告警</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="处理备注" name="remark">
        <a-textarea v-model:value="formData.remark" placeholder="请输入处理备注" :rows="4" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { handleEpidemicAlert, type HealthInfo } from '/@/api/operations/epidemic';

  interface Props {
    visible: boolean;
    healthInfo: HealthInfo | null;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{ 'update:visible': [value: boolean]; success: [] }>();

  const formRef = ref();
  const formData = reactive({
    action: '',
    remark: '',
  });

  const rules = {
    action: [{ required: true, message: '请选择处理措施', trigger: 'change' }],
    remark: [{ required: true, message: '请输入处理备注', trigger: 'blur' }],
  };

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const handleSubmit = async () => {
    try {
      await formRef.value.validate();
      if (!props.healthInfo) return;

      await handleEpidemicAlert({
        alertId: props.healthInfo.id,
        action: formData.action as any,
        remark: formData.remark,
      });

      message.success('告警处理成功');
      emit('success');
    } catch (error) {
      message.error('处理失败');
    }
  };

  const handleCancel = () => {
    visible.value = false;
  };
</script>
