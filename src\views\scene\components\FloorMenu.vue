<template>
  <div class="relative w-[5vw] flex flex-col items-center gap-[0.5vw]">
    <!-- 当前楼层指示器 -->
    <div
      class="relative w-[3.6vw] h-[4vw] bg-gradient-to-br from-[rgba(23,43,77,0.8)] to-[rgba(36,108,249,0.2)] rounded-[0.8vw] cursor-pointer flex items-center justify-center transition-all duration-300 border border-blue-400/30 overflow-hidden"
      :class="{
        'scale-105 bg-gradient-to-br from-blue-400/30 to-[rgba(23,43,77,0.9)] border-blue-400/50': isExpanded,
      }"
      @click="toggleMenu"
      @mouseleave="startAutoCloseTimer"
      @mouseenter="clearAutoCloseTimer"
    >
      <div
        class="absolute inset-0 before:content-[''] before:absolute before:top-[-50%] before:left-[-50%] before:w-[200%] before:h-[200%] before:bg-gradient-to-r before:from-transparent before:via-[rgba(36,108,249,0.3)] before:to-transparent before:transform before:rotate-45 before:animate-borderScan"
      ></div>
      <div class="relative z-2 flex flex-col items-center">
        <div class="text-[1.4vw] font-600 text-white text-shadow-[0_0_4px_rgba(36,108,249,0.8)] leading-[1.2]">{{ currentShortName }}</div>
        <div class="text-[1vw] text-blue-400/80 mt-[0.2vw]">
          <HomeOutlined />
        </div>
      </div>
      <div
        class="absolute right-[0.4vw] bottom-[0.4vw] w-[0.5vw] h-[0.5vw] rounded-full transition-colors duration-300"
        :class="[
          globalThreeStore.canUserInteract && loadingProgress === 100
            ? 'bg-green-400/80 shadow-[0_0_4px_rgba(0,255,0,0.5)]'
            : 'bg-red-400/80 shadow-[0_0_4px_rgba(255,0,0,0.5)]',
        ]"
      ></div>
    </div>

    <!-- 楼层选择菜单 -->
    <div
      class="flex flex-col gap-[0.4vw] transition-all duration-300 ease-out relative"
      :class="{
        'opacity-100 translate-y-0 pointer-events-auto': isExpanded,
        'opacity-0 -translate-y-[1vw] pointer-events-none': !isExpanded,
      }"
      @mouseleave="startAutoCloseTimer"
      @mouseenter="clearAutoCloseTimer"
    >
      <div
        class="absolute w-px h-full bg-gradient-to-b from-[rgba(36,108,249,0)] via-[rgba(36,108,249,0.5)] to-[rgba(36,108,249,0)] left-1/2 -translate-x-1/2 z-[-1]"
      ></div>
      <div
        v-for="(floor, index) in buildingData.floors"
        :key="floor.id"
        class="w-[4vw] h-[2vw] relative cursor-pointer transition-all duration-300"
        :class="[
          'opacity-0 -translate-x-[1vw]',
          isExpanded ? 'opacity-100 translate-x-0' : '',
          selectedFloorId === floor.id ? 'scale-105' : '',
          !globalThreeStore.canUserInteract || loadingProgress < 100 || isSwitching || globalThreeStore.floorTransitionState.isTransitioning
            ? 'opacity-50 cursor-not-allowed'
            : '',
        ]"
        :style="{ transitionDelay: `${index * 0.05}s` }"
        @click="selectFloor(floor)"
      >
        <div
          class="w-full h-full bg-[rgba(23,43,77,0.8)] border border-blue-400/30 rounded-[0.4vw] flex items-center justify-center relative overflow-hidden transition-all duration-300"
          :class="[selectedFloorId === floor.id ? 'bg-blue-400/30 border-blue-400/80' : '', 'hover:bg-blue-400/20 hover:border-blue-400/50']"
        >
          <span class="text-[0.8vw] font-500 text-white">{{ floor.name }}</span>
          <div
            class="absolute inset-0 before:content-[''] before:absolute before:top-[-50%] before:left-[-50%] before:w-[200%] before:h-[200%] before:bg-gradient-to-r before:from-transparent before:via-[rgba(36,108,249,0.3)] before:to-transparent before:transform before:rotate-45 before:animate-borderScan"
          ></div>
          <div
            v-if="(loadingProgress < 100 && selectedFloorId === floor.id) || isSwitching"
            class="absolute inset-0 bg-[rgba(23,43,77,0.9)] flex items-center justify-center"
          >
            <div class="w-[1.2vw] h-[1.2vw] relative">
              <div
                class="absolute w-full h-full rounded-full border-[0.15vw] border-[rgba(36,108,249,0.3)] border-t-[rgba(36,108,249,0.8)] animate-spin"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加TechMessage组件 -->
  <TechMessage ref="techMessage" />
</template>

<script setup lang="ts">
  import { ref, computed, onBeforeUnmount } from 'vue';
  import { HomeOutlined } from '@ant-design/icons-vue';
  import { useGlobalThreeStore } from '/@/views/scene/store/globalThreeStore';
  import { buildingData } from '@/data/buildingData';
  import { ControlManager } from '../lib/control/ControlManager';
  import { ModelLoaderManager } from '../lib/load/ModelLoaderManager';
  import { SELECTION_CONFIG } from '../config';
  import TechMessage from './TechMessage.vue';
  import { debounce } from 'lodash-es';

  // 定义楼层数据的类型
  interface Floor {
    id: string;
    name: string;
  }

  // 初始化全局状态和工具
  const globalThreeStore = useGlobalThreeStore();
  const loadingProgress = computed(() => globalThreeStore.loadingProgress);
  const techMessage = ref<InstanceType<typeof TechMessage> | null>(null);

  // 当前楼层 ID 的计算属性
  const selectedFloorId = computed({
    get: () => globalThreeStore.currentFloorId,
    set: (value: string | null) => globalThreeStore.setCurrentFloorId(value),
  });

  // 状态定义
  const isBuildingSelected = computed(() => selectedFloorId.value === null);
  const isExpanded = ref<boolean>(false);
  const isSwitching = ref<boolean>(false);
  const floorIndicator = ref<any>(null);
  let autoCloseTimer: NodeJS.Timeout | null = null;

  // 计算当前楼层的简短名称
  const currentShortName = computed(() => {
    if (isBuildingSelected.value) return '主';
    const currentFloor = buildingData.floors.find((f: Floor) => f.id === selectedFloorId.value);
    return currentFloor ? currentFloor.name.replace(/[^0-9]/g, '') : 'F';
  });

  // 切换菜单展开状态
  const toggleMenu = (): void => {
    isExpanded.value = !isExpanded.value;
    if (isExpanded.value) {
      startAutoCloseTimer();
    } else {
      clearAutoCloseTimer();
    }
  };

  // 启动自动关闭计时器
  const startAutoCloseTimer = (): void => {
    clearAutoCloseTimer();
    autoCloseTimer = setTimeout(() => {
      isExpanded.value = false;
    }, 3000);
  };

  // 清除自动关闭计时器
  const clearAutoCloseTimer = (): void => {
    if (autoCloseTimer) {
      clearTimeout(autoCloseTimer);
      autoCloseTimer = null;
    }
  };

  // 组件销毁时清理计时器
  onBeforeUnmount(() => {
    clearAutoCloseTimer();
    if (selectFloor.cancel) selectFloor.cancel();
  });

  // 选择楼层并切换 - 使用debounce防止频繁点击
  const selectFloor = debounce(async (floor: Floor): Promise<void> => {
    if (!globalThreeStore.canUserInteract) {
      techMessage.value?.showMessage('系统正在加载中，请稍候再试...', 'warning', { duration: 3000 });
      return;
    }

    // 如果正在切换楼层，直接返回
    if (
      loadingProgress.value < 100 ||
      isSwitching.value ||
      selectedFloorId.value === floor.id ||
      globalThreeStore.floorTransitionState.isTransitioning
    )
      return;

    // 检查是否处于透视模式
    if (globalThreeStore.transparencyMode) {
      techMessage.value?.showMessage('请先关闭透视模式，再切换楼层', 'warning', { duration: 3000 });
      return;
    }

    if (SELECTION_CONFIG.observeMode.enabled) {
      window.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));
    }

    try {
      isSwitching.value = true;

      // 触发事件通知透视状态需要重置
      window.dispatchEvent(new CustomEvent('floorChange'));

      const controlManager = ControlManager.getInstance();
      controlManager.setMode('orbit');

      // 使用新的楼层切换方法，包含过渡效果
      await globalThreeStore.switchFloor(floor.id);

      floorIndicator.value?.showFloorIndicator(floor.name);
    } catch (error) {
      console.error('楼层切换失败:', error);
      techMessage.value?.showMessage('楼层切换失败，请重试', 'error', { duration: 3000 });
    } finally {
      isSwitching.value = false;
    }
  }, 300);
</script>
