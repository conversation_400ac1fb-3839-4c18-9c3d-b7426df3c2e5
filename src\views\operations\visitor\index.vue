<template>
  <div class="p-4">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <UserOutlined class="h-8 w-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">今日访客</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.todayTotal }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <ClockCircleOutlined class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">正在访问</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.todayVisiting }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <ExclamationCircleOutlined class="h-8 w-8 text-yellow-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">待审批</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.pendingApproval }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <BarChartOutlined class="h-8 w-8 text-purple-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">本月访客</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.monthTotal }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="mb-4 flex justify-between items-center">
      <div class="flex space-x-2">
        <a-button type="primary" @click="showAppointmentModal">
          <PlusOutlined />
          访客预约
        </a-button>
        <a-button @click="showCheckInModal">
          <LoginOutlined />
          访客签到
        </a-button>
        <a-button @click="refreshData">
          <ReloadOutlined />
          刷新数据
        </a-button>
      </div>

      <div class="flex space-x-2">
        <a-input-search v-model:value="searchText" placeholder="搜索访客姓名、电话或公司" style="width: 300px" @search="handleSearch" />
        <a-select v-model:value="statusFilter" placeholder="状态筛选" style="width: 120px" @change="handleSearch">
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="pending">待审批</a-select-option>
          <a-select-option value="approved">已审批</a-select-option>
          <a-select-option value="visiting">访问中</a-select-option>
          <a-select-option value="completed">已完成</a-select-option>
        </a-select>
      </div>
    </div>

    <!-- 访客列表 -->
    <div class="bg-white rounded-lg shadow">
      <a-table :columns="columns" :data-source="visitorList" :loading="loading" :pagination="pagination" @change="handleTableChange" row-key="id">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'photo'">
            <a-avatar v-if="record.photo" :src="record.photo" />
            <a-avatar v-else>
              <UserOutlined />
            </a-avatar>
          </template>

          <template v-if="column.key === 'temperature'">
            <span :class="record.temperature > 37.3 ? 'text-red-500' : 'text-green-500'">
              {{ record.temperature ? record.temperature + '°C' : '-' }}
            </span>
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button v-if="record.status === 'pending'" type="primary" size="small" @click="approveVisitor(record, true)"> 审批 </a-button>

              <a-button v-if="record.status === 'approved'" type="primary" size="small" @click="checkInVisitor(record)"> 签到 </a-button>

              <a-button v-if="record.status === 'visiting'" size="small" @click="checkOutVisitor(record)"> 签退 </a-button>

              <a-button size="small" @click="viewVisitorDetail(record)"> 详情 </a-button>

              <a-button v-if="record.qrCode" size="small" @click="showQRCode(record)"> 二维码 </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 访客预约弹窗 -->
    <VisitorAppointmentModal v-model:visible="appointmentModalVisible" @success="handleAppointmentSuccess" />

    <!-- 访客签到弹窗 -->
    <VisitorCheckInModal v-model:visible="checkInModalVisible" @success="handleCheckInSuccess" />

    <!-- 访客详情弹窗 -->
    <VisitorDetailModal v-model:visible="detailModalVisible" :visitor="selectedVisitor" />

    <!-- 二维码显示弹窗 -->
    <QRCodeModal v-model:visible="qrCodeModalVisible" :qr-code="selectedQRCode" :visitor-name="selectedVisitor?.name" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import {
    UserOutlined,
    ClockCircleOutlined,
    ExclamationCircleOutlined,
    BarChartOutlined,
    PlusOutlined,
    LoginOutlined,
    ReloadOutlined,
  } from '@ant-design/icons-vue';
  import {
    getVisitorList,
    getVisitorStats,
    approveVisitor as apiApproveVisitor,
    visitorCheckIn,
    visitorCheckOut,
    generateVisitorQRCode,
    type VisitorInfo,
    type VisitorStats,
  } from '/@/api/operations/visitor';
  import VisitorAppointmentModal from './components/VisitorAppointmentModal.vue';
  import VisitorCheckInModal from './components/VisitorCheckInModal.vue';
  import VisitorDetailModal from './components/VisitorDetailModal.vue';
  import QRCodeModal from './components/QRCodeModal.vue';

  // 响应式数据
  const loading = ref(false);
  const visitorList = ref<VisitorInfo[]>([]);
  const stats = ref<VisitorStats>({
    todayTotal: 0,
    todayVisiting: 0,
    weekTotal: 0,
    monthTotal: 0,
    pendingApproval: 0,
    avgVisitDuration: 0,
  });

  // 搜索和筛选
  const searchText = ref('');
  const statusFilter = ref('');

  // 分页
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 弹窗状态
  const appointmentModalVisible = ref(false);
  const checkInModalVisible = ref(false);
  const detailModalVisible = ref(false);
  const qrCodeModalVisible = ref(false);
  const selectedVisitor = ref<VisitorInfo | null>(null);
  const selectedQRCode = ref('');

  // 表格列定义
  const columns = [
    {
      title: '头像',
      key: 'photo',
      width: 80,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '公司',
      dataIndex: 'company',
      key: 'company',
    },
    {
      title: '被访人',
      dataIndex: 'visitee',
      key: 'visitee',
    },
    {
      title: '访问目的',
      dataIndex: 'visitPurpose',
      key: 'visitPurpose',
    },
    {
      title: '状态',
      key: 'status',
    },
    {
      title: '体温',
      key: 'temperature',
    },
    {
      title: '访问时间',
      dataIndex: 'visitTime',
      key: 'visitTime',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
    },
  ];

  // 方法
  const loadVisitorList = async () => {
    loading.value = true;
    try {
      const params = {
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
        name: searchText.value || undefined,
        status: statusFilter.value || undefined,
      };

      const response = await getVisitorList(params);
      visitorList.value = response.records;
      pagination.total = response.total;
    } catch (error) {
      message.error('获取访客列表失败');
    } finally {
      loading.value = false;
    }
  };

  const loadStats = async () => {
    try {
      stats.value = await getVisitorStats();
    } catch (error) {
      message.error('获取统计数据失败');
    }
  };

  const refreshData = () => {
    loadVisitorList();
    loadStats();
  };

  const handleSearch = () => {
    pagination.current = 1;
    loadVisitorList();
  };

  const handleTableChange = (pag: any) => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    loadVisitorList();
  };

  const showAppointmentModal = () => {
    appointmentModalVisible.value = true;
  };

  const showCheckInModal = () => {
    checkInModalVisible.value = true;
  };

  const approveVisitor = async (visitor: VisitorInfo, approved: boolean) => {
    try {
      await apiApproveVisitor(visitor.id, approved);
      message.success(approved ? '审批通过' : '审批拒绝');
      refreshData();
    } catch (error) {
      message.error('审批失败');
    }
  };

  const checkInVisitor = async (visitor: VisitorInfo) => {
    try {
      await visitorCheckIn(visitor.id, 36.5, 'green'); // 默认值，实际应该从设备获取
      message.success('签到成功');
      refreshData();
    } catch (error) {
      message.error('签到失败');
    }
  };

  const checkOutVisitor = async (visitor: VisitorInfo) => {
    try {
      await visitorCheckOut(visitor.id);
      message.success('签退成功');
      refreshData();
    } catch (error) {
      message.error('签退失败');
    }
  };

  const viewVisitorDetail = (visitor: VisitorInfo) => {
    selectedVisitor.value = visitor;
    detailModalVisible.value = true;
  };

  const showQRCode = async (visitor: VisitorInfo) => {
    try {
      const response = await generateVisitorQRCode(visitor.id);
      selectedQRCode.value = response.qrCode;
      selectedVisitor.value = visitor;
      qrCodeModalVisible.value = true;
    } catch (error) {
      message.error('生成二维码失败');
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'orange',
      approved: 'blue',
      visiting: 'green',
      completed: 'gray',
      rejected: 'red',
    };
    return colors[status] || 'gray';
  };

  const getStatusText = (status: string) => {
    const texts = {
      pending: '待审批',
      approved: '已审批',
      visiting: '访问中',
      completed: '已完成',
      rejected: '已拒绝',
    };
    return texts[status] || status;
  };

  const handleAppointmentSuccess = () => {
    appointmentModalVisible.value = false;
    refreshData();
  };

  const handleCheckInSuccess = () => {
    checkInModalVisible.value = false;
    refreshData();
  };

  // 生命周期
  onMounted(() => {
    refreshData();
  });
</script>
