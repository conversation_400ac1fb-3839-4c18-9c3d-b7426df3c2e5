<template>
  <div class="w-full h-full p-[0.8vw] flex flex-col">
    <!-- 参数列表 -->
    <div class="flex-1 bg-[#15274D]/30 rounded p-[0.6vw] overflow-auto custom-scrollbar">
      <div v-if="loading" class="flex justify-center items-center h-full">
        <div class="text-white text-[0.7vw]">加载中...</div>
      </div>
      <div v-else-if="error" class="flex justify-center items-center h-full">
        <div class="text-red-400 text-[0.7vw]">{{ error }}</div>
      </div>
      <div v-else-if="!deviceData || deviceData.length === 0" class="flex justify-center items-center h-full">
        <div class="text-yellow-400 text-[0.7vw]">未找到设备数据</div>
      </div>
      <div v-else class="space-y-[0.4vw]">
        <div
          v-for="param in rightParams"
          :key="param.id || param.remark"
          class="bg-[rgba(59,142,230,0.1)] p-[0.6vw] rounded hover:bg-[rgba(59,142,230,0.15)] transition-colors cursor-pointer"
          @click="handleParamClick(param)"
        >
          <!-- remark和name字段的左右布局 -->
          <div class="grid grid-cols-2 gap-[0.4vw] mb-[0.3vw]">
            <div class="text-[0.7vw] text-gray-300 break-all whitespace-normal leading-relaxed">{{ param.remark }}</div>
            <div class="text-[0.7vw] text-gray-300 break-all whitespace-normal leading-relaxed">{{ param.name }}</div>
          </div>
          <div class="text-[1vw] text-cyan-300 font-semibold break-all whitespace-normal tracking-wide">{{ param.valueData }}</div>
        </div>
      </div>
    </div>

    <!-- 弹窗式折线图面板 -->
    <Teleport to="body">
      <DeviceChartPanel
        v-for="panel in rightChartPanels"
        :key="panel.id"
        :device-id="panel.deviceId"
        :device-type="panel.deviceType"
        :device-name="panel.deviceName"
        :device-remark="panel.deviceRemark"
        :position="panel.position"
        @close="removeChartPanel(panel.id)"
      />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted } from 'vue';
  import { storeToRefs } from 'pinia/dist/pinia';
  import { useDeviceDetailStore } from './deviceDetailStore';
  import { useGlobalThreeStore } from '@/views/scene/store/globalThreeStore';
  import DeviceChartPanel from './DeviceChartPanel.vue';
  import type { DeviceDetailData } from '/@/api/scene';

  const deviceDetailStore = useDeviceDetailStore();
  const globalThreeStore = useGlobalThreeStore();
  const { deviceData, loading, error, chartPanels } = storeToRefs(deviceDetailStore);

  // 右侧显示的参数 - 显示后一半
  const rightParams = computed(() => {
    if (!deviceData.value || deviceData.value.length === 0) return [];
    const halfIndex = Math.ceil(deviceData.value.length / 2);
    return deviceData.value.slice(halfIndex);
  });

  // 获取右侧折线图面板
  const rightChartPanels = computed(() => {
    return deviceDetailStore.getChartPanelsBySourcePosition('right');
  });

  // 处理参数点击事件
  const handleParamClick = (param: DeviceDetailData) => {
    // 开发环境日志
    if (process.env.NODE_ENV === 'development') {
      console.log('[DeviceDetailRight] 点击参数:', param.remark);
    }

    // 添加折线图面板（因为这是右侧组件，图表显示在左侧）
    deviceDetailStore.addChartPanel(param, 'right');
  };

  // 移除折线图面板
  const removeChartPanel = (panelId: string) => {
    deviceDetailStore.removeChartPanel(panelId);
  };

  onMounted(() => {
    console.log('DeviceDetailRight 组件已挂载，当前设备编码:', globalThreeStore.currentDeviceCode);
    if (globalThreeStore.currentDeviceCode) {
      console.log('组件挂载时尝试加载设备数据');
      deviceDetailStore.loadDeviceData(globalThreeStore.currentDeviceCode);
    }
  });
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.5) rgba(21, 39, 77, 0.3);
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 0.4vw;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(21, 39, 77, 0.3);
    border-radius: 0.2vw;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.5);
    border-radius: 0.2vw;
  }
</style>
