<template>
  <div class="h-full flex flex-col gap-[0.8vw]">
    <!-- 资产概览 -->
    <div class="bg-[#15274D]/40 backdrop-blur-sm rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex flex-wrap items-center justify-between">
        <div class="flex items-center">
          <DashboardOutlined class="mr-[0.4vw] text-blue-400" />
          资产概览
          <LoadingOutlined v-if="loading" class="ml-[0.4vw] text-blue-400 animate-spin" />
        </div>
        <div class="flex items-center gap-[0.3vw]">
          <button
            class="px-[0.3vw] py-[0.15vw] text-[0.55vw] rounded transition-colors bg-[#15274D]/60 text-white/70 hover:bg-[#15274D]/80"
            @click="refreshData"
            title="刷新"
          >
            <ReloadOutlined />
          </button>
          <button
            class="px-[0.4vw] py-[0.15vw] text-[0.55vw] rounded transition-colors bg-blue-500 text-white hover:bg-blue-600"
            @click="showFullAssetDialog"
          >
            全部
          </button>
          <button
            class="px-[0.4vw] py-[0.15vw] text-[0.55vw] rounded transition-colors bg-green-500 text-white hover:bg-green-600"
            @click="showITAssetDialog"
          >
            IT
          </button>
          <button
            class="px-[0.4vw] py-[0.15vw] text-[0.55vw] rounded transition-colors bg-orange-500 text-white hover:bg-orange-600"
            @click="showHVACDialog"
          >
            暖通
          </button>
          <button
            class="px-[0.4vw] py-[0.15vw] text-[0.55vw] rounded transition-colors bg-purple-500 text-white hover:bg-purple-600"
            @click="showLifecycleDialog"
          >
            统一资产库
          </button>
        </div>
      </div>

      <!-- 统计数据 -->
      <div class="grid grid-cols-3 gap-[0.4vw] mb-[0.6vw]">
        <div v-for="stat in assetStats" :key="stat.label" class="bg-black/20 p-[0.6vw] rounded-lg">
          <div class="text-[0.9vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
        </div>
      </div>

      <!-- 机柜管理快捷入口 - 优化版本 -->
      <div class="bg-gradient-to-r from-purple-500/25 to-blue-500/25 border border-purple-500/40 rounded-lg p-[0.8vw] shadow-lg backdrop-blur-sm">
        <div class="flex items-center justify-between mb-[0.6vw] flex-wrap gap-[1vw]">
          <div class="flex items-center">
            <div class="bg-purple-500/20 p-[0.3vw] rounded-lg mr-[0.5vw]">
              <BoxPlotOutlined class="text-[1.4vw] text-purple-400" />
            </div>
            <div>
              <div class="text-[0.9vw] text-white font-semibold">机柜精细化管理</div>
              <div class="text-[0.65vw] text-gray-300">维保合同管理 · 设备生命周期</div>
            </div>
          </div>
          <div class="flex gap-[0.3vw]">
            <button
              class="px-[0.6vw] py-[0.3vw] bg-gradient-to-r from-orange-500 to-orange-600 text-white text-[0.65vw] rounded-md hover:from-orange-600 hover:to-orange-700 transition-all duration-200 flex items-center shadow-sm hover:shadow-md transform hover:scale-105"
              @click="showMaintenanceManagement"
              title="维保合同管理"
            >
              <ToolOutlined class="mr-[0.2vw] text-[0.7vw]" />
              维保管理
            </button>
          </div>
        </div>

        <!-- 机柜统计信息 - 优化版本 -->
        <div class="grid grid-cols-2 gap-[0.5vw]">
          <div
            class="bg-black/30 backdrop-blur-sm p-[0.6vw] rounded-lg text-center border border-white/10 hover:border-purple-400/30 transition-colors"
          >
            <div class="text-[1vw] text-purple-400 font-bold mb-[0.2vw]">{{ cabinetStats.total }}</div>
            <div class="text-[0.6vw] text-gray-300">总机柜数</div>
          </div>
          <div
            class="bg-black/30 backdrop-blur-sm p-[0.6vw] rounded-lg text-center border border-white/10 hover:border-yellow-400/30 transition-colors"
          >
            <div class="text-[1vw] text-yellow-400 font-bold mb-[0.2vw]">{{ cabinetStats.maintenanceAlerts }}</div>
            <div class="text-[0.6vw] text-gray-300">维保提醒</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 资产列表 -->
    <div class="flex-1 bg-[#15274D]/40 backdrop-blur-sm rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
        <div class="flex items-center">
          <BarChartOutlined class="mr-[0.4vw] text-green-400" />
          资产列表
          <LoadingOutlined v-if="loading" class="ml-[0.4vw] text-green-400 animate-spin" />
        </div>
        <div class="text-[0.6vw] text-gray-400" v-if="total > 0"> 共 {{ total }} 项 </div>
      </div>

      <!-- 简化的资产列表 -->
      <div
        class="space-y-[0.4vw] overflow-y-auto h-[calc(100%-2.5vw)] scrollbar scrollbar-rounded scrollbar-track-transparent scrollbar-thumb-white/30 pr-[0.4vw]"
      >
        <div v-if="loading" class="h-full flex items-center justify-center py-[2vw]">
          <LoadingOutlined class="text-[1.5vw] text-green-400 animate-spin" />
          <span class="ml-[0.6vw] text-[0.7vw] text-white">加载中...</span>
        </div>
        <div v-else-if="filteredAssets.length === 0" class="h-full flex items-center justify-center py-[2vw]">
          <span class="text-[0.7vw] text-gray-400">当前楼层暂无资产数据，请确保3D模型中有以"{{ props.currentFloor }}F"开头的对象</span>
        </div>
        <div
          v-else
          v-for="asset in filteredAssets"
          :key="asset.id"
          class="rounded-lg p-[0.7vw] transition-all duration-200 border"
          :class="
            asset.type === 'cabinet'
              ? 'bg-gradient-to-r from-purple-500/15 to-blue-500/15 border-purple-500/30 hover:border-purple-400/50 hover:from-purple-500/20 hover:to-blue-500/20 shadow-md'
              : 'bg-black/25 border-white/10 hover:bg-black/35 hover:border-white/20'
          "
        >
          <div class="flex items-center justify-between flex-wrap gap-[.5vw]">
            <div class="text-[0.75vw] text-white flex items-center">
              <div class="mr-[0.4vw] p-[0.2vw] rounded" :class="asset.type === 'cabinet' ? 'bg-purple-500/20' : 'bg-gray-500/20'">
                <component
                  :is="getAssetIcon(asset.type)"
                  class="text-[0.9vw]"
                  :class="asset.type === 'cabinet' ? 'text-purple-400' : 'text-gray-400'"
                />
              </div>
              <span class="font-medium">{{ asset.name }}</span>
            </div>
            <!-- 机柜特殊标识 - 优化版本 -->
            <div v-if="asset.type === 'cabinet'" class="flex items-center gap-[0.3vw]">
              <span
                class="px-[0.4vw] py-[0.15vw] bg-gradient-to-r from-purple-500/30 to-purple-600/30 text-purple-300 text-[0.55vw] rounded-md border border-purple-400/40 font-medium shadow-sm"
              >
                机柜设备
              </span>
              <span
                class="px-[0.4vw] py-[0.15vw] bg-gradient-to-r from-blue-500/30 to-blue-600/30 text-blue-300 text-[0.55vw] rounded-md border border-blue-400/40 font-medium shadow-sm"
              >
                {{ asset.usedU }}/{{ asset.totalU }}U
              </span>
            </div>
            <div class="text-[0.65vw] text-gray-300 font-medium">{{ asset.location }}</div>
          </div>

          <!-- 机柜额外信息 - 优化版本 -->
          <div
            v-if="asset.type === 'cabinet'"
            class="mt-[0.5vw] bg-gradient-to-r from-black/30 to-black/20 backdrop-blur-sm rounded-lg p-[0.5vw] border border-white/10"
          >
            <div class="flex justify-between items-center mb-[0.3vw]">
              <div class="text-[0.65vw] text-gray-300 font-medium">U位使用情况</div>
              <div class="text-[0.65vw] font-semibold" :class="getUsageRateClass(asset)"> 使用率: {{ getUsageRate(asset) }}% </div>
            </div>
            <div class="w-full bg-black/40 rounded-full h-[0.4vw] overflow-hidden border border-white/10">
              <div
                class="h-full transition-all duration-700 rounded-full shadow-sm"
                :class="getUsageBarClass(asset)"
                :style="{ width: `${getUsageRate(asset)}%` }"
              ></div>
            </div>
          </div>

          <div class="flex justify-between items-center mt-[0.4vw] gap-[1vw]">
            <div class="text-[0.65vw] text-gray-300 font-medium">
              类型: <span class="text-white">{{ getAssetTypeName(asset.type) }}</span>
            </div>
            <div class="flex gap-[0.2vw] flex-wrap">
              <button
                class="px-[0.4vw] py-[0.2vw] bg-gradient-to-r from-blue-500 to-blue-600 text-white text-[0.6vw] rounded hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex items-center shadow-sm hover:shadow-md"
                @click.stop="focusOnAsset(asset)"
                title="定位到设备"
              >
                <EyeOutlined class="mr-[0.15vw] text-[0.65vw]" />
                定位
              </button>
              <button
                class="px-[0.4vw] py-[0.2vw] bg-gradient-to-r from-cyan-500 to-cyan-600 text-white text-[0.6vw] rounded hover:from-cyan-600 hover:to-cyan-700 transition-all duration-200 flex items-center shadow-sm hover:shadow-md"
                @click.stop="showAssetDetail(asset)"
                title="查看详情"
              >
                <BarChartOutlined class="mr-[0.15vw] text-[0.65vw]" />
                详情
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 主界面分页组件 - 简化版 -->
      <div v-if="allFilteredAssets.length > 0" class="mt-[0.6vw] flex justify-center">
        <div class="bg-[rgba(59,142,230,0.1)] px-[0.6vw] py-[0.3vw] rounded-md">
          <div class="flex items-center gap-[0.6vw]">
            <!-- 简化分页信息 -->
            <div class="text-[0.55vw] text-gray-400">
              {{ mainPagination.current }}/{{ Math.ceil(mainPagination.total / mainPagination.pageSize) }}
            </div>

            <!-- 分页控件 -->
            <div class="flex items-center gap-[0.2vw]">
              <!-- 上一页 -->
              <button
                class="px-[0.3vw] py-[0.15vw] bg-[#15274D]/60 text-white/90 text-[0.55vw] rounded hover:bg-[#15274D]/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                :disabled="mainPagination.current <= 1"
                @click="handleMainPageChange(mainPagination.current - 1, mainPagination.pageSize)"
              >
                <LeftOutlined class="text-[0.45vw]" />
              </button>

              <!-- 页码 -->
              <div class="flex items-center gap-[0.1vw]">
                <template v-for="page in getPageNumbers(mainPagination)" :key="page">
                  <button
                    v-if="page !== '...'"
                    class="px-[0.3vw] py-[0.15vw] text-[0.55vw] rounded transition-colors min-w-[1.2vw] text-center"
                    :class="page === mainPagination.current ? 'bg-blue-500 text-white' : 'bg-[#15274D]/60 text-white/90 hover:bg-[#15274D]/80'"
                    @click="handleMainPageChange(page, mainPagination.pageSize)"
                  >
                    {{ page }}
                  </button>
                  <span v-else class="text-[0.55vw] text-gray-400 px-[0.1vw]">...</span>
                </template>
              </div>

              <!-- 下一页 -->
              <button
                class="px-[0.3vw] py-[0.15vw] bg-[#15274D]/60 text-white/90 text-[0.55vw] rounded hover:bg-[#15274D]/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                :disabled="mainPagination.current >= Math.ceil(mainPagination.total / mainPagination.pageSize)"
                @click="handleMainPageChange(mainPagination.current + 1, mainPagination.pageSize)"
              >
                <RightOutlined class="text-[0.45vw]" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 资产详情弹窗 -->
  <ModalDialog
    v-if="selectedAsset"
    v-model:visible="dialogVisible"
    :title="selectedAsset?.name"
    width="90vw"
    height="90vh"
    :show-footer="false"
    :icon-src="dashboardTitle"
    :enable-fullscreen="true"
    :default-fullscreen="true"
  >
    <CabinetDetail v-if="selectedAsset.type === 'cabinet'" v-model:cabinet="selectedAsset" @update:cabinet="updateAsset" />
    <AssetDetailPanel v-else :asset="selectedAsset" @close="closeDialog" />
  </ModalDialog>

  <!-- 完整资产管理弹窗 -->
  <ModalDialog
    v-model:visible="fullAssetDialogVisible"
    title="资产管理"
    width="90vw"
    height="90vh"
    :show-footer="false"
    :icon-src="dashboardTitle"
    :enable-fullscreen="true"
    :default-fullscreen="true"
  >
    <div class="p-[1vw] h-full flex flex-col">
      <!-- 搜索和筛选区域 -->
      <div class="bg-[rgba(59,142,230,0.1)] p-[0.8vw] rounded mb-[0.8vw]">
        <div class="flex flex-wrap gap-[0.8vw]">
          <div class="flex items-center">
            <span class="text-[0.7vw] text-gray-400 mr-[0.4vw]">资产类型:</span>
            <select
              v-model="assetTypeFilter"
              class="bg-[#15274D]/60 text-white/90 text-[0.6vw] rounded px-[0.4vw] py-[0.2vw] border-none outline-none"
              @change="loadDevices"
            >
              <option value="all">全部类型</option>
              <option value="cabinet">机柜</option>
              <option value="power">电力柜</option>
              <option value="ac">空调</option>
              <option value="other">其他设备</option>
            </select>
          </div>
          <div class="flex items-center">
            <span class="text-[0.7vw] text-gray-400 mr-[0.4vw]">楼层:</span>
            <select
              v-model="floorFilter"
              class="bg-[#15274D]/60 text-white/90 text-[0.6vw] rounded px-[0.4vw] py-[0.2vw] border-none outline-none"
              @change="loadDevices"
            >
              <option value="all">全部楼层</option>
              <option value="1">1楼</option>
              <option value="2">2楼</option>
              <option value="3">3楼</option>
              <option value="4">4楼</option>
              <option value="5">5楼</option>
            </select>
          </div>
          <div class="flex items-center">
            <span class="text-[0.7vw] text-gray-400 mr-[0.4vw]">状态:</span>
            <select v-model="statusFilter" class="bg-[#15274D]/60 text-white/90 text-[0.6vw] rounded px-[0.4vw] py-[0.2vw] border-none outline-none">
              <option value="all">全部状态</option>
              <option value="normal">正常</option>
              <option value="warning">警告</option>
              <option value="error">错误</option>
              <option value="offline">离线</option>
            </select>
          </div>
          <div class="flex items-center flex-1">
            <span class="text-[0.7vw] text-gray-400 mr-[0.4vw]">搜索:</span>
            <input
              v-model="searchQuery"
              class="flex-1 bg-[#15274D]/60 text-white/90 text-[0.6vw] rounded px-[0.4vw] py-[0.2vw] border-none outline-none"
              placeholder="输入资产名称、位置或型号搜索"
              @keyup.enter="loadDevices"
            />
            <button
              class="ml-[0.4vw] px-[0.6vw] py-[0.2vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-colors"
              @click="loadDevices"
            >
              搜索
            </button>
          </div>
        </div>
        <div class="flex justify-between items-center mt-[0.6vw]">
          <div class="text-[0.6vw] text-gray-400" v-if="dialogPagination.total > 0"> 共找到 {{ dialogPagination.total }} 项资产 </div>
          <div class="flex items-center">
            <button
              class="px-[0.6vw] py-[0.2vw] bg-[#15274D]/60 text-white/90 text-[0.6vw] rounded hover:bg-[#15274D]/80 transition-colors flex items-center"
              @click="refreshData"
              :disabled="loading"
            >
              <ReloadOutlined class="mr-[0.2vw]" :class="{ 'animate-spin': loading }" />
              刷新
            </button>
          </div>
        </div>
      </div>

      <!-- 资产表格 -->
      <div class="flex-1 bg-[rgba(59,142,230,0.1)] p-[0.8vw] rounded overflow-y-auto pr-[0.4vw]">
        <div v-if="loading" class="h-full flex items-center justify-center">
          <LoadingOutlined class="text-[2vw] text-blue-400 animate-spin" />
          <span class="ml-[0.8vw] text-[0.8vw] text-white">加载中...</span>
        </div>
        <div v-else-if="fullFilteredAssets.length === 0" class="h-full flex items-center justify-center">
          <span class="text-[0.8vw] text-gray-400">暂无资产数据</span>
        </div>
        <table v-else class="w-full border-collapse table-fixed">
          <thead class="sticky top-0 bg-[rgba(23,43,77,0.9)] z-10">
            <tr class="text-left border-b border-white/10">
              <th class="text-[0.7vw] text-gray-400 p-[0.4vw] w-[18%]">资产名称</th>
              <th class="text-[0.7vw] text-gray-400 p-[0.4vw] w-[12%]">类型</th>
              <th class="text-[0.7vw] text-gray-400 p-[0.4vw] w-[15%]">位置</th>
              <th class="text-[0.7vw] text-gray-400 p-[0.4vw] w-[10%]">状态</th>
              <th class="text-[0.7vw] text-gray-400 p-[0.4vw] w-[15%]">型号</th>
              <th class="text-[0.7vw] text-gray-400 p-[0.4vw] w-[15%]">制造商</th>
              <th class="text-[0.7vw] text-gray-400 p-[0.4vw] w-[15%]">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="asset in fullFilteredAssets"
              :key="asset.id"
              class="border-b border-white/5 hover:bg-white/5 transition-colors cursor-pointer"
              @click="showAssetDetail(asset)"
            >
              <td class="text-[0.7vw] text-white p-[0.4vw] truncate">
                <div class="flex items-center">
                  <component :is="getAssetIcon(asset.type)" class="mr-[0.3vw] text-[0.8vw] flex-shrink-0" />
                  <span class="truncate" :title="asset.name">{{ asset.name }}</span>
                </div>
              </td>
              <td class="text-[0.7vw] text-gray-400 p-[0.4vw] truncate" :title="getAssetTypeName(asset.type)">{{ getAssetTypeName(asset.type) }}</td>
              <td class="text-[0.7vw] text-gray-400 p-[0.4vw] truncate" :title="asset.location">{{ asset.location }}</td>
              <td class="text-[0.7vw] p-[0.4vw] truncate">
                <span :class="getStatusClass(asset)" :title="getStatusText(asset)">{{ getStatusText(asset) }}</span>
              </td>
              <td class="text-[0.7vw] text-gray-400 p-[0.4vw] truncate" :title="asset.model || '-'">{{ asset.model || '-' }}</td>
              <td class="text-[0.7vw] text-gray-400 p-[0.4vw] truncate" :title="asset.manufacturer || '-'">{{ asset.manufacturer || '-' }}</td>
              <td class="text-[0.7vw] text-gray-400 p-[0.4vw]">
                <div class="flex gap-[0.2vw]">
                  <button
                    class="px-[0.3vw] py-[0.1vw] bg-blue-500 text-white text-[0.55vw] rounded hover:bg-blue-600 transition-colors flex items-center"
                    @click.stop="focusOnAsset(asset)"
                    title="定位到设备"
                  >
                    <EyeOutlined class="mr-[0.15vw]" />
                    定位
                  </button>
                  <button
                    class="px-[0.3vw] py-[0.1vw] bg-cyan-500 text-white text-[0.55vw] rounded hover:bg-cyan-600 transition-colors flex items-center"
                    @click.stop="showAssetDetail(asset)"
                    title="查看详情"
                  >
                    <BarChartOutlined class="mr-[0.15vw]" />
                    详情
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- 弹窗分页组件 - 简化版 -->
        <div v-if="allFullFilteredAssets.length > 0" class="mt-[0.6vw] flex justify-center">
          <div class="bg-[rgba(23,43,77,0.9)] px-[0.6vw] py-[0.3vw] rounded-md">
            <div class="flex items-center gap-[0.6vw]">
              <!-- 简化分页信息 -->
              <div class="text-[0.55vw] text-gray-400">
                {{ dialogPagination.current }}/{{ Math.ceil(dialogPagination.total / dialogPagination.pageSize) }}
              </div>

              <!-- 分页控件 -->
              <div class="flex items-center gap-[0.2vw]">
                <!-- 上一页 -->
                <button
                  class="px-[0.3vw] py-[0.15vw] bg-[#15274D]/60 text-white/90 text-[0.55vw] rounded hover:bg-[#15274D]/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  :disabled="dialogPagination.current <= 1"
                  @click="handleDialogPageChange(dialogPagination.current - 1, dialogPagination.pageSize)"
                >
                  <LeftOutlined class="text-[0.45vw]" />
                </button>

                <!-- 页码 -->
                <div class="flex items-center gap-[0.1vw]">
                  <template v-for="page in getPageNumbers(dialogPagination)" :key="page">
                    <button
                      v-if="page !== '...'"
                      class="px-[0.3vw] py-[0.15vw] text-[0.55vw] rounded transition-colors min-w-[1.2vw] text-center"
                      :class="page === dialogPagination.current ? 'bg-blue-500 text-white' : 'bg-[#15274D]/60 text-white/90 hover:bg-[#15274D]/80'"
                      @click="handleDialogPageChange(page, dialogPagination.pageSize)"
                    >
                      {{ page }}
                    </button>
                    <span v-else class="text-[0.55vw] text-gray-400 px-[0.1vw]">...</span>
                  </template>
                </div>

                <!-- 下一页 -->
                <button
                  class="px-[0.3vw] py-[0.15vw] bg-[#15274D]/60 text-white/90 text-[0.55vw] rounded hover:bg-[#15274D]/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  :disabled="dialogPagination.current >= Math.ceil(dialogPagination.total / dialogPagination.pageSize)"
                  @click="handleDialogPageChange(dialogPagination.current + 1, dialogPagination.pageSize)"
                >
                  <RightOutlined class="text-[0.45vw]" />
                </button>
              </div>

              <!-- 简化每页显示数量 -->
              <select
                v-model="dialogPagination.pageSize"
                class="bg-[#15274D]/60 text-white/90 text-[0.55vw] rounded px-[0.25vw] py-[0.1vw] border-none outline-none"
                @change="handleDialogPageChange(1, dialogPagination.pageSize)"
              >
                <option v-for="size in dialogPagination.pageSizeOptions" :key="size" :value="parseInt(size)"> {{ size }}条/页 </option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ModalDialog>

  <!-- IT资产管理弹窗 -->
  <ModalDialog
    v-model:visible="itAssetDialogVisible"
    title="IT资产设施管理"
    width="90vw"
    height="90vh"
    :show-footer="false"
    :icon-src="dashboardTitle"
    :enable-fullscreen="true"
    :default-fullscreen="true"
  >
    <ITAssetManagement />
  </ModalDialog>

  <!-- 暖通设施管理弹窗 -->
  <ModalDialog
    v-model:visible="hvacDialogVisible"
    title="暖通设施管理"
    width="90vw"
    height="90vh"
    :show-footer="false"
    :icon-src="dashboardTitle"
    :enable-fullscreen="true"
    :default-fullscreen="true"
  >
    <HVACManagement />
  </ModalDialog>

  <!-- 统一资产库管理弹窗 -->
  <ModalDialog
    v-model:visible="lifecycleDialogVisible"
    title="统一资产库"
    width="90vw"
    height="90vh"
    :show-footer="false"
    :icon-src="dashboardTitle"
    :enable-fullscreen="true"
    :default-fullscreen="true"
  >
    <UnifiedAssetLibrary />
  </ModalDialog>

  <!-- 维保管理弹窗 -->
  <ModalDialog
    v-model:visible="maintenanceManagementDialogVisible"
    title="维保合同管理"
    width="90vw"
    height="90vh"
    :show-footer="false"
    :icon-src="dashboardTitle"
    :enable-fullscreen="true"
    :default-fullscreen="true"
  >
    <div v-if="selectedAssetForMaintenance" class="h-full">
      <MaintenanceManager :asset="selectedAssetForMaintenance" />
    </div>
  </ModalDialog>
</template>

<script setup>
  import { ref, computed, onMounted, watch } from 'vue';
  import {
    DashboardOutlined,
    BarChartOutlined,
    BoxPlotOutlined,
    ThunderboltOutlined,
    CloudOutlined,
    DesktopOutlined,
    LoadingOutlined,
    ReloadOutlined,
    EnvironmentOutlined,
    EyeOutlined,
    ToolOutlined,
    LeftOutlined,
    RightOutlined,
  } from '@ant-design/icons-vue';
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import CabinetDetail from '@/views/scene/pages/components/asset/components/CabinetDetail.vue';
  import AssetDetailPanel from '@/views/scene/pages/components/asset/AssetDetailPanel.vue';
  import ITAssetManagement from '@/views/scene/pages/components/asset/components/ITAssetManagement.vue';
  import HVACManagement from '@/views/scene/pages/components/asset/components/HVACManagement.vue';
  import MaintenanceManager from '@/views/scene/pages/components/asset/components/MaintenanceManager.vue';
  import UnifiedAssetLibrary from '@/views/scene/pages/components/asset/components/UnifiedAssetLibrary.vue';
  import { getDeviceList, getDeviceById } from '/@/api/device';
  import { ModelLoaderManager } from '@/views/scene/lib/load/ModelLoaderManager';
  import { CameraController } from '@/views/scene/lib/CameraController';
  import { SceneManager } from '@/views/scene/lib/SceneManager';
  import { ObjectSelection } from '@/views/scene/lib/selection/ObjectSelection';
  import { isFloorDevice, extractFloorNumber } from '@/views/scene/utils/deviceIdentifier';
  import * as THREE from 'three';

  // 资产统计数据
  const assetStats = ref([
    { label: '总资产数', value: '0台', valueClass: 'text-white' },
    { label: '机柜使用率', value: '0%', valueClass: 'text-blue-400' },
    { label: '设备在线率', value: '0%', valueClass: 'text-green-400' },
  ]);

  const props = defineProps({
    currentFloor: {
      type: String,
      required: true,
    },
  });

  // 弹窗状态
  const fullAssetDialogVisible = ref(false);
  const itAssetDialogVisible = ref(false);
  const hvacDialogVisible = ref(false);
  const lifecycleDialogVisible = ref(false);
  const maintenanceManagementDialogVisible = ref(false);

  // 选中的资产
  const selectedAssetForMaintenance = ref(null);

  // 筛选条件
  const assetTypeFilter = ref('all');
  const floorFilter = ref('all');
  const statusFilter = ref('all');
  const searchQuery = ref('');

  // 加载状态
  const loading = ref(false);
  const pageParams = ref({
    pageNo: 1,
    pageSize: 100,
  });

  // 主界面分页状态
  const mainPagination = ref({
    current: 1,
    pageSize: 15,
    total: 0,
    pageSizeOptions: ['15', '30'],
  });

  // 弹窗分页状态
  const dialogPagination = ref({
    current: 1,
    pageSize: 20,
    total: 0,
    pageSizeOptions: ['20', '50'],
  });

  // 总数
  const total = ref(0);

  // 资产数据
  const assets = ref([]);

  // 机柜统计数据
  const cabinetStats = computed(() => {
    const cabinets = assets.value.filter((asset) => asset.type === 'cabinet');
    const total = cabinets.length;

    if (total === 0) {
      return {
        total: 0,
        avgUsage: 0,
        availableU: 0,
        maintenanceAlerts: 0,
      };
    }

    const totalUsedU = cabinets.reduce((sum, cabinet) => sum + cabinet.usedU, 0);
    const totalCapacityU = cabinets.reduce((sum, cabinet) => sum + cabinet.totalU, 0);
    const avgUsage = Math.round((totalUsedU / totalCapacityU) * 100);
    const availableU = totalCapacityU - totalUsedU;

    // 模拟维保提醒数量
    const maintenanceAlerts = Math.floor(total * 0.3); // 假设30%的机柜有维保提醒

    return {
      total,
      avgUsage,
      availableU,
      maintenanceAlerts,
    };
  });

  // 从3D模型中获取设备对象
  const getDevicesFromModel = () => {
    try {
      const modelLoader = ModelLoaderManager.getInstance();
      const models = modelLoader.getCurrentModels();
      const devices = [];
      const processedIds = new Set(); // 避免重复处理同一设备

      // 获取当前楼层号
      const currentFloorNumber = props.currentFloor;

      // 遍历所有模型
      models.forEach((model) => {
        model.traverse((object) => {
          // 跳过已处理的对象
          if (processedIds.has(object.uuid)) return;
          processedIds.add(object.uuid);

          // 检查是否是当前楼层的设备 - 使用统一的设备识别逻辑
          if (object.name && isFloorDevice(object.name)) {
            const deviceFloor = extractFloorNumber(object.name);
            if (deviceFloor && deviceFloor === currentFloorNumber.toString()) {
              // 获取对象的世界坐标
              const position = new THREE.Vector3();
              object.getWorldPosition(position);

              // 计算对象的包围盒
              const boundingBox = new THREE.Box3().setFromObject(object);
              const size = new THREE.Vector3();
              boundingBox.getSize(size);

              // 添加设备
              devices.push({
                object: object,
                name: object.name,
                position: position,
                size: size,
              });
            }
          }
        });
      });

      console.log(`[AssetManager] 从3D模型中找到 ${devices.length} 个设备`);
      return devices;
    } catch (error) {
      console.error('从3D模型获取设备失败:', error);
      return [];
    }
  };

  // 生成机柜设备数据
  const generateCabinetDevices = () => {
    const deviceTypes = ['server', 'network', 'storage', 'ups'];
    const deviceNames = {
      server: ['Web服务器', '数据库服务器', '应用服务器'],
      network: ['核心交换机', '汇聚交换机', '路由器'],
      storage: ['存储阵列', 'NAS设备', '备份设备'],
      ups: ['UPS电源', '配电单元', '监控设备'],
    };

    const devices = [];
    let currentU = 1;
    const deviceCount = Math.floor(Math.random() * 5) + 2; // 2-6个设备

    for (let i = 0; i < deviceCount && currentU <= 40; i++) {
      const type = deviceTypes[Math.floor(Math.random() * deviceTypes.length)];
      const names = deviceNames[type];
      const name = names[Math.floor(Math.random() * names.length)];
      const uSize = Math.floor(Math.random() * 3) + 1; // 1-3U

      if (currentU + uSize <= 42) {
        devices.push({
          id: Date.now() + i,
          name: `${name}-${String(i + 1).padStart(2, '0')}`,
          type,
          startU: currentU,
          endU: currentU + uSize - 1,
          uSize,
        });
        currentU += uSize + Math.floor(Math.random() * 2); // 可能有间隔
      }
    }

    return devices;
  };

  // 生成预留U位
  const generateReservedSlots = () => {
    const reservedCount = Math.floor(Math.random() * 3); // 0-2个预留位
    const reserved = [];

    for (let i = 0; i < reservedCount; i++) {
      const slot = Math.floor(Math.random() * 42) + 1;
      if (!reserved.includes(slot)) {
        reserved.push(slot);
      }
    }

    return reserved;
  };

  // 将3D设备对象转换为资产数据格式
  const convertDevicesToAssets = (devices) => {
    return devices.map((device, index) => {
      // 从设备名称中提取信息 - 使用统一的楼层提取函数
      const floorNumber = extractFloorNumber(device.name) || '未知';

      // 确定设备类型 - 改进识别逻辑
      let type = 'other';
      const name = device.name.toLowerCase();

      // 机柜识别 - 扩展识别关键词
      if (
        name.includes('jg') ||
        name.includes('机柜') ||
        name.includes('cabinet') ||
        name.includes('rack') ||
        name.includes('服务器柜') ||
        name.includes('网络柜')
      ) {
        type = 'cabinet';
      }
      // 配电设备识别
      else if (
        name.includes('pdg') ||
        name.includes('配电') ||
        name.includes('电力') ||
        name.includes('power') ||
        name.includes('ups') ||
        name.includes('电源')
      ) {
        type = 'power';
      }
      // 空调设备识别
      else if (name.includes('空调') || name.includes('ac') || name.includes('hvac') || name.includes('制冷') || name.includes('cooling')) {
        type = 'ac';
      }
      // 如果没有明确的类型标识，但是设备名称符合某些模式，也可能是机柜
      else if (name.match(/\d+f.*\d+/i)) {
        // 如果名称包含楼层信息和数字编号，很可能是机柜
        type = 'cabinet';
      }

      // 生成随机但合理的属性
      const randomId = `ASSET-${floorNumber}${index + 100}`;
      const randomModel = `Model-${Math.floor(Math.random() * 1000)}`;
      const randomStatus = Math.random() > 0.8 ? 'warning' : 'normal';

      // 构建资产对象
      return {
        id: randomId,
        name: device.name,
        location: `${floorNumber}楼-${Math.random() > 0.5 ? '主机房' : '配电室'}`,
        type,
        model: randomModel,
        manufacturer: '示例厂商',
        status: randomStatus,
        // 保存原始3D对象的引用，用于定位
        objectUUID: device.object.uuid,
        // 根据设备类型添加特定属性
        ...(type === 'cabinet'
          ? (() => {
              const cabinetDevices = generateCabinetDevices();
              const usedU = cabinetDevices.reduce((sum, dev) => sum + dev.uSize, 0);
              return {
                usedU,
                totalU: 42,
                devices: cabinetDevices,
                reservedSlots: generateReservedSlots(),
              };
            })()
          : {}),
        ...(type === 'power'
          ? {
              load: Math.floor(Math.random() * 40) + 20,
              capacity: 60,
            }
          : {}),
        ...(type === 'ac'
          ? {
              status: Math.random() > 0.9 ? 'stopped' : 'running',
              temperature: Math.floor(Math.random() * 5) + 20,
              humidity: Math.floor(Math.random() * 20) + 35,
            }
          : {}),
        // 添加一些额外的详细信息
        serialNumber: `SN-${Math.floor(Math.random() * 10000)}`,
        purchaseDate: '2022-01-01',
        maintenanceEndDate: '2025-12-31',
        // 添加维保记录
        maintenanceRecords: [
          {
            id: 1,
            type: '定期检查',
            date: '2023-06-15',
            description: '设备运行正常，已完成例行检查和清洁',
          },
        ],
        // 添加告警记录
        alarmRecords:
          randomStatus === 'warning'
            ? [
                {
                  id: 1,
                  level: 'warning',
                  message: '设备温度偏高',
                  time: '2023-10-25 14:30',
                },
              ]
            : [],
      };
    });
  };

  // 加载设备数据
  const loadDevices = async () => {
    try {
      loading.value = true;

      // 使用3D模型中的设备作为假数据源
      const modelDevices = getDevicesFromModel();
      const deviceData = convertDevicesToAssets(modelDevices);

      assets.value = deviceData;
      total.value = deviceData.length;

      // 更新统计数据
      assetStats.value = [
        { label: '总资产数', value: `${total.value}台`, valueClass: 'text-white' },
        { label: '机柜使用率', value: '76.5%', valueClass: 'text-blue-400' },
        { label: '设备在线率', value: '98.2%', valueClass: 'text-green-400' },
      ];

      console.log(`[AssetManager] 已加载 ${deviceData.length} 个资产数据`);

      // 调试信息：显示设备类型分布
      const typeCount = deviceData.reduce((acc, device) => {
        acc[device.type] = (acc[device.type] || 0) + 1;
        return acc;
      }, {});
      console.log(`[AssetManager] 设备类型分布:`, typeCount);

      // 调试信息：显示机柜设备列表
      const cabinets = deviceData.filter((device) => device.type === 'cabinet');
      console.log(
        `[AssetManager] 找到 ${cabinets.length} 个机柜设备:`,
        cabinets.map((c) => c.name)
      );

      /* 注释掉原来的API调用代码
      const params = {
        ...pageParams.value,
        floorInfo: props.currentFloor !== 'all' ? parseInt(props.currentFloor) : undefined,
      };

      if (assetTypeFilter.value !== 'all') {
        params.deviceType = assetTypeFilter.value;
      }

      if (searchQuery.value) {
        params.name = searchQuery.value;
      }

      const res = await getDeviceList(params);
      if (res.success) {
        // 转换API数据为组件所需格式
        const deviceData = res.result.records.map((device) => {
          // 根据设备类型确定资产类型
          let type = 'other';
          if (device.deviceType?.includes('机柜')) {
            type = 'cabinet';
          } else if (device.deviceType?.includes('电力') || device.deviceType?.includes('配电')) {
            type = 'power';
          } else if (device.deviceType?.includes('空调')) {
            type = 'ac';
          }

          // 构建资产对象
          return {
            id: device.deviceId,
            name: device.name,
            location: `${device.floorInfo}楼-${device.roomName || '未知区域'}`,
            type,
            model: device.modelName,
            manufacturer: '未知', // API中没有制造商信息
            status: 'normal', // 默认状态
            // 根据设备类型添加特定属性
            ...(type === 'cabinet'
              ? {
                  usedU: 30, // 默认值，实际应从API获取
                  totalU: 42,
                }
              : {}),
            ...(type === 'power'
              ? {
                  load: 30, // 默认值，实际应从API获取
                  capacity: 60,
                }
              : {}),
            ...(type === 'ac'
              ? {
                  status: 'running',
                  temperature: device.temperature || 22,
                  humidity: 45, // 默认值，实际应从API获取
                }
              : {}),
          };
        });

        assets.value = deviceData;
        total.value = res.result.total;

        // 更新统计数据
        assetStats.value = [
          { label: '总资产数', value: `${total.value}台`, valueClass: 'text-white' },
          { label: '机柜使用率', value: '76.5%', valueClass: 'text-blue-400' }, // 可以根据实际数据计算
          { label: '设备在线率', value: '98.2%', valueClass: 'text-green-400' }, // 可以根据实际数据计算
        ];
      }
      */
    } catch (error) {
      console.error('加载设备数据失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 刷新数据
  const refreshData = () => {
    loadDevices();
  };

  // 监听当前楼层变化
  watch(
    () => props.currentFloor,
    () => {
      loadDevices();
    }
  );

  // 组件挂载时加载数据
  onMounted(() => {
    console.log(`[AssetManager] 组件挂载，当前楼层: ${props.currentFloor}`);
    loadDevices();
  });

  // 根据当前楼层筛选资产（简化版）
  const allFilteredAssets = computed(() => {
    if (loading.value) {
      return [];
    }

    // 使用统一的设备识别逻辑检查楼层
    return assets.value.filter((asset) => {
      // 优先使用统一的楼层提取函数
      const assetFloor = extractFloorNumber(asset.name);
      if (assetFloor && assetFloor === props.currentFloor.toString()) {
        return true;
      }
      // 兼容旧的位置筛选逻辑
      return asset.location.startsWith(`${props.currentFloor}楼`);
    });
  });

  // 主界面分页后的资产列表
  const filteredAssets = computed(() => {
    const allAssets = allFilteredAssets.value;
    mainPagination.value.total = allAssets.length;

    const start = (mainPagination.value.current - 1) * mainPagination.value.pageSize;
    const end = start + mainPagination.value.pageSize;
    return allAssets.slice(start, end);
  });

  // 完整筛选资产（弹窗版）- 所有筛选后的资产
  const allFullFilteredAssets = computed(() => {
    if (loading.value) {
      return [];
    }

    return assets.value.filter((asset) => {
      // 楼层筛选 - 使用统一的设备识别逻辑
      if (floorFilter.value !== 'all') {
        const assetFloor = extractFloorNumber(asset.name);
        // 检查名称中的楼层标识
        if (!(assetFloor && assetFloor === floorFilter.value.toString()) && !asset.location.startsWith(`${floorFilter.value}楼`)) {
          return false;
        }
      }

      // 类型筛选
      if (assetTypeFilter.value !== 'all' && asset.type !== assetTypeFilter.value) {
        return false;
      }

      // 状态筛选
      if (statusFilter.value !== 'all') {
        if (asset.type === 'ac') {
          if (statusFilter.value === 'normal' && asset.status !== 'running') return false;
          if (statusFilter.value !== 'normal' && asset.status === 'running') return false;
        } else {
          if (asset.status !== statusFilter.value) return false;
        }
      }

      // 搜索查询
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        return (
          asset.name.toLowerCase().includes(query) ||
          asset.location.toLowerCase().includes(query) ||
          (asset.model && asset.model.toLowerCase().includes(query)) ||
          (asset.manufacturer && asset.manufacturer.toLowerCase().includes(query))
        );
      }

      return true;
    });
  });

  // 弹窗分页后的资产列表
  const fullFilteredAssets = computed(() => {
    const allAssets = allFullFilteredAssets.value;
    dialogPagination.value.total = allAssets.length;

    const start = (dialogPagination.value.current - 1) * dialogPagination.value.pageSize;
    const end = start + dialogPagination.value.pageSize;
    return allAssets.slice(start, end);
  });

  const dialogVisible = ref(false);
  const selectedAsset = ref(null);

  // 显示完整资产管理弹窗
  const showFullAssetDialog = () => {
    fullAssetDialogVisible.value = true;
  };

  // 显示资产详情
  const showAssetDetail = async (asset) => {
    try {
      loading.value = true;

      // 使用假数据而不是调用API
      // 添加一些额外的详细信息
      selectedAsset.value = {
        ...asset,
        deviceId: asset.id,
        lengthInfo: Math.floor(Math.random() * 50) + 50, // 50-100cm
        high: Math.floor(Math.random() * 100) + 100, // 100-200cm
        wide: Math.floor(Math.random() * 40) + 60, // 60-100cm
        weight: Math.floor(Math.random() * 200) + 50, // 50-250kg
        createTime: '2022-01-01 08:00:00',
        updateTime: '2023-10-01 15:30:00',
      };

      dialogVisible.value = true;

      /* 注释掉原来的API调用代码
      // 获取详细信息
      const res = await getDeviceById(asset.id);
      if (res.success) {
        const deviceDetail = res.result;
        // 合并详细信息
        selectedAsset.value = {
          ...asset,
          // 添加API返回的详细信息
          deviceId: deviceDetail.deviceId,
          lengthInfo: deviceDetail.lengthInfo,
          high: deviceDetail.high,
          wide: deviceDetail.wide,
          weight: deviceDetail.weight,
          createTime: deviceDetail.createTime,
          updateTime: deviceDetail.updateTime,
        };
        dialogVisible.value = true;
      }
      */
    } catch (error) {
      console.error('获取设备详情失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 关闭详情弹窗
  const closeDialog = () => {
    dialogVisible.value = false;
    selectedAsset.value = null;
  };

  // 更新资产信息
  const updateAsset = (updatedAsset) => {
    const index = assets.value.findIndex((a) => a.id === updatedAsset.id);
    if (index !== -1) {
      assets.value[index] = updatedAsset;

      // 不再调用loadDevices()，而是直接更新本地数据
      // 这样可以避免重新从3D模型中获取数据，保持用户的编辑
      console.log(`[AssetManager] 已更新资产: ${updatedAsset.name}`);
    }
  };

  // 定位到资产对应的3D模型
  const focusOnAsset = async (asset) => {
    try {
      console.log(`[AssetManager] 尝试定位到资产: ${asset.name}`);

      // 获取场景管理器
      const sceneManager = SceneManager.getInstance();

      if (!sceneManager) {
        console.error('[AssetManager] 无法获取场景管理器');
        return;
      }

      // 获取ObjectSelection实例
      const objectSelection = ObjectSelection.getInstance();

      if (!objectSelection || !objectSelection.objectDoubleClickHandler) {
        console.error('[AssetManager] 无法获取ObjectSelection或ObjectDoubleClickHandler');
        return;
      }

      // 在场景中查找与资产匹配的对象
      let targetObject = null;

      // 首先尝试使用UUID查找对象
      if (asset.objectUUID) {
        targetObject = sceneManager.scene.getObjectByProperty('uuid', asset.objectUUID);
      }

      // 如果通过UUID没有找到，则尝试通过名称查找
      if (!targetObject) {
        sceneManager.scene.traverse((object) => {
          // 如果已经找到目标对象，跳过
          if (targetObject) return;

          // 检查对象名称是否与资产名称匹配
          if (object.name === asset.name) {
            targetObject = object;
          }
        });
      }

      if (!targetObject) {
        console.warn(`[AssetManager] 未找到与资产 "${asset.name}" 匹配的3D对象`);
        return;
      }

      console.log(`[AssetManager] 找到资产对应的3D对象: ${targetObject.name}, UUID: ${targetObject.uuid}`);

      // 如果是在弹窗中点击的，先关闭弹窗
      if (fullAssetDialogVisible.value) {
        fullAssetDialogVisible.value = false;
      }

      // 使用与双击设备相同的功能
      const doubleClickHandler = objectSelection.objectDoubleClickHandler;

      // 设置观察模式状态
      doubleClickHandler.isDeviceObserving = true;

      // 禁用高亮
      doubleClickHandler.disableHighlighting();

      // 调用双击处理函数，与双击设备功能相同
      // 使用await确保异步操作完成
      await doubleClickHandler.handleObjectDoubleSelection(targetObject);

      console.log(`[AssetManager] 已定位到资产: ${asset.name}`);
    } catch (error) {
      console.error('[AssetManager] 定位到资产失败:', error);
    }
  };

  // 获取资产图标
  const getAssetIcon = (type) => {
    switch (type) {
      case 'cabinet':
        return BoxPlotOutlined;
      case 'power':
        return ThunderboltOutlined;
      case 'ac':
        return CloudOutlined;
      default:
        return DesktopOutlined;
    }
  };

  // 获取资产类型名称
  const getAssetTypeName = (type) => {
    switch (type) {
      case 'cabinet':
        return '机柜';
      case 'power':
        return '电力柜';
      case 'ac':
        return '空调';
      default:
        return '其他设备';
    }
  };

  // 获取状态文本
  const getStatusText = (asset) => {
    if (asset.type === 'ac') {
      return asset.status === 'running' ? '运行中' : '已停止';
    }

    const statusMap = {
      normal: '正常',
      warning: '警告',
      error: '错误',
      offline: '离线',
    };
    return statusMap[asset.status] || '未知';
  };

  // 获取状态样式
  const getStatusClass = (asset) => {
    if (asset.type === 'ac') {
      return asset.status === 'running' ? 'text-green-400' : 'text-red-400';
    }

    const classMap = {
      normal: 'text-green-400',
      warning: 'text-yellow-400',
      error: 'text-red-400',
      offline: 'text-gray-400',
    };
    return classMap[asset.status] || 'text-gray-400';
  };

  // 显示IT资产管理弹窗
  const showITAssetDialog = () => {
    itAssetDialogVisible.value = true;
  };

  // 显示暖通设施管理弹窗
  const showHVACDialog = () => {
    hvacDialogVisible.value = true;
  };

  // 显示生命周期管理弹窗
  const showLifecycleDialog = () => {
    lifecycleDialogVisible.value = true;
  };

  // 机柜相关功能函数
  const getUsageRate = (cabinet) => {
    return Math.round((cabinet.usedU / cabinet.totalU) * 100);
  };

  const getUsageRateClass = (cabinet) => {
    const rate = getUsageRate(cabinet);
    if (rate >= 90) return 'text-red-400';
    if (rate >= 75) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getUsageBarClass = (cabinet) => {
    const rate = getUsageRate(cabinet);
    if (rate >= 90) return 'bg-gradient-to-r from-red-500 to-red-600';
    if (rate >= 75) return 'bg-gradient-to-r from-yellow-500 to-yellow-600';
    return 'bg-gradient-to-r from-green-500 to-green-600';
  };

  // 创建示例机柜数据
  const createSampleCabinet = () => {
    const cabinetDevices = generateCabinetDevices();
    const usedU = cabinetDevices.reduce((sum, dev) => sum + dev.uSize, 0);

    return {
      id: `SAMPLE-CABINET-${Date.now()}`,
      name: `${props.currentFloor}F-示例机柜-01`,
      location: `${props.currentFloor}楼-主机房`,
      type: 'cabinet',
      model: 'Model-DEMO-42U',
      manufacturer: '示例厂商',
      status: 'normal',
      usedU,
      totalU: 42,
      devices: cabinetDevices,
      reservedSlots: generateReservedSlots(),
      serialNumber: `SN-DEMO-${Math.floor(Math.random() * 10000)}`,
      purchaseDate: '2022-01-01',
      maintenanceEndDate: '2025-12-31',
      maintenanceRecords: [
        {
          id: 1,
          type: '定期检查',
          date: '2023-06-15',
          description: '设备运行正常，已完成例行检查和清洁',
        },
      ],
      alarmRecords: [],
    };
  };

  // 显示维保管理弹窗
  const showMaintenanceManagement = () => {
    console.log(`[AssetManager] 尝试显示维保管理，当前资产数量: ${assets.value.length}`);

    // 首先尝试从当前楼层的资产中找到机柜
    let firstCabinet = filteredAssets.value.find((asset) => asset.type === 'cabinet');

    if (!firstCabinet) {
      // 如果当前楼层没有机柜，尝试从所有资产中找
      firstCabinet = assets.value.find((asset) => asset.type === 'cabinet');
      console.log(`[AssetManager] 当前楼层没有机柜，从所有资产中查找: ${firstCabinet ? '找到' : '未找到'}`);
    }

    if (!firstCabinet) {
      // 如果还是没有找到，创建一个示例机柜
      console.log(`[AssetManager] 没有找到任何机柜设备，创建示例机柜数据`);
      firstCabinet = createSampleCabinet();
      // 将示例机柜添加到资产列表中
      assets.value.push(firstCabinet);
    }

    selectedAssetForMaintenance.value = firstCabinet;
    maintenanceManagementDialogVisible.value = true;
    console.log(`[AssetManager] 已选择资产进行维保管理: ${firstCabinet.name}`);
  };

  // 快速访问维保管理
  const quickAccessMaintenance = (asset) => {
    selectedAssetForMaintenance.value = asset;
    maintenanceManagementDialogVisible.value = true;
  };

  // 主界面分页处理
  const handleMainPageChange = (page, pageSize) => {
    loading.value = true;
    setTimeout(() => {
      mainPagination.value.current = page;
      mainPagination.value.pageSize = pageSize;
      loading.value = false;
    }, 100); // 短暂的loading效果
  };

  // 弹窗分页处理
  const handleDialogPageChange = (page, pageSize) => {
    loading.value = true;
    setTimeout(() => {
      dialogPagination.value.current = page;
      dialogPagination.value.pageSize = pageSize;
      loading.value = false;
    }, 100); // 短暂的loading效果
  };

  // 重置分页到第一页
  const resetPagination = () => {
    mainPagination.value.current = 1;
    dialogPagination.value.current = 1;
  };

  // 监听筛选条件变化，重置分页
  watch([assetTypeFilter, floorFilter, statusFilter, searchQuery], () => {
    resetPagination();
  });

  // 简化的页码生成算法
  const getPageNumbers = (pagination) => {
    const { current, total, pageSize } = pagination;
    const totalPages = Math.ceil(total / pageSize);
    const pages = [];

    if (totalPages <= 5) {
      // 总页数小于等于5，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 总页数大于5，简化显示
      if (current <= 3) {
        // 当前页在前面: 1 2 3 4 ... 10
        for (let i = 1; i <= 4; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (current >= totalPages - 2) {
        // 当前页在后面: 1 ... 7 8 9 10
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // 当前页在中间: 1 ... 5 6 7 ... 10
        pages.push(1);
        pages.push('...');
        for (let i = current - 1; i <= current + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };
</script>

<style scoped>
  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(21, 39, 77, 0.3);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.5);
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.7);
  }
</style>
