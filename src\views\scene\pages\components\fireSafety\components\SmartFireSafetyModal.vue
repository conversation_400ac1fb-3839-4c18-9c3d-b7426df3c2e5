<template>
  <div :class="['relative', customClass]">
    <ModalDialog v-model:visible="localVisible" title="智慧安消防管理" width="1400px" :default-fullscreen="defaultFullscreen" :class="rootClass">
      <div class="h-full flex flex-col gap-[0.8vw]">
        <!-- 功能导航 -->
        <div class="bg-[#15274D]/40 backdrop-blur-sm rounded p-[0.8vw]">
          <div class="flex gap-[1vw]">
            <button
              v-for="tab in tabs"
              :key="tab.key"
              @click="activeTab = tab.key"
              :class="[
                'px-[1.2vw] py-[0.6vw] rounded text-[0.7vw] transition-all',
                activeTab === tab.key ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-gray-300 hover:bg-black/30 hover:text-white',
              ]"
            >
              <i :class="tab.icon" class="mr-[0.4vw]"></i>
              {{ tab.label }}
            </button>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="flex-1 bg-[#15274D]/40 backdrop-blur-sm rounded p-[0.8vw] overflow-y-auto custom-scrollbar">
          <!-- 门禁等点位信息 -->
          <AccessPointInfoPanel v-if="activeTab === 'access'" />

          <!-- 人车概况 -->
          <PersonVehicleOverviewPanel v-if="activeTab === 'overview'" />

          <!-- 车辆出入记录 -->
          <VehicleAccessRecordsPanel v-if="activeTab === 'vehicle'" />

          <!-- 人员出入记录 -->
          <PersonAccessRecordsPanel v-if="activeTab === 'person'" />

          <!-- 人员出入统计 -->
          <PersonAccessStatsPanel v-if="activeTab === 'stats'" />

          <!-- 安防态势 -->
          <SecuritySituationPanel v-if="activeTab === 'security'" />
        </div>
      </div>
    </ModalDialog>
  </div>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import AccessPointInfoPanel from './smart-fire-safety/AccessPointInfoPanel.vue';
  import PersonVehicleOverviewPanel from './smart-fire-safety/PersonVehicleOverviewPanel.vue';
  import VehicleAccessRecordsPanel from './smart-fire-safety/VehicleAccessRecordsPanel.vue';
  import PersonAccessRecordsPanel from './smart-fire-safety/PersonAccessRecordsPanel.vue';
  import PersonAccessStatsPanel from './smart-fire-safety/PersonAccessStatsPanel.vue';
  import SecuritySituationPanel from './smart-fire-safety/SecuritySituationPanel.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    iconSrc: {
      type: String,
      default: '',
    },
    defaultFullscreen: {
      type: Boolean,
      default: false,
    },
    class: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['update:visible']);

  // 将props.class重命名为customClass以避免与HTML class属性冲突
  const customClass = computed(() => props.class);
  const rootClass = computed(() => props.class);

  // 控制弹窗显示
  const localVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  // 当前激活的标签页
  const activeTab = ref('access');

  // 标签页配置
  const tabs = ref([
    {
      key: 'access',
      label: '门禁等点位信息',
      icon: 'fas fa-door-open',
    },
    {
      key: 'overview',
      label: '人车概况',
      icon: 'fas fa-users',
    },
    {
      key: 'vehicle',
      label: '车辆出入记录',
      icon: 'fas fa-car',
    },
    {
      key: 'person',
      label: '人员出入记录',
      icon: 'fas fa-user-check',
    },
    {
      key: 'stats',
      label: '人员出入统计',
      icon: 'fas fa-chart-bar',
    },
    {
      key: 'security',
      label: '安防态势',
      icon: 'fas fa-shield-alt',
    },
  ]);

  // 监听弹窗打开，重置到第一个标签页
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        activeTab.value = 'access';
      }
    }
  );
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
