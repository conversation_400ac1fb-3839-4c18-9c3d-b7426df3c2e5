import * as THREE from 'three';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass';
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass';
import { FXAAShader } from 'three/examples/jsm/shaders/FXAAShader';

export class OutlineEffectManager {
  private renderer: THREE.WebGLRenderer;
  private scene: THREE.Scene;
  private camera: THREE.Camera;
  private selectedObjects: THREE.Object3D[];
  private composer!: EffectComposer;
  private outlinePass!: OutlinePass;
  private fxaaPass!: ShaderPass;

  constructor(renderer: THREE.WebGLRenderer, scene: THREE.Scene, camera: THREE.Camera) {
    this.renderer = renderer;
    this.scene = scene;
    this.camera = camera;
    this.selectedObjects = [];
    this.initPostprocessing();
  }

  private initPostprocessing(): void {
    this.composer = new EffectComposer(this.renderer);

    const renderPass = new RenderPass(this.scene, this.camera);
    this.composer.addPass(renderPass);

    const resolution = new THREE.Vector2(this.renderer.domElement.width, this.renderer.domElement.height);
    this.outlinePass = new OutlinePass(resolution, this.scene, this.camera);
    this.outlinePass.edgeStrength = 3.0;
    this.outlinePass.edgeGlow = 0.5;
    this.outlinePass.edgeThickness = 1.0;
    this.outlinePass.pulsePeriod = 2;
    this.outlinePass.visibleEdgeColor.set('#00ffff');
    this.outlinePass.hiddenEdgeColor.set('#190a05');
    this.composer.addPass(this.outlinePass);

    this.fxaaPass = new ShaderPass(FXAAShader);
    // 使用类型断言来解决可能的类型错误
    (this.fxaaPass.uniforms['resolution'].value as THREE.Vector2).set(1 / this.renderer.domElement.width, 1 / this.renderer.domElement.height);
    this.composer.addPass(this.fxaaPass);
  }

  public highlightObjects(objects: THREE.Object3D | THREE.Object3D[]): void {
    this.selectedObjects = Array.isArray(objects) ? objects : [objects];
    this.outlinePass.selectedObjects = this.selectedObjects;
  }

  public clearHighlight(): void {
    this.selectedObjects = [];
    this.outlinePass.selectedObjects = [];
  }

  public resize(width: number, height: number): void {
    this.composer.setSize(width, height);
    if (this.fxaaPass) {
      // 使用类型断言来解决可能的类型错误
      (this.fxaaPass.uniforms['resolution'].value as THREE.Vector2).set(1 / width, 1 / height);
    }
  }

  public render(): void {
    this.composer.render();
  }

  public dispose(): void {
    // 清理各个pass
    if (this.outlinePass) {
      this.outlinePass.dispose();
    }

    // ShaderPass通常不需要特殊的dispose方法
    // 只需要清理uniforms和materials

    // 清理composer - 手动清理内部资源
    if (this.composer) {
      // 清理所有passes
      const passes = (this.composer as any).passes;
      if (passes) {
        passes.forEach((pass: any) => {
          if (pass && typeof pass.dispose === 'function') {
            pass.dispose();
          }
        });
      }

      // 清理render targets
      const writeBuffer = (this.composer as any).writeBuffer;
      const readBuffer = (this.composer as any).readBuffer;
      if (writeBuffer && typeof writeBuffer.dispose === 'function') {
        writeBuffer.dispose();
      }
      if (readBuffer && typeof readBuffer.dispose === 'function') {
        readBuffer.dispose();
      }
    }
  }
}
