import * as THREE from 'three';
import { SceneManager } from '../SceneManager';
import { useGlobalThreeStore } from '../../store/globalThreeStore';
import { ModelLoaderManager } from '../../lib/load/ModelLoaderManager';
import { containsDevice } from '../../utils/deviceIdentifier';
import { throttle } from 'lodash-es';

// 定义扩展的材质接口来处理THREE.js中的材质类型
interface ExtendedMaterial extends THREE.Material {
  map?: THREE.Texture;
  color?: THREE.Color;
  emissive?: THREE.Color;
  emissiveIntensity?: number;
  wireframe?: boolean;
}

interface DeviceData {
  id: string;
  position: THREE.Vector3;
  object: THREE.Object3D;
  scale: THREE.Vector3;
  boundingBox: THREE.Box3;
  size?: number; // 设备尺寸，用于动态调整纹理分辨率
}

interface MaterialOriginalProps {
  opacity: number;
  color: THREE.Color;
  emissive: THREE.Color;
  emissiveIntensity: number;
  wireframe: boolean;
}

// 纹理缓存接口
interface TextureCacheItem {
  texture: THREE.CanvasTexture;
  temperature: number;
  lastUsed: number;
  size: number;
}

// 性能监控接口
interface PerformanceMetrics {
  fps: number;
  lastFpsUpdateTime: number;
  frameCount: number;
  updateTimes: number[];
  deviceCount: number;
  textureCount: number;
  memoryUsage: number;
  qualityLevel: 'high' | 'medium' | 'low';
}

export class ThreeDHeatmapManager {
  private static instance: ThreeDHeatmapManager | null = null;

  private sceneManager!: SceneManager;
  private scene!: THREE.Scene;
  public isActive: boolean = false;

  // 设备数据
  private deviceObjects: THREE.Object3D[] = []; // 存储设备对象
  private devicePositions: DeviceData[] = []; // 存储设备位置
  private deviceTemperatures: { [deviceId: string]: number } = {}; // 设备温度映射
  private heatmapMeshes: Map<string, THREE.Mesh> = new Map(); // 每个设备对应的热力图Mesh
  private originalMaterials: Map<THREE.Material, MaterialOriginalProps> = new Map(); // 设备原始材质

  // 用于场景透明化 - 与HeatmapManager保持一致
  private nonDeviceMeshes: THREE.Mesh[] = []; // 非设备对象
  private nonDeviceMaterials: Map<THREE.Material, MaterialOriginalProps> = new Map(); // 非设备对象的原始材质
  private wallsTransparent: boolean = false; // 是否已透明化墙体
  private animationFrameIds: Set<number> = new Set(); // 动画帧ID集合，用于清理动画
  private isTransitioning: boolean = false; // 是否正在过渡
  private updateTimer: ReturnType<typeof setInterval> | null = null;

  // 纹理缓存系统
  private textureCache: Map<string, TextureCacheItem> = new Map(); // 纹理缓存
  private maxCacheSize: number = 50; // 最大缓存数量
  private textureSizes: { high: number; medium: number; low: number } = { high: 512, medium: 256, low: 128 }; // 纹理尺寸配置

  // 批量处理
  private batchSize: number = 5; // 每批处理的设备数量(从10降到5以提高性能)
  private currentBatchIndex: number = 0; // 当前批次索引
  private processingBatch: boolean = false; // 是否正在处理批次
  private updateInterval: number = 5000; // 更新间隔(毫秒)，降低更新频率

  // 性能监控
  private performanceMetrics: PerformanceMetrics = {
    fps: 60,
    lastFpsUpdateTime: 0,
    frameCount: 0,
    updateTimes: [],
    deviceCount: 0,
    textureCount: 0,
    memoryUsage: 0,
    qualityLevel: 'high',
  };
  private fpsMonitoringEnabled: boolean = true; // 是否启用FPS监控
  private lastPerformanceCheck: number = 0; // 上次性能检查时间
  private performanceCheckInterval: number = 2000; // 性能检查间隔(毫秒)

  // 节流函数
  private throttledUpdateHeatmaps = throttle(this.updateHeatmapsBatch.bind(this), 1000);

  // 获取全局状态
  private globalThreeStore = useGlobalThreeStore();

  constructor() {
    if (ThreeDHeatmapManager.instance) {
      return ThreeDHeatmapManager.instance;
    }

    this.sceneManager = SceneManager.getInstance();
    this.scene = this.sceneManager.scene;

    ThreeDHeatmapManager.instance = this;
  }

  static getInstance(): ThreeDHeatmapManager {
    if (!ThreeDHeatmapManager.instance) {
      ThreeDHeatmapManager.instance = new ThreeDHeatmapManager();
    }
    return ThreeDHeatmapManager.instance;
  }

  // 根据温度生成颜色
  private getTemperatureColor(temp: number): string {
    // 温度范围 18-40度
    const t = Math.max(0, Math.min(1, (temp - 18) / 22));

    if (t < 0.25) {
      return `rgb(0, ${Math.floor(t * 4 * 255)}, 255)`; // 蓝色到青色
    } else if (t < 0.5) {
      return `rgb(0, 255, ${Math.floor(255 - (t - 0.25) * 4 * 255)})`; // 青色到绿色
    } else if (t < 0.75) {
      return `rgb(${Math.floor((t - 0.5) * 4 * 255)}, 255, 0)`; // 绿色到黄色
    } else {
      return `rgb(255, ${Math.floor(255 - (t - 0.75) * 4 * 255)}, 0)`; // 黄色到红色
    }
  }

  // 更新性能指标
  private updatePerformanceMetrics(): void {
    const now = performance.now();

    // 更新FPS
    this.performanceMetrics.frameCount++;
    if (now - this.performanceMetrics.lastFpsUpdateTime >= 1000) {
      this.performanceMetrics.fps = this.performanceMetrics.frameCount;
      this.performanceMetrics.frameCount = 0;
      this.performanceMetrics.lastFpsUpdateTime = now;

      // 根据FPS动态调整质量级别 - 更精细的控制
      if (this.performanceMetrics.fps < 25) {
        this.performanceMetrics.qualityLevel = 'low';
        this.batchSize = 3; // 极低FPS时进一步减小批次
      } else if (this.performanceMetrics.fps < 35) {
        this.performanceMetrics.qualityLevel = 'medium';
        this.batchSize = 5;
      } else if (this.performanceMetrics.fps < 50) {
        this.performanceMetrics.qualityLevel = 'high';
        this.batchSize = 8;
      } else {
        this.performanceMetrics.qualityLevel = 'high';
        this.batchSize = 10;
      }

      // 更新其他指标
      this.performanceMetrics.deviceCount = this.devicePositions.length;
      this.performanceMetrics.textureCount = this.textureCache.size;

      // 添加内存使用估算
      try {
        if (performance && (performance as any).memory) {
          this.performanceMetrics.memoryUsage = (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
        }
      } catch (e) {
        console.debug('无法获取内存使用情况:', e);
      }

      // 增强性能日志
      console.log(
        `热力图性能指标 - FPS: ${this.performanceMetrics.fps}, 质量: ${this.performanceMetrics.qualityLevel}, 设备数: ${this.performanceMetrics.deviceCount}, 纹理数: ${this.performanceMetrics.textureCount}, 批次大小: ${this.batchSize}, 内存: ${this.performanceMetrics.memoryUsage?.toFixed(1) || 'N/A'}MB`
      );
    }
  }

  // 获取当前纹理尺寸
  private getTextureSizeForQuality(): number {
    return this.textureSizes[this.performanceMetrics.qualityLevel];
  }

  // 清理过期纹理缓存
  private cleanupTextureCache(): void {
    if (this.textureCache.size <= this.maxCacheSize) return;

    // 按最后使用时间排序
    const sortedEntries = Array.from(this.textureCache.entries()).sort((a, b) => a[1].lastUsed - b[1].lastUsed);

    // 移除最旧的30%
    const removeCount = Math.ceil(this.textureCache.size * 0.3);
    for (let i = 0; i < removeCount; i++) {
      if (i < sortedEntries.length) {
        const [key, item] = sortedEntries[i];
        // 释放纹理资源
        item.texture.dispose();
        this.textureCache.delete(key);
      }
    }

    console.log(`清理纹理缓存: 移除了 ${removeCount} 个纹理，当前缓存大小: ${this.textureCache.size}`);
  }

  // 创建备用纹理 - 当Canvas 2D不可用时使用
  private createFallbackTexture(temperature: number): THREE.Texture {
    // 创建一个简单的纯色纹理
    const data = new Uint8Array(3);

    // 根据温度设置颜色
    const t = Math.max(0, Math.min(1, (temperature - 18) / 22));

    if (t < 0.25) {
      // 蓝色到青色
      data[0] = 0;
      data[1] = Math.floor(t * 4 * 255);
      data[2] = 255;
    } else if (t < 0.5) {
      // 青色到绿色
      data[0] = 0;
      data[1] = 255;
      data[2] = Math.floor(255 - (t - 0.25) * 4 * 255);
    } else if (t < 0.75) {
      // 绿色到黄色
      data[0] = Math.floor((t - 0.5) * 4 * 255);
      data[1] = 255;
      data[2] = 0;
    } else {
      // 黄色到红色
      data[0] = 255;
      data[1] = Math.floor(255 - (t - 0.75) * 4 * 255);
      data[2] = 0;
    }

    // 创建1x1像素的纹理
    const texture = new THREE.DataTexture(data, 1, 1, THREE.RGBFormat);
    texture.needsUpdate = true;

    return texture;
  }

  // 修改热力图纹理创建方法 - 添加缓存、动态分辨率和错误处理
  private createHeatmapTexture(temperature: number, deviceSize: number = 1): THREE.Texture {
    try {
      // 量化温度值，减少缓存变体
      const roundedTemp = Math.round(temperature * 2) / 2; // 四舍五入到0.5

      // 获取当前质量级别的纹理尺寸
      const baseSize = this.getTextureSizeForQuality();

      // 根据设备大小调整纹理尺寸，但限制在一定范围内
      const sizeMultiplier = Math.max(0.5, Math.min(1.5, deviceSize));
      const size = Math.max(64, Math.min(512, Math.round(baseSize * sizeMultiplier)));

      // 生成缓存键
      const cacheKey = `temp_${roundedTemp}_size_${size}`;

      // 检查缓存
      if (this.textureCache.has(cacheKey)) {
        const cachedItem = this.textureCache.get(cacheKey)!;
        cachedItem.lastUsed = performance.now();
        return cachedItem.texture;
      }

      // 如果缓存过大，清理
      this.cleanupTextureCache();

      try {
        // 创建新纹理
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;

        const ctx = canvas.getContext('2d');
        if (!ctx) {
          console.warn('无法获取Canvas 2D上下文，使用备用纹理');
          return this.createFallbackTexture(temperature);
        }

        try {
          ctx.clearRect(0, 0, size, size);

          // 创建径向渐变，增大渐变范围
          const gradient = ctx.createRadialGradient(size / 2, size / 2, 0, size / 2, size / 2, size / 2);

          // 获取温度颜色
          const color = this.getTemperatureColor(roundedTemp);

          // 调整渐变stops和透明度，降低整体亮度
          gradient.addColorStop(0, color.replace(')', ', 0.6)').replace('rgb', 'rgba')); // 中心点降低透明度
          gradient.addColorStop(0.4, color.replace(')', ', 0.3)').replace('rgb', 'rgba')); // 中间区域降低透明度
          gradient.addColorStop(0.8, 'rgba(0, 0, 0, 0)'); // 提前开始完全透明

          ctx.fillStyle = gradient;
          ctx.fillRect(0, 0, size, size);

          const texture = new THREE.CanvasTexture(canvas);
          texture.needsUpdate = true;

          // 优化纹理设置，减少GPU内存使用
          texture.minFilter = THREE.LinearFilter;
          texture.magFilter = THREE.LinearFilter;
          texture.generateMipmaps = false;

          // 添加到缓存
          this.textureCache.set(cacheKey, {
            texture,
            temperature: roundedTemp,
            lastUsed: performance.now(),
            size,
          });

          return texture;
        } catch (gradientError) {
          console.warn('创建渐变时出错，使用备用方法:', gradientError);

          // 渐变创建失败，尝试使用纯色填充
          try {
            ctx.fillStyle = this.getTemperatureColor(roundedTemp);
            ctx.fillRect(0, 0, size, size);

            const texture = new THREE.CanvasTexture(canvas);
            texture.needsUpdate = true;
            texture.minFilter = THREE.LinearFilter;
            texture.magFilter = THREE.LinearFilter;
            texture.generateMipmaps = false;

            return texture;
          } catch (fillError) {
            console.warn('纯色填充也失败，使用备用纹理:', fillError);
            return this.createFallbackTexture(temperature);
          }
        }
      } catch (canvasError) {
        console.warn('Canvas操作失败，使用备用纹理:', canvasError);
        return this.createFallbackTexture(temperature);
      }
    } catch (error) {
      console.error('创建热力图纹理时发生错误:', error);
      // 出错时返回备用纹理
      return this.createFallbackTexture(temperature);
    }
  }

  // 创建热力图材质
  private createHeatmapMaterial(): THREE.ShaderMaterial {
    return new THREE.ShaderMaterial({
      transparent: true,
      uniforms: {
        opacity: { value: 0.1 }, // 降低整体不透明度
        intensity: { value: 0.1 }, // 降低光照强度
        colorHot: { value: new THREE.Color(0xff3300).multiplyScalar(0.8) }, // 降低高温颜色亮度
        colorCold: { value: new THREE.Color(0x0033ff).multiplyScalar(0.7) }, // 降低低温颜色亮度
      },
    });
  }

  // 收集当前楼层内的设备 - 优化版本
  private async collectDevices(): Promise<void> {
    try {
      // 重置设备数据
      this.deviceObjects = [];
      this.devicePositions = [];

      // 记录开始时间，用于性能监控
      const startTime = performance.now();

      const modelLoader = ModelLoaderManager.getInstance();
      const models = modelLoader.getCurrentModels();

      // 限制最大设备数量，防止性能问题
      const maxDevices = 100;
      let deviceCount = 0;

      // 使用Set避免重复处理同一设备
      const processedIds = new Set<string>();

      for (const model of models) {
        if (model.userData.type !== 'floor' || deviceCount >= maxDevices) continue;

        // 使用队列进行广度优先遍历，避免递归导致的栈溢出
        const queue: THREE.Object3D[] = [model];

        while (queue.length > 0 && deviceCount < maxDevices) {
          const object = queue.shift()!;

          // 跳过已处理的对象
          if (processedIds.has(object.uuid)) continue;
          processedIds.add(object.uuid); // 检查是否为设备 - 使用动画循环优化版本
          if (object instanceof THREE.Mesh && containsDevice(object, true)) {
            this.deviceObjects.push(object);
            deviceCount++;

            const position = new THREE.Vector3();
            object.getWorldPosition(position);

            // 获取对象的缩放
            const scale = new THREE.Vector3();
            object.getWorldScale(scale);

            // 计算设备尺寸，用于后续优化
            const boundingBox = new THREE.Box3().setFromObject(object);
            const size = new THREE.Vector3();
            boundingBox.getSize(size);
            const deviceSize = Math.max(size.x, size.z);

            this.devicePositions.push({
              id: object.name,
              position,
              boundingBox,
              object,
              scale,
              size: deviceSize,
            });

            // 为每个设备生成随机温度(28-40°C)
            this.deviceTemperatures[object.name] = 28 + Math.random() * 12;
          }

          // 将子对象添加到队列
          if (object.children && object.children.length > 0) {
            for (const child of object.children) {
              queue.push(child);
            }
          }
        }
      }

      // 记录性能数据
      const endTime = performance.now();
      const duration = endTime - startTime;

      console.log(`找到 ${this.deviceObjects.length} 个设备，耗时 ${duration.toFixed(2)}ms`);

      // 如果设备数量过多，发出警告
      if (this.deviceObjects.length > 50) {
        console.warn(`设备数量(${this.deviceObjects.length})较多，可能影响性能，已限制为最多${maxDevices}个`);
      }

      // 更新性能指标
      this.performanceMetrics.deviceCount = this.deviceObjects.length;
    } catch (error) {
      console.error('收集设备数据失败:', error);
    }
  }

  // 批量创建热力图 - 分批处理，避免一次性创建过多对象导致卡顿
  private createHeatmapsForDevices(): void {
    // 清理现有热力图
    this.clearHeatmaps();

    // 重置批处理状态
    this.currentBatchIndex = 0;
    this.processingBatch = false;

    // 如果没有设备，直接返回
    if (this.devicePositions.length === 0) {
      console.warn('没有找到设备，无法创建热力图');
      return;
    }

    // 开始批量处理
    this.processNextBatch();
  }

  // 处理下一批设备
  private processNextBatch(): void {
    if (this.processingBatch || !this.isActive) return;

    this.processingBatch = true;
    const startTime = performance.now();

    // 计算当前批次的起始和结束索引
    const startIndex = this.currentBatchIndex;
    const endIndex = Math.min(startIndex + this.batchSize, this.devicePositions.length);

    // 处理当前批次的设备
    for (let i = startIndex; i < endIndex; i++) {
      this.createHeatmapForDevice(this.devicePositions[i]);
    }

    // 更新批次索引
    this.currentBatchIndex = endIndex;

    // 记录处理时间
    const endTime = performance.now();
    const batchTime = endTime - startTime;

    // 更新性能指标
    this.performanceMetrics.updateTimes.push(batchTime);
    if (this.performanceMetrics.updateTimes.length > 10) {
      this.performanceMetrics.updateTimes.shift();
    }

    // 动态调整批次大小
    this.adjustBatchSize(batchTime);

    console.log(`处理热力图批次 ${startIndex}-${endIndex - 1}/${this.devicePositions.length}，耗时 ${batchTime.toFixed(2)}ms`);

    // 检查是否还有更多批次需要处理
    if (this.currentBatchIndex < this.devicePositions.length) {
      // 使用requestAnimationFrame安排下一批处理，给主线程一些喘息空间
      const frameId = requestAnimationFrame(() => {
        this.processingBatch = false;
        this.processNextBatch();
      });
      this.animationFrameIds.add(frameId);
    } else {
      // 所有批次处理完成
      this.processingBatch = false;
      console.log(`所有热力图创建完成，共 ${this.devicePositions.length} 个设备`);
    }
  }

  // 动态调整批次大小
  private adjustBatchSize(lastBatchTime: number): void {
    // 如果处理时间过长，减小批次大小
    if (lastBatchTime > 50) {
      this.batchSize = Math.max(1, Math.floor(this.batchSize * 0.8));
    }
    // 如果处理时间很短，增加批次大小
    else if (lastBatchTime < 16 && this.batchSize < 20) {
      this.batchSize = Math.min(20, this.batchSize + 1);
    }
  }

  // 为单个设备创建热力图 - 增强版
  private createHeatmapForDevice(deviceData: DeviceData): void {
    try {
      const { id, position, boundingBox, size = 1 } = deviceData;
      const temperature = this.deviceTemperatures[id] || 30;

      // 根据设备尺寸调整热力图大小，但不要过大
      const sizeVector = new THREE.Vector3();
      boundingBox.getSize(sizeVector);
      const heatmapSize = Math.max(sizeVector.x, sizeVector.z) * 4; // 减小倍数，从6倍降低到4倍

      // 使用共享几何体，减少内存占用
      const geometry = new THREE.PlaneGeometry(heatmapSize, heatmapSize);
      geometry.rotateX(-Math.PI / 2);

      try {
        // 使用优化后的纹理创建方法，传入设备尺寸
        const texture = this.createHeatmapTexture(temperature, size);

        // 根据纹理类型选择合适的材质设置
        let material: THREE.Material;

        if (texture instanceof THREE.CanvasTexture) {
          // 标准热力图材质
          material = new THREE.MeshBasicMaterial({
            map: texture,
            transparent: true,
            opacity: 0.75, // 略微降低不透明度，减少视觉干扰
            depthWrite: false,
            side: THREE.DoubleSide,
            blending: THREE.AdditiveBlending,
          });
        } else if (texture instanceof THREE.DataTexture) {
          // 备用纹理使用不同的材质设置
          material = new THREE.MeshBasicMaterial({
            map: texture,
            transparent: true,
            opacity: 0.6, // 更低的不透明度
            depthWrite: false,
            side: THREE.DoubleSide,
            blending: THREE.NormalBlending, // 使用普通混合模式
          });
        } else {
          // 其他类型的纹理
          material = new THREE.MeshBasicMaterial({
            map: texture,
            transparent: true,
            opacity: 0.7,
            depthWrite: false,
            side: THREE.DoubleSide,
          });
        }

        const heatmapMesh = new THREE.Mesh(geometry, material);

        // 稍微提高热力图位置，确保在地面上方显示
        const bottomY = boundingBox.min.y;
        heatmapMesh.position.set(position.x, bottomY + 0.05, position.z);

        // 优化渲染性能
        heatmapMesh.matrixAutoUpdate = false; // 禁用自动矩阵更新
        heatmapMesh.updateMatrix(); // 手动更新一次矩阵

        // 添加到场景
        this.heatmapMeshes.set(id, heatmapMesh);
        this.scene.add(heatmapMesh);
      } catch (textureError) {
        console.warn(`为设备 ${id} 创建纹理失败，尝试使用纯色热力图:`, textureError);

        // 纹理创建失败，使用纯色材质作为备用
        try {
          // 根据温度生成颜色
          const t = Math.max(0, Math.min(1, (temperature - 18) / 22));
          const color = new THREE.Color();

          if (t < 0.25) {
            color.setRGB(0, t * 4, 1); // 蓝色到青色
          } else if (t < 0.5) {
            color.setRGB(0, 1, 1 - (t - 0.25) * 4); // 青色到绿色
          } else if (t < 0.75) {
            color.setRGB((t - 0.5) * 4, 1, 0); // 绿色到黄色
          } else {
            color.setRGB(1, 1 - (t - 0.75) * 4, 0); // 黄色到红色
          }

          // 创建纯色材质
          const material = new THREE.MeshBasicMaterial({
            color: color,
            transparent: true,
            opacity: 0.5,
            depthWrite: false,
            side: THREE.DoubleSide,
          });

          const heatmapMesh = new THREE.Mesh(geometry, material);

          // 设置位置
          const bottomY = boundingBox.min.y;
          heatmapMesh.position.set(position.x, bottomY + 0.05, position.z);

          // 优化渲染性能
          heatmapMesh.matrixAutoUpdate = false;
          heatmapMesh.updateMatrix();

          // 添加到场景
          this.heatmapMeshes.set(id, heatmapMesh);
          this.scene.add(heatmapMesh);
        } catch (fallbackError) {
          console.error(`为设备 ${id} 创建备用热力图也失败:`, fallbackError);
        }
      }
    } catch (error) {
      console.error(`为设备 ${deviceData.id} 创建热力图时出错:`, error);
    }
  }

  // 批量更新热力图 - 替代原来的updateHeatmaps方法
  private updateHeatmapsBatch(): void {
    if (!this.isActive || this.processingBatch) return;

    // 检查性能，如果FPS过低，跳过本次更新
    if (this.performanceMetrics.fps < 20) {
      console.warn('FPS过低，跳过热力图更新以保持性能');
      return;
    }

    // 更新性能指标
    this.updatePerformanceMetrics();

    // 开始批量更新
    this.processingBatch = true;
    this.currentBatchIndex = 0;

    // 使用requestAnimationFrame启动批量更新
    const frameId = requestAnimationFrame(() => {
      this.processingBatch = false;
      this.processUpdateBatch();
    });
    this.animationFrameIds.add(frameId);
  }

  // 处理热力图更新批次
  private processUpdateBatch(): void {
    if (!this.isActive || this.processingBatch) return;

    this.processingBatch = true;
    const startTime = performance.now();

    // 计算当前批次的起始和结束索引
    const startIndex = this.currentBatchIndex;
    const endIndex = Math.min(startIndex + this.batchSize, this.devicePositions.length);

    // 处理当前批次的设备
    for (let i = startIndex; i < endIndex; i++) {
      this.updateHeatmapForDevice(this.devicePositions[i]);
    }

    // 更新批次索引
    this.currentBatchIndex = endIndex;

    // 记录处理时间
    const endTime = performance.now();
    const _batchTime = endTime - startTime; // 记录但不使用

    // 检查是否还有更多批次需要处理
    if (this.currentBatchIndex < this.devicePositions.length) {
      // 使用requestAnimationFrame安排下一批处理
      const frameId = requestAnimationFrame(() => {
        this.processingBatch = false;
        this.processUpdateBatch();
      });
      this.animationFrameIds.add(frameId);
    } else {
      // 所有批次处理完成
      this.processingBatch = false;
    }
  }

  // 更新单个设备的热力图
  private updateHeatmapForDevice(deviceData: DeviceData): void {
    try {
      const { id, size = 1 } = deviceData;
      const heatmapMesh = this.heatmapMeshes.get(id);

      if (!heatmapMesh) return;

      // 更新温度 - 随机波动，但幅度减小
      let temperature = this.deviceTemperatures[id] || 30;
      temperature += (Math.random() - 0.5) * 1; // 随机波动±0.5度，减小波动幅度
      temperature = Math.max(18, Math.min(40, temperature));
      this.deviceTemperatures[id] = temperature;

      // 使用优化后的纹理创建方法，传入设备尺寸
      const texture = this.createHeatmapTexture(temperature, size);

      // 清理旧纹理（如果不是来自缓存）
      const material = heatmapMesh.material as ExtendedMaterial;
      if (material && material.map && material.map !== texture) {
        // 检查旧纹理是否在缓存中，如果不在才释放
        let isInCache = false;
        for (const cacheItem of this.textureCache.values()) {
          if (cacheItem.texture === material.map) {
            isInCache = true;
            break;
          }
        }

        if (!isInCache) {
          material.map.dispose();
        }

        // 应用新纹理
        material.map = texture;
        material.needsUpdate = true;
      }
    } catch (error) {
      console.error(`更新设备 ${deviceData.id} 热力图时出错:`, error);
    }
  }

  // 清除热力图 - 优化版本
  private clearHeatmaps(): void {
    // 移除所有热力图网格
    this.heatmapMeshes.forEach((mesh) => {
      if (mesh) {
        // 从场景中移除
        this.scene.remove(mesh);

        // 清理材质和纹理
        if (mesh.material) {
          const material = mesh.material as ExtendedMaterial;
          // 注意：不要在这里直接释放纹理，因为可能在缓存中被其他对象使用
          material.dispose();
        }

        // 清理几何体
        if (mesh.geometry) {
          mesh.geometry.dispose();
        }
      }
    });

    // 清空映射
    this.heatmapMeshes.clear();

    // 如果不再需要热力图，清理纹理缓存
    if (!this.isActive) {
      this.textureCache.forEach((item) => {
        item.texture.dispose();
      });
      this.textureCache.clear();
      console.log('已清理所有热力图纹理缓存');
    }
  }

  // 开始定时更新 - 优化版本
  private startUpdating(): void {
    this.stopUpdating();

    // 使用更长的更新间隔，减少资源消耗
    this.updateTimer = setInterval(() => {
      // 使用节流函数进行更新，避免频繁更新
      this.throttledUpdateHeatmaps();
    }, this.updateInterval);

    // 启动FPS监控
    if (this.fpsMonitoringEnabled) {
      this.startFpsMonitoring();
    }
  }

  // 启动FPS监控
  private startFpsMonitoring(): void {
    // 使用requestAnimationFrame监控FPS
    const monitorFps = () => {
      this.updatePerformanceMetrics();

      // 检查性能，如果FPS持续过低，自动降低质量
      const now = performance.now();
      if (now - this.lastPerformanceCheck > this.performanceCheckInterval) {
        this.lastPerformanceCheck = now;

        // 如果FPS低于阈值，降低质量
        if (this.performanceMetrics.fps < 25 && this.performanceMetrics.qualityLevel !== 'low') {
          console.warn(`FPS过低(${this.performanceMetrics.fps})，自动降低热力图质量`);
          this.performanceMetrics.qualityLevel = 'low';
        }
      }

      // 继续监控
      if (this.isActive && this.fpsMonitoringEnabled) {
        const frameId = requestAnimationFrame(monitorFps);
        this.animationFrameIds.add(frameId);
      }
    };

    // 启动监控
    const frameId = requestAnimationFrame(monitorFps);
    this.animationFrameIds.add(frameId);
  }

  // 停止更新
  private stopUpdating(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }

    // 清理所有动画帧ID
    this.animationFrameIds.forEach((id) => {
      cancelAnimationFrame(id);
    });
    this.animationFrameIds.clear();
  }

  // 检查系统是否支持热力图功能 - 改进版
  private checkSystemSupport(): boolean {
    try {
      // 检查WebGL支持 - 使用try-catch避免可能的异常
      let hasWebGL = false;
      try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        hasWebGL = !!gl;

        // 释放WebGL上下文资源
        if (gl && gl instanceof WebGLRenderingContext) {
          const loseContext = gl.getExtension('WEBGL_lose_context');
          if (loseContext) loseContext.loseContext();
        }
      } catch (e) {
        console.warn('检查WebGL支持时出错:', e);
      }

      if (!hasWebGL) {
        console.warn('浏览器可能不支持WebGL，但热力图功能将尝试继续运行');
        // 不返回false，让功能尝试运行
      }

      // 检查Canvas支持 - 使用更可靠的检测方法
      let hasCanvas2D = false;
      try {
        // 检查Canvas元素是否存在
        if (typeof document !== 'undefined' && typeof document.createElement === 'function') {
          const canvas = document.createElement('canvas');
          // 检查是否有getContext方法
          hasCanvas2D = typeof canvas.getContext === 'function';

          // 尝试获取2D上下文，但不将结果作为唯一判断依据
          if (hasCanvas2D) {
            const _ctx = canvas.getContext('2d'); // 记录但不使用
            // 即使ctx为null，也不一定意味着不支持Canvas 2D
            // 某些浏览器环境或扩展可能会影响getContext的结果
          }
        }
      } catch (e) {
        console.warn('检查Canvas 2D支持时出错:', e);
      }

      // 即使Canvas 2D检测失败，也允许功能继续
      if (!hasCanvas2D) {
        console.warn('浏览器可能不完全支持Canvas 2D，热力图可能无法正常显示');
        // 不返回false，让功能尝试运行
      }

      // 检查内存使用情况 - 只作为警告，不阻止功能
      try {
        if (performance && (performance as any).memory) {
          const memory = (performance as any).memory;
          if (memory && memory.jsHeapSizeLimit && memory.usedJSHeapSize) {
            const usedRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
            if (usedRatio > 0.8) {
              console.warn(`内存使用率较高(${(usedRatio * 100).toFixed(1)}%)，热力图可能导致性能下降`);
              // 不返回false，只是警告
            }
          }
        }
      } catch (e) {
        console.warn('检查内存使用情况时出错:', e);
      }

      // 默认允许功能运行，让实际渲染过程决定是否能成功
      return true;
    } catch (error) {
      console.error('检查系统支持时出错:', error);
      // 出现未知错误时，仍然允许尝试运行
      return true;
    }
  }

  // 优化：显示热力图前进行系统检查，并使用批量处理 - 改进版
  public async show(): Promise<void> {
    try {
      if (this.isActive) return;

      // 检查系统支持 - 只作为警告，不阻止功能
      this.checkSystemSupport();

      // 设置初始状态
      this.isActive = true;
      this.performanceMetrics.qualityLevel = 'medium'; // 默认使用中等质量

      // 显示加载提示
      console.log('正在加载热力图功能...');

      try {
        // 收集设备
        await this.collectDevices();

        if (this.devicePositions.length === 0) {
          console.warn('未找到可应用热力图的设备');
          this.isActive = false;
          return;
        }

        // 如果设备数量过多，自动降低质量
        if (this.devicePositions.length > 50) {
          console.warn(`设备数量较多(${this.devicePositions.length})，自动降低热力图质量以保持性能`);
          this.performanceMetrics.qualityLevel = 'low';
        }

        // 创建热力图，使用批量处理
        this.createHeatmapsForDevices();

        // 开始定时更新
        this.startUpdating();

        console.log(`设备热力图已显示，质量级别: ${this.performanceMetrics.qualityLevel}, 设备数量: ${this.devicePositions.length}`);
      } catch (innerError) {
        // 内部操作失败时，尝试回退到低质量模式
        console.warn('热力图创建过程中出错，尝试使用低质量模式:', innerError);

        // 清理已创建的资源
        this.clearHeatmaps();

        // 尝试使用低质量模式
        this.performanceMetrics.qualityLevel = 'low';

        try {
          // 重新收集设备，但限制数量
          await this.collectDevices();

          if (this.devicePositions.length === 0) {
            throw new Error('未找到可应用热力图的设备');
          }

          // 使用更小的批次大小
          this.batchSize = 5;

          // 重新创建热力图
          this.createHeatmapsForDevices();

          // 开始定时更新，但使用更长的间隔
          this.updateInterval = 8000;
          this.startUpdating();

          console.log(`热力图已以低质量模式显示，设备数量: ${this.devicePositions.length}`);
        } catch (fallbackError) {
          // 如果回退也失败，则放弃
          console.error('低质量模式也失败，无法显示热力图:', fallbackError);
          throw fallbackError;
        }
      }
    } catch (error) {
      // 出错时恢复状态
      this.isActive = false;
      this.clearHeatmaps();
      this.stopUpdating();

      console.error('显示热力图时发生错误:', error);
      throw error;
    }
  }

  // 优化：隐藏热力图时确保所有资源都被正确释放
  public async hide(): Promise<void> {
    if (!this.isActive) return;

    console.log('正在关闭热力图功能...');

    // 停止更新
    this.stopUpdating();

    // 清除所有热力图
    this.clearHeatmaps();

    // 重置状态
    this.isActive = false;
    this.processingBatch = false;
    this.currentBatchIndex = 0;

    // 清理纹理缓存
    this.textureCache.forEach((item) => {
      item.texture.dispose();
    });
    this.textureCache.clear();

    console.log('热力图已隐藏，所有资源已释放');
  }

  // 增强dispose方法，确保所有资源都被正确释放
  public dispose(): void {
    // 先隐藏热力图
    if (this.isActive) {
      this.hide();
    }

    // 清理所有数据
    this.deviceObjects = [];
    this.devicePositions = [];
    this.deviceTemperatures = {};

    // 清理所有材质和动画
    this.originalMaterials.clear();
    this.nonDeviceMaterials.clear();
    this.nonDeviceMeshes = [];

    // 确保所有动画帧都被取消
    this.animationFrameIds.forEach((id) => {
      cancelAnimationFrame(id);
    });
    this.animationFrameIds.clear();

    // 确保没有过渡效果在进行中
    this.isTransitioning = false;

    // 清理纹理缓存
    this.textureCache.forEach((item) => {
      item.texture.dispose();
    });
    this.textureCache.clear();

    // 重置性能指标
    this.performanceMetrics = {
      fps: 60,
      lastFpsUpdateTime: 0,
      frameCount: 0,
      updateTimes: [],
      deviceCount: 0,
      textureCount: 0,
      memoryUsage: 0,
      qualityLevel: 'high',
    };

    // 重置单例实例
    ThreeDHeatmapManager.instance = null;

    console.log('热力图管理器已完全释放');
  }
}
