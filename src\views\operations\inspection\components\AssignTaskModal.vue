<template>
  <a-modal v-model:visible="visible" title="分配任务" width="400px" @ok="handleSubmit" @cancel="handleCancel">
    <div v-if="task" class="mb-4 p-3 bg-gray-50 rounded">
      <h4>任务信息</h4>
      <p>任务编号: {{ task.taskNumber }}</p>
      <p>任务标题: {{ task.title }}</p>
      <p>当前执行人: {{ task.assignee || '未分配' }}</p>
    </div>

    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <a-form-item label="新执行人" name="assigneeId">
        <a-select v-model:value="formData.assigneeId" placeholder="选择执行人">
          <a-select-option value="1">张三</a-select-option>
          <a-select-option value="2">李四</a-select-option>
          <a-select-option value="3">王五</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { assignInspectionTask, type InspectionTask } from '/@/api/operations/inspection';

  interface Props {
    visible: boolean;
    task: InspectionTask | null;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{ 'update:visible': [value: boolean]; success: [] }>();

  const formRef = ref();
  const formData = reactive({
    assigneeId: null as number | null,
  });

  const rules = {
    assigneeId: [{ required: true, message: '请选择执行人', trigger: 'change' }],
  };

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const handleSubmit = async () => {
    try {
      await formRef.value.validate();
      if (!props.task) return;

      await assignInspectionTask(props.task.id, formData.assigneeId!);
      message.success('任务分配成功');
      emit('success');
    } catch (error) {
      message.error('分配失败');
    }
  };

  const handleCancel = () => {
    visible.value = false;
  };
</script>
