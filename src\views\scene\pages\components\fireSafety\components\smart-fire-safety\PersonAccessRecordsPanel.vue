<template>
  <div class="min-h-full flex flex-col gap-[0.8vw]">
    <!-- 人员出入统计 -->
    <div class="bg-black/20 rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
        <i class="fas fa-user-check mr-[0.4vw] text-blue-400"></i>
        人员出入统计
      </div>
      <div class="grid grid-cols-5 gap-[0.6vw]">
        <div v-for="stat in personStats" :key="stat.label" class="bg-[#15274D]/30 p-[0.6vw] rounded">
          <div class="text-[1vw] font-medium" :class="stat.valueClass">{{ stat.value }}</div>
          <div class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ stat.label }}</div>
          <div v-if="stat.trend" class="text-[0.5vw] mt-[0.2vw]" :class="stat.trendClass">
            {{ stat.trend }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex gap-[0.8vw]">
      <!-- 左侧：人员出入记录 -->
      <div class="flex-1 bg-black/20 rounded p-[0.8vw] flex flex-col">
        <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-list mr-[0.4vw] text-blue-400"></i>
            人员出入记录
          </div>
          <div class="flex gap-[0.4vw]">
            <button
              v-for="filter in recordFilters"
              :key="filter.key"
              @click="activeRecordFilter = filter.key"
              :class="[
                'px-[0.6vw] py-[0.2vw] rounded text-[0.6vw] transition-all',
                activeRecordFilter === filter.key ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-gray-300 hover:bg-black/30',
              ]"
            >
              {{ filter.label }}
            </button>
          </div>
        </div>

        <div class="flex-1 overflow-y-auto custom-scrollbar">
          <div class="space-y-[0.4vw]">
            <div v-for="record in filteredRecords" :key="record.id" class="bg-[#15274D]/30 p-[0.6vw] rounded hover:bg-[#15274D]/50 transition-all">
              <div class="flex items-center justify-between mb-[0.3vw]">
                <div class="flex items-center">
                  <div class="w-[2vw] h-[2vw] rounded-full bg-blue-400/20 flex items-center justify-center mr-[0.6vw]">
                    <i class="fas fa-user text-blue-400"></i>
                  </div>
                  <div>
                    <div class="text-[0.65vw] text-white font-medium">{{ record.name }}</div>
                    <div class="text-[0.6vw] text-gray-400">{{ record.department }}</div>
                  </div>
                </div>
                <span class="text-[0.6vw]" :class="getRecordTypeClass(record.type)">
                  {{ record.type === 'in' ? '进入' : '离开' }}
                </span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400 mb-[0.3vw]">
                <span>工号：{{ record.employeeId }}</span>
                <span>时间：{{ record.time }}</span>
              </div>
              <div class="flex justify-between text-[0.6vw] text-gray-400">
                <span>门禁：{{ record.accessPoint }}</span>
                <span>方式：{{ record.method }}</span>
              </div>
              <div v-if="record.duration" class="text-[0.6vw] text-gray-400 mt-[0.2vw]"> 在场时长：{{ record.duration }} </div>
              <div v-if="record.temperature" class="text-[0.6vw] text-green-400 mt-[0.2vw]"> 体温：{{ record.temperature }}°C </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：人员分类和统计 -->
      <div class="w-[40%] flex flex-col gap-[0.8vw]">
        <!-- 人员类型统计 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-users mr-[0.4vw] text-blue-400"></i>
            人员类型统计
          </div>

          <div class="space-y-[0.4vw]">
            <div v-for="type in personTypes" :key="type.name" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ type.name }}</span>
                <span class="text-[0.6vw] text-blue-400">{{ type.count }}人</span>
              </div>
              <div class="flex justify-between text-[0.5vw] text-gray-400 mb-[0.2vw]">
                <span>占比：{{ type.percentage }}%</span>
                <span>今日：{{ type.todayCount }}人</span>
              </div>
              <div class="w-full bg-black/30 rounded-full h-[0.3vw]">
                <div class="bg-blue-400 h-[0.3vw] rounded-full transition-all" :style="{ width: type.percentage + '%' }"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 部门出入统计 -->
        <div class="bg-black/20 rounded p-[0.8vw] flex-1">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-building mr-[0.4vw] text-blue-400"></i>
            部门出入统计
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="dept in departmentStats" :key="dept.name" class="flex justify-between items-center">
              <span class="text-[0.6vw] text-gray-400">{{ dept.name }}</span>
              <div class="flex items-center gap-[0.4vw]">
                <span class="text-[0.6vw] text-white">{{ dept.count }}人</span>
                <div class="w-[3vw] bg-black/30 rounded-full h-[0.3vw]">
                  <div class="bg-green-400 h-[0.3vw] rounded-full" :style="{ width: dept.percentage + '%' }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 考勤统计 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-clock mr-[0.4vw] text-blue-400"></i>
            考勤统计
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="attendance in attendanceStats" :key="attendance.label" class="flex justify-between items-center">
              <span class="text-[0.6vw] text-gray-400">{{ attendance.label }}</span>
              <span class="text-[0.6vw] text-white font-medium">{{ attendance.value }}</span>
            </div>
          </div>
        </div>

        <!-- 健康监测 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-heartbeat mr-[0.4vw] text-blue-400"></i>
            健康监测
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="health in healthStats" :key="health.label" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ health.label }}</span>
                <span class="text-[0.6vw]" :class="getHealthStatusClass(health.status)">{{ health.value }}</span>
              </div>
              <div class="text-[0.5vw] text-gray-400">{{ health.description }}</div>
            </div>
          </div>
        </div>

        <!-- 异常记录 -->
        <div class="bg-black/20 rounded p-[0.8vw]">
          <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center">
            <i class="fas fa-exclamation-triangle mr-[0.4vw] text-blue-400"></i>
            异常记录
          </div>

          <div class="space-y-[0.3vw]">
            <div v-for="anomaly in anomalies" :key="anomaly.id" class="bg-[#15274D]/30 p-[0.4vw] rounded">
              <div class="flex justify-between items-center mb-[0.2vw]">
                <span class="text-[0.6vw] text-white">{{ anomaly.time }}</span>
                <span class="text-[0.5vw]" :class="getAnomalyLevelClass(anomaly.level)">{{ anomaly.level }}</span>
              </div>
              <div class="text-[0.5vw] text-gray-400">{{ anomaly.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  // 人员出入统计数据
  const personStats = ref([
    {
      label: '今日进入',
      value: '234',
      valueClass: 'text-green-400',
      trend: '↑ 18',
      trendClass: 'text-green-400',
    },
    {
      label: '今日离开',
      value: '198',
      valueClass: 'text-orange-400',
      trend: '↑ 15',
      trendClass: 'text-green-400',
    },
    {
      label: '当前在场',
      value: '156',
      valueClass: 'text-blue-400',
      trend: '↑ 12',
      trendClass: 'text-green-400',
    },
    {
      label: '出勤率',
      value: '98.5%',
      valueClass: 'text-green-400',
      trend: '↑ 1.2%',
      trendClass: 'text-green-400',
    },
    {
      label: '异常次数',
      value: '0',
      valueClass: 'text-green-400',
      trend: '→ 0',
      trendClass: 'text-green-400',
    },
  ]);

  // 记录筛选器
  const recordFilters = ref([
    { key: 'all', label: '全部' },
    { key: 'in', label: '进入' },
    { key: 'out', label: '离开' },
    { key: 'today', label: '今日' },
  ]);

  const activeRecordFilter = ref('all');

  // 人员出入记录
  const personRecords = ref([
    {
      id: 1,
      name: '张工程师',
      department: '技术部',
      employeeId: 'T001',
      type: 'in',
      time: '2024-02-28 08:30',
      accessPoint: '主入口',
      method: '刷卡',
      temperature: 36.5,
    },
    {
      id: 2,
      name: '李经理',
      department: '管理部',
      employeeId: 'M001',
      type: 'out',
      time: '2024-02-28 17:45',
      accessPoint: '主入口',
      method: '人脸识别',
      duration: '9小时15分钟',
    },
    {
      id: 3,
      name: '王技师',
      department: '运维部',
      employeeId: 'O001',
      type: 'in',
      time: '2024-02-28 08:45',
      accessPoint: '员工通道',
      method: '指纹',
      temperature: 36.3,
    },
    {
      id: 4,
      name: '访客-陈先生',
      department: '外部访客',
      employeeId: 'V001',
      type: 'out',
      time: '2024-02-28 16:30',
      accessPoint: '访客通道',
      method: '临时卡',
      duration: '2小时30分钟',
    },
    {
      id: 5,
      name: '赵主管',
      department: '安全部',
      employeeId: 'S001',
      type: 'in',
      time: '2024-02-28 09:00',
      accessPoint: '主入口',
      method: '人脸识别',
      temperature: 36.4,
    },
  ]);

  // 筛选后的记录
  const filteredRecords = computed(() => {
    if (activeRecordFilter.value === 'all') {
      return personRecords.value;
    }
    if (activeRecordFilter.value === 'today') {
      return personRecords.value.filter((record) => record.time.includes('2024-02-28'));
    }
    return personRecords.value.filter((record) => record.type === activeRecordFilter.value);
  });

  // 人员类型统计
  const personTypes = ref([
    { name: '正式员工', count: 145, percentage: 75, todayCount: 189 },
    { name: '临时员工', count: 28, percentage: 15, todayCount: 25 },
    { name: '外部访客', count: 15, percentage: 8, todayCount: 18 },
    { name: '其他人员', count: 4, percentage: 2, todayCount: 2 },
  ]);

  // 部门出入统计
  const departmentStats = ref([
    { name: '技术部', count: 45, percentage: 35 },
    { name: '管理部', count: 32, percentage: 25 },
    { name: '运维部', count: 28, percentage: 22 },
    { name: '安全部', count: 18, percentage: 14 },
    { name: '其他部门', count: 12, percentage: 9 },
  ]);

  // 考勤统计
  const attendanceStats = ref([
    { label: '正常出勤', value: '189人' },
    { label: '迟到人数', value: '0人' },
    { label: '早退人数', value: '0人' },
    { label: '请假人数', value: '3人' },
    { label: '出差人数', value: '8人' },
    { label: '出勤率', value: '98.5%' },
  ]);

  // 健康监测
  const healthStats = ref([
    {
      label: '体温检测',
      value: '正常',
      status: 'normal',
      description: '所有人员体温正常，范围36.0-37.2°C',
    },
    {
      label: '健康码状态',
      value: '绿码',
      status: 'normal',
      description: '所有人员健康码均为绿码',
    },
    {
      label: '疫苗接种',
      value: '100%',
      status: 'normal',
      description: '全员已完成疫苗接种',
    },
  ]);

  // 异常记录
  const anomalies = ref([
    { id: 1, time: '2024-02-28 14:30', level: '正常', description: '所有人员进出正常，无异常情况' },
    { id: 2, time: '2024-02-28 10:15', level: '正常', description: '健康检测正常，体温均在正常范围' },
    { id: 3, time: '2024-02-28 08:45', level: '正常', description: '考勤系统运行正常' },
  ]);

  // 获取记录类型样式
  const getRecordTypeClass = (type) => {
    return type === 'in' ? 'text-green-400' : 'text-orange-400';
  };

  // 获取健康状态样式
  const getHealthStatusClass = (status) => {
    switch (status) {
      case 'normal':
        return 'text-green-400';
      case 'warning':
        return 'text-yellow-400';
      case 'danger':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // 获取异常等级样式
  const getAnomalyLevelClass = (level) => {
    switch (level) {
      case '正常':
        return 'text-green-400';
      case '警告':
        return 'text-yellow-400';
      case '严重':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 142, 230, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 142, 230, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 142, 230, 0.5);
  }
</style>
