<template>
  <div class="w-full h-full relative flex flex-col">
    <div class="flex-1 flex flex-col bg-[#1E2A47]/10 rounded mt-[0.15vw] p-[0.4vw] dashboard-component-shadow overflow-hidden">
      <!-- 标题 -->
      <div class="text-[0.6vw] dashboard-text-primary mb-[0.4vw] flex justify-between items-center">
        <span>环境温湿度</span>
        <span v-if="lastUpdateTime" class="text-[0.45vw] dashboard-text-secondary">{{ lastUpdateTime }}</span>
      </div>

      <!-- 内容区域（可滚动） -->
      <div class="flex-1 overflow-auto scrollbar-thin">
        <!-- 温度卡片组 -->
        <div v-if="temperatures.length > 0" class="mb-[0.4vw]">
          <div class="text-[0.5vw] dashboard-text-secondary mb-[0.3vw]">实时温度</div>
          <div class="grid grid-cols-2 gap-[0.3vw]">
            <div v-for="(item, index) in temperatures" :key="`temp-${index}`" class="flex flex-col bg-[#1E2A47]/20 rounded p-[0.3vw] status-card">
              <div class="flex justify-between items-center">
                <div class="text-[0.45vw] dashboard-text-secondary truncate">温度 #{{ index + 1 }}</div>
                <div class="flex items-center ml-[0.2vw]">
                  <div class="w-[0.4vw] h-[0.4vw] rounded-full" :class="getTemperatureStatusClass(item.dataValue)"></div>
                </div>
              </div>
              <div class="flex-1 flex flex-col items-center justify-center py-[0.1vw]">
                <div class="flex items-baseline">
                  <div class="text-[1vw] font-medium dashboard-text-primary text-shadow-bright">
                    <template v-if="loading.temperature">
                      <div class="pulse-loader"></div>
                    </template>
                    <template v-else>{{ item.dataValue.toFixed(1) }}</template>
                  </div>
                  <div class="text-[0.5vw] ml-[0.2vw] dashboard-text-secondary">{{ item.unit }}</div>
                </div>
                <div class="text-[0.4vw] mt-[0.2vw]" :class="getTemperatureTextClass(item.dataValue)">
                  {{ getTemperatureStatus(item.dataValue) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 湿度卡片组 -->
        <div v-if="humidities.length > 0">
          <div class="text-[0.5vw] dashboard-text-secondary mb-[0.3vw]">相对湿度</div>
          <div class="grid grid-cols-2 gap-[0.3vw]">
            <div v-for="(item, index) in humidities" :key="`humid-${index}`" class="flex flex-col bg-[#1E2A47]/20 rounded p-[0.3vw] status-card">
              <div class="flex justify-between items-center">
                <div class="text-[0.45vw] dashboard-text-secondary truncate">湿度 #{{ index + 1 }}</div>
                <div class="flex items-center ml-[0.2vw]">
                  <div class="w-[0.4vw] h-[0.4vw] rounded-full" :class="getHumidityStatusClass(item.dataValue)"></div>
                </div>
              </div>
              <div class="flex-1 flex flex-col items-center justify-center py-[0.1vw]">
                <div class="flex items-baseline">
                  <div class="text-[1vw] font-medium dashboard-text-primary text-shadow-bright">
                    <template v-if="loading.humidity">
                      <div class="pulse-loader"></div>
                    </template>
                    <template v-else>{{ item.dataValue.toFixed(1) }}</template>
                  </div>
                  <div class="text-[0.5vw] ml-[0.2vw] dashboard-text-secondary">{{ item.unit }}</div>
                </div>
                <div class="text-[0.4vw] mt-[0.2vw]" :class="getHumidityTextClass(item.dataValue)">
                  {{ getHumidityStatus(item.dataValue) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 无数据提示 -->
        <div v-if="temperatures.length === 0 && humidities.length === 0" class="h-full flex justify-center items-center">
          <div class="text-[0.6vw] dashboard-text-secondary">暂无温湿度数据</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
  import { getTemperature, getHumidity } from '/@/api/modbus/temperature';
  import { throttle } from 'lodash-es';

  // 定义状态 - 使用数组存储多个温湿度数据点
  const temperatures = ref<{ dataValue: number; unit: string }[]>([]);
  const humidities = ref<{ dataValue: number; unit: string }[]>([]);

  const loading = reactive({
    temperature: true,
    humidity: true,
  });

  const lastUpdateTime = ref('');
  let timer: number | null = null;

  // 获取温度数据
  const fetchTemperature = async () => {
    try {
      loading.temperature = true;
      const res = await getTemperature();

      if (Array.isArray(res) && res.length > 0) {
        temperatures.value = res.map((item) => ({
          dataValue: item.dataValue,
          unit: item.unit || '°C',
        }));
        updateLastUpdateTime();
      } else {
        temperatures.value = [];
      }
    } catch (error) {
      console.error('获取温度数据失败:', error);
      temperatures.value = [];
    } finally {
      loading.temperature = false;
    }
  };

  // 获取湿度数据
  const fetchHumidity = async () => {
    try {
      loading.humidity = true;
      const res = await getHumidity();

      if (Array.isArray(res) && res.length > 0) {
        humidities.value = res.map((item) => ({
          dataValue: item.dataValue,
          unit: item.unit || '%',
        }));
        updateLastUpdateTime();
      } else {
        humidities.value = [];
      }
    } catch (error) {
      console.error('获取湿度数据失败:', error);
      humidities.value = [];
    } finally {
      loading.humidity = false;
    }
  };

  // 更新时间
  const updateLastUpdateTime = () => {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    lastUpdateTime.value = `更新于 ${hours}:${minutes}:${seconds}`;
  };

  // 获取温度状态 - 接收温度值作为参数
  const getTemperatureStatus = (value: number) => {
    if (isNaN(value)) return '等待数据';
    if (value > 28) return '温度偏高';
    if (value < 18) return '温度偏低';
    return '温度正常';
  };

  // 获取湿度状态 - 接收湿度值作为参数
  const getHumidityStatus = (value: number) => {
    if (isNaN(value)) return '等待数据';
    if (value > 70) return '湿度偏高';
    if (value < 30) return '湿度偏低';
    return '湿度正常';
  };

  // 获取温度状态对应的颜色类 - 接收温度值作为参数
  const getTemperatureStatusClass = (value: number) => {
    if (isNaN(value)) return 'bg-gray-300';
    if (value > 28) return 'bg-red-300';
    if (value < 18) return 'bg-blue-300';
    return 'bg-green-300';
  };

  // 获取湿度状态对应的颜色类 - 接收湿度值作为参数
  const getHumidityStatusClass = (value: number) => {
    if (isNaN(value)) return 'bg-gray-300';
    if (value > 70) return 'bg-red-300';
    if (value < 30) return 'bg-yellow-300';
    return 'bg-green-300';
  };

  // 获取温度状态对应的文本颜色类 - 接收温度值作为参数
  const getTemperatureTextClass = (value: number) => {
    if (isNaN(value)) return 'dashboard-text-secondary';
    if (value > 28) return 'dashboard-text-danger';
    if (value < 18) return 'dashboard-text-accent';
    return 'dashboard-text-highlight';
  };

  // 获取湿度状态对应的文本颜色类 - 接收湿度值作为参数
  const getHumidityTextClass = (value: number) => {
    if (isNaN(value)) return 'dashboard-text-secondary';
    if (value > 70) return 'dashboard-text-danger';
    if (value < 30) return 'dashboard-text-warning';
    return 'dashboard-text-highlight';
  };

  // 窗口大小变化时优化性能
  const handleResize = throttle(() => {
    // 只处理窗口大小变化，不再有图表需要调整
  }, 300);

  // 生命周期钩子
  onMounted(() => {
    // 初始加载数据
    fetchTemperature();
    fetchHumidity();

    // 设置定时刷新
    timer = window.setInterval(() => {
      fetchTemperature();
      fetchHumidity();
    }, 30000); // 每30秒刷新一次

    window.addEventListener('resize', handleResize);
  });

  onBeforeUnmount(() => {
    // 清理定时器
    if (timer) {
      clearInterval(timer);
    }

    // 移除事件监听
    window.removeEventListener('resize', handleResize);
  });
</script>

<style scoped>
  .text-shadow-bright {
    text-shadow: 0 0 12px rgba(255, 255, 255, 0.7);
  }

  .status-card {
    transition:
      transform 0.3s ease,
      background-color 0.3s ease;
    min-height: 4.5vw;
  }

  .status-card:hover {
    transform: scale(1.02);
    background-color: rgba(30, 42, 71, 0.4);
  }

  .pulse-loader {
    width: 0.8vw;
    height: 0.8vw;
    border-radius: 50%;
    background-color: rgba(96, 165, 250, 0.6);
    animation: pulse 1.5s infinite ease-in-out;
  }

  @keyframes pulse {
    0% {
      transform: scale(0.8);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.6;
    }
    100% {
      transform: scale(0.8);
      opacity: 1;
    }
  }

  /* 自定义滚动条样式 */
  .scrollbar-thin::-webkit-scrollbar {
    width: 0.3vw;
    height: 0.3vw;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 0.3vw;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgba(96, 165, 250, 0.3);
    border-radius: 0.3vw;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgba(96, 165, 250, 0.5);
  }
</style>
