<template>
  <a-modal v-model:visible="visible" title="车辆出场" width="500px" @ok="handleSubmit" @cancel="handleCancel">
    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <a-form-item label="车牌号" name="vehicleNumber">
        <a-input v-model:value="formData.vehicleNumber" placeholder="请输入车牌号" style="text-transform: uppercase" />
      </a-form-item>

      <a-form-item label="停车费用">
        <a-input-number v-model:value="formData.fee" :min="0" :precision="2" placeholder="停车费用" style="width: 100%">
          <template #addonAfter>元</template>
        </a-input-number>
      </a-form-item>

      <a-form-item label="支付方式" name="paymentMethod">
        <a-select v-model:value="formData.paymentMethod" placeholder="选择支付方式">
          <a-select-option value="cash">现金</a-select-option>
          <a-select-option value="card">刷卡</a-select-option>
          <a-select-option value="mobile">手机支付</a-select-option>
          <a-select-option value="monthly">月卡</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { vehicleExit } from '/@/api/operations/parking';

  interface Props {
    visible: boolean;
  }

  const props = defineProps<Props>();

  const emit = defineEmits<{
    'update:visible': [value: boolean];
    success: [];
  }>();

  const formRef = ref();
  const formData = reactive({
    vehicleNumber: '',
    fee: 0,
    paymentMethod: '',
  });

  const rules = {
    vehicleNumber: [{ required: true, message: '请输入车牌号', trigger: 'blur' }],
    paymentMethod: [{ required: true, message: '请选择支付方式', trigger: 'change' }],
  };

  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const handleSubmit = async () => {
    try {
      await formRef.value.validate();
      await vehicleExit(formData.vehicleNumber);
      message.success('车辆出场成功');
      emit('success');
    } catch (error) {
      message.error('出场失败');
    }
  };

  const handleCancel = () => {
    visible.value = false;
  };
</script>
