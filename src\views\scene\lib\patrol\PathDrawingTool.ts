import * as THREE from 'three';
import { SceneManager } from '../SceneManager';
import { CameraController } from '../CameraController';
import { ControlManager } from '../control/ControlManager';
import { RenderingPipeline } from '../RenderingPipeline';
import { useGlobalThreeStore } from '../../store/globalThreeStore';
import { HighPerformanceTransparencyManager } from '../HighPerformanceTransparencyManager';
import { throttle } from 'lodash-es';
import { NavMeshPathFinder } from './NavMeshPathFinder';

/**
 * 路径点接口
 */
export interface PathPoint {
  position: THREE.Vector3;
  isDetour?: boolean; // 是否是自动生成的绕行点
}

/**
 * 绘制工具状态枚举
 */
export enum DrawingToolState {
  IDLE = 'idle', // 空闲状态
  DRAWING = 'drawing', // 绘制中
  COMPLETED = 'completed', // 绘制完成
}

/**
 * 绘制工具事件接口
 */
export interface DrawingToolEvents {
  onStart?: () => void;
  onPointAdded?: (point: PathPoint, index: number) => void;
  onComplete?: (points: PathPoint[]) => void;
  onCancel?: () => void;
}

/**
 * 路径绘制工具
 * 允许用户在3D场景中通过点击或拖拽方式绘制自定义路径
 */
export class PathDrawingTool {
  private static instance: PathDrawingTool | null = null;

  // 基础组件
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;
  private controlManager: ControlManager;
  private raycaster: THREE.Raycaster;
  private mouse: THREE.Vector2;

  // 绘制状态
  private state: DrawingToolState = DrawingToolState.IDLE;
  private pathPoints: PathPoint[] = [];
  private undoStack: PathPoint[][] = []; // 撤销栈
  private redoStack: PathPoint[][] = []; // 重做栈
  private isDragging: boolean = false;
  private lastPointTime: number = 0;
  private minTimeBetweenPoints: number = 100; // 毫秒

  // 可视化对象
  private pathLine: THREE.Line | null = null;
  private pointMarkers: THREE.Mesh[] = [];
  private previewLine: THREE.Line | null = null;
  private previewPoint: THREE.Vector3 | null = null;

  // 材质和几何体
  private lineMaterial: THREE.LineBasicMaterial;
  private pointGeometry: THREE.SphereGeometry;
  private pointMaterial: THREE.MeshBasicMaterial;

  // 事件处理
  private events: DrawingToolEvents = {};
  private boundMouseMove: (event: MouseEvent) => void;
  private boundMouseDown: (event: MouseEvent) => void;
  private boundMouseUp: (event: MouseEvent) => void;
  private boundKeyDown: (event: KeyboardEvent) => void;

  // 配置
  private config = {
    pointSize: 0.12, // 路径点大小（米）- 减小到原来的40%
    lineWidth: 2, // 路径线宽度
    pathColor: 0x3b82f6, // 路径颜色（蓝色）
    previewColor: 0x60a5fa, // 预览线颜色（浅蓝色）
    pointColor: 0x3b82f6, // 点标记颜色
    highlightColor: 0x4ade80, // 高亮颜色（绿色）
    invalidColor: 0xef4444, // 无效区域颜色（红色）
    minDistanceBetweenPoints: 0.5, // 最小点间距（米）
    maxRaycastDistance: 100, // 最大射线检测距离
  };

  // 高亮相关
  private highlightedObjects: THREE.Object3D[] = [];
  private originalMaterials: Map<THREE.Object3D, THREE.Material | THREE.Material[]> = new Map();
  private highlightMaterial: THREE.MeshBasicMaterial;
  private isOverValidSurface: boolean = false;
  private lastMessageTime: number = 0;
  private messageDebounceTime: number = 2000; // 消息防抖时间（毫秒）

  /**
   * 获取单例实例
   */
  public static getInstance(): PathDrawingTool {
    if (!PathDrawingTool.instance) {
      PathDrawingTool.instance = new PathDrawingTool();
    }
    return PathDrawingTool.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    // 获取必要组件
    this.scene = SceneManager.getInstance().scene;
    this.camera = CameraController.getInstance().camera as THREE.PerspectiveCamera;
    this.renderer = RenderingPipeline.getInstance().getRenderer();
    this.controlManager = ControlManager.getInstance();

    // 初始化射线检测器
    this.raycaster = new THREE.Raycaster();
    this.mouse = new THREE.Vector2();

    // 初始化材质
    this.lineMaterial = new THREE.LineBasicMaterial({
      color: this.config.pathColor,
      linewidth: this.config.lineWidth,
    });

    this.pointGeometry = new THREE.SphereGeometry(this.config.pointSize, 16, 16);
    this.pointMaterial = new THREE.MeshBasicMaterial({
      color: this.config.pointColor,
    });

    // 初始化高亮材质
    this.highlightMaterial = new THREE.MeshBasicMaterial({
      color: this.config.highlightColor,
      transparent: true,
      opacity: 0.3,
      depthWrite: false,
      side: THREE.DoubleSide,
    });

    // 绑定事件处理函数
    this.boundMouseMove = throttle(this._handleMouseMove.bind(this), 16);
    this.boundMouseDown = this._handleMouseDown.bind(this);
    this.boundMouseUp = this._handleMouseUp.bind(this);
    this.boundKeyDown = this._handleKeyDown.bind(this);
  }

  /**
   * 开始绘制路径
   * @param events 事件回调
   */
  public startDrawing(events?: DrawingToolEvents): void {
    // 如果已经在绘制中，先取消
    if (this.state !== DrawingToolState.IDLE) {
      this.cancelDrawing();
    }

    // 设置事件回调
    if (events) {
      this.events = events;
    }

    // 检查当前是否处于透明模式
    const globalThreeStore = useGlobalThreeStore();
    const isTransparencyActive = globalThreeStore.transparencyMode;

    // 如果当前处于透明模式，记录这个状态，以便在结束绘制时恢复
    if (isTransparencyActive) {
      console.log('[PathDrawingTool] 检测到透视模式已启用，记录透明状态');
      // 透明状态由全局状态管理，这里只需记录日志
    }

    // 禁用轨道控制器
    this.controlManager.disableControl();

    // 添加事件监听
    window.addEventListener('mousemove', this.boundMouseMove);
    window.addEventListener('mousedown', this.boundMouseDown);
    window.addEventListener('mouseup', this.boundMouseUp);
    window.addEventListener('keydown', this.boundKeyDown);

    // 更新状态
    this.state = DrawingToolState.DRAWING;
    this.pathPoints = [];

    // 创建路径线对象
    this._createPathLine();

    // 高亮可绘制区域
    this._highlightDrawableAreas();

    // 设置鼠标样式
    document.body.style.cursor = 'crosshair';

    // 显示提示消息
    this._showMessage('请在地板(Floor)区域上绘制路径点', 'info');

    // 触发开始事件
    if (this.events.onStart) {
      this.events.onStart();
    }

    console.log('[PathDrawingTool] 开始绘制路径');
  }

  /**
   * 完成绘制
   */
  public completeDrawing(): void {
    try {
      console.log('[PathDrawingTool] 执行completeDrawing方法');

      if (this.state !== DrawingToolState.DRAWING) {
        console.warn('[PathDrawingTool] 无法完成绘制：当前不在绘制状态');
        this._showMessage('无法完成绘制：当前不在绘制状态', 'warning');
        return;
      }

      if (this.pathPoints.length < 2) {
        console.warn('[PathDrawingTool] 无法完成绘制：路径点不足，当前点数:', this.pathPoints.length);
        this._showMessage('无法完成绘制：至少需要2个路径点', 'warning');
        return;
      }

      // 移除预览线
      this._removePreviewLine();

      // 更新状态
      this.state = DrawingToolState.COMPLETED;
      console.log('[PathDrawingTool] 状态已更新为COMPLETED');

      // 移除事件监听
      this._removeEventListeners();

      // 检查当前是否处于透明模式
      const globalThreeStore = useGlobalThreeStore();
      const isTransparencyActive = globalThreeStore.transparencyMode;

      // 取消高亮，并传入是否强制重新应用透明效果的参数
      this._removeHighlights(isTransparencyActive);

      // 恢复鼠标样式
      document.body.style.cursor = 'default';

      // 启用轨道控制器
      this.controlManager.enableControl();

      // 创建路径点的深拷贝，确保数据正确传递
      const pathPointsCopy = this.pathPoints.map((point) => ({
        position: point.position.clone(),
        isDetour: point.isDetour || false,
      }));

      console.log('[PathDrawingTool] 准备触发onComplete事件，路径点数量:', pathPointsCopy.length);

      // 触发完成事件
      if (this.events.onComplete) {
        this.events.onComplete(pathPointsCopy);
      } else {
        console.warn('[PathDrawingTool] 未设置onComplete回调');
      }

      console.log('[PathDrawingTool] 完成绘制路径，点数:', this.pathPoints.length);
    } catch (error) {
      console.error('[PathDrawingTool] 完成绘制时发生错误:', error);

      // 确保在发生错误时恢复控制器
      try {
        this.controlManager.enableControl();
      } catch (e) {
        console.error('[PathDrawingTool] 恢复控制器时发生错误:', e);
      }
    }
  }

  /**
   * 取消绘制
   * @param forceReapplyTransparency 是否强制重新应用透明效果
   */
  public cancelDrawing(forceReapplyTransparency: boolean = false): void {
    console.log('[PathDrawingTool] 开始取消绘制路径');

    // 检查当前是否处于透明模式
    const globalThreeStore = useGlobalThreeStore();
    const isTransparencyActive = globalThreeStore.transparencyMode;

    // 记录当前状态，用于日志
    console.log(
      `[PathDrawingTool] 取消绘制，当前状态: ${this.state}, 透视模式: ${isTransparencyActive ? '启用' : '禁用'}, 强制重新应用: ${forceReapplyTransparency}`
    );

    // 移除所有可视化对象
    this._removeAllVisualObjects();

    // 移除事件监听
    this._removeEventListeners();

    // 更新状态
    this.state = DrawingToolState.IDLE;
    this.pathPoints = [];

    // 取消高亮，并传入是否强制重新应用透明效果的参数
    this._removeHighlights(forceReapplyTransparency || isTransparencyActive);

    // 恢复鼠标样式
    document.body.style.cursor = 'default';

    // 启用轨道控制器
    this.controlManager.enableControl();

    // 触发取消事件
    if (this.events.onCancel) {
      this.events.onCancel();
    }

    console.log('[PathDrawingTool] 取消绘制路径完成');
  }

  /**
   * 获取当前路径点
   */
  public getPathPoints(): PathPoint[] {
    return [...this.pathPoints];
  }

  /**
   * 获取当前状态
   */
  public getState(): DrawingToolState {
    return this.state;
  }

  /**
   * 清除路径
   */
  public clearPath(): void {
    // 保存当前路径到撤销栈
    if (this.pathPoints.length > 0) {
      this.undoStack.push([...this.pathPoints]);
      this.redoStack = []; // 清空重做栈
    }

    this._removeAllVisualObjects();
    this.pathPoints = [];
    this._createPathLine();

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  /**
   * 隐藏路径可视化（路径线条和点位球体）
   * 在巡检开始时调用，完全隐藏路径线条和点位球体
   */
  public hidePathVisualization(): void {
    console.log('[PathDrawingTool] 隐藏路径可视化');

    // 隐藏路径线
    if (this.pathLine) {
      this.pathLine.visible = false;
    }

    // 隐藏点位球体
    this.pointMarkers.forEach((marker) => {
      marker.visible = false;
    });

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  /**
   * 显示路径可视化（路径线条和点位球体）
   * 在巡检结束或被用户手动停止时调用，恢复显示路径线条和点位球体
   */
  public showPathVisualization(): void {
    console.log('[PathDrawingTool] 显示路径可视化');

    // 显示路径线
    if (this.pathLine) {
      this.pathLine.visible = true;
    }

    // 显示点位球体
    this.pointMarkers.forEach((marker) => {
      marker.visible = true;
    });

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  /**
   * 撤销上一个点
   */
  public undoLastPoint(): void {
    if (this.state !== DrawingToolState.DRAWING || this.pathPoints.length === 0) return;

    // 保存当前路径到撤销栈
    this.undoStack.push([...this.pathPoints]);

    // 移除最后一个点
    if (this.pathPoints.length > 0) {
      // 移除点标记
      if (this.pointMarkers.length > 0) {
        const lastMarker = this.pointMarkers.pop();
        if (lastMarker) {
          this.scene.remove(lastMarker);
        }
      }

      // 移除路径点
      this.pathPoints.pop();

      // 更新路径线
      this._updatePathLine();

      // 更新预览线
      if (this.previewPoint && this.pathPoints.length > 0) {
        this._updatePreviewLine();
      }

      // 请求渲染
      SceneManager.getInstance().needsRender = true;
    }
  }

  /**
   * 重做点
   */
  public redoPoint(): void {
    if (this.state !== DrawingToolState.DRAWING || this.redoStack.length === 0) return;

    // 获取最后一个重做状态
    const redoState = this.redoStack.pop();
    if (redoState && redoState.length > 0) {
      // 保存当前状态到撤销栈
      this.undoStack.push([...this.pathPoints]);

      // 恢复重做状态
      this._removeAllVisualObjects();
      this.pathPoints = [...redoState];

      // 重新创建路径线和点标记
      this._createPathLine();
      this.pathPoints.forEach((point) => {
        this._addPointMarker(point.position, point.isDetour || false);
      });

      // 更新路径线
      this._updatePathLine();

      // 请求渲染
      SceneManager.getInstance().needsRender = true;
    }
  }

  /**
   * 检查是否可以撤销
   */
  public canUndo(): boolean {
    return this.pathPoints.length > 0;
  }

  /**
   * 检查是否可以重做
   */
  public canRedo(): boolean {
    return this.redoStack.length > 0;
  }

  /**
   * 移除所有事件监听
   */
  private _removeEventListeners(): void {
    window.removeEventListener('mousemove', this.boundMouseMove);
    window.removeEventListener('mousedown', this.boundMouseDown);
    window.removeEventListener('mouseup', this.boundMouseUp);
    window.removeEventListener('keydown', this.boundKeyDown);
  }

  /**
   * 移除所有可视化对象
   */
  private _removeAllVisualObjects(): void {
    // 移除路径线
    if (this.pathLine) {
      this.scene.remove(this.pathLine);
      this.pathLine = null;
    }

    // 移除预览线
    this._removePreviewLine();

    // 移除点标记
    this.pointMarkers.forEach((marker) => {
      this.scene.remove(marker);
    });
    this.pointMarkers = [];
  }

  /**
   * 移除预览线
   */
  private _removePreviewLine(): void {
    if (this.previewLine) {
      this.scene.remove(this.previewLine);
      this.previewLine = null;
    }
    this.previewPoint = null;
  }

  /**
   * 创建路径线对象
   */
  private _createPathLine(): void {
    // 移除现有路径线
    if (this.pathLine) {
      this.scene.remove(this.pathLine);
    }

    // 创建新的路径线
    const geometry = new THREE.BufferGeometry();
    this.pathLine = new THREE.Line(geometry, this.lineMaterial);
    this.scene.add(this.pathLine);
  }

  /**
   * 更新路径线
   */
  private _updatePathLine(): void {
    if (!this.pathLine) return;

    // 提取位置数组
    const positions = this.pathPoints.map((point) => point.position);

    // 更新几何体
    const geometry = new THREE.BufferGeometry().setFromPoints(positions);
    this.pathLine.geometry.dispose();
    this.pathLine.geometry = geometry;

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  // 缓存的预览线材质
  private validPreviewMaterial: THREE.LineBasicMaterial = new THREE.LineBasicMaterial({
    color: 0x60a5fa, // 蓝色表示正常路径
    linewidth: 1,
    transparent: true,
    opacity: 0.6,
  });

  private invalidPreviewMaterial: THREE.LineBasicMaterial = new THREE.LineBasicMaterial({
    color: 0xef4444, // 红色表示穿墙路径
    linewidth: 1,
    transparent: true,
    opacity: 0.6,
  });

  /**
   * 更新预览线
   */
  private _updatePreviewLine(): void {
    // 如果没有预览点或路径点为空，移除预览线
    if (!this.previewPoint || this.pathPoints.length === 0) {
      this._removePreviewLine();
      return;
    }

    // 获取最后一个路径点
    const lastPoint = this.pathPoints[this.pathPoints.length - 1].position;

    // 检查是否穿墙 - 使用NavMeshPathFinder
    const pathFinder = NavMeshPathFinder.getInstance();
    const hasWall = pathFinder.hasObstacleBetween(lastPoint, this.previewPoint);

    // 选择适当的材质（重用缓存的材质而不是每次创建新的）
    const lineMaterial = hasWall ? this.invalidPreviewMaterial : this.validPreviewMaterial;

    // 创建或更新预览线
    if (!this.previewLine) {
      const geometry = new THREE.BufferGeometry().setFromPoints([lastPoint, this.previewPoint]);
      this.previewLine = new THREE.Line(geometry, lineMaterial);
      this.scene.add(this.previewLine);
    } else {
      // 更新几何体
      const positions = [lastPoint, this.previewPoint];
      const geometry = new THREE.BufferGeometry().setFromPoints(positions);

      // 如果预览线已存在，只更新几何体和材质
      if (this.previewLine.geometry) {
        this.previewLine.geometry.dispose();
      }
      this.previewLine.geometry = geometry;

      // 只有在材质需要变化时才更新材质
      if (this.previewLine.material !== lineMaterial) {
        this.previewLine.material = lineMaterial;
      }
    }

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  // 绕行点材质缓存
  private detourPointMaterial: THREE.MeshBasicMaterial = new THREE.MeshBasicMaterial({
    color: 0x4ade80, // 绿色表示绕行点
  });

  /**
   * 添加点标记
   * @param position 点位置
   * @param isDetour 是否是绕行点
   */
  private _addPointMarker(position: THREE.Vector3, isDetour: boolean = false): void {
    // 选择材质（绕行点使用不同颜色）- 使用缓存的材质
    const material = isDetour ? this.detourPointMaterial : this.pointMaterial;

    // 创建点标记
    const marker = new THREE.Mesh(this.pointGeometry, material);
    marker.position.copy(position);
    this.scene.add(marker);
    this.pointMarkers.push(marker);

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  /**
   * 添加路径点
   * @param position 点位置
   */
  private _addPathPoint(position: THREE.Vector3): void {
    // 检查是否与上一个点距离太近
    if (this.pathPoints.length > 0) {
      const lastPoint = this.pathPoints[this.pathPoints.length - 1].position;
      const distance = position.distanceTo(lastPoint);

      if (distance < this.config.minDistanceBetweenPoints) {
        return;
      }
    }

    // 检查时间间隔
    const now = performance.now();
    if (now - this.lastPointTime < this.minTimeBetweenPoints) {
      return;
    }
    this.lastPointTime = now;

    // 保存当前路径状态到撤销栈
    this.undoStack.push([...this.pathPoints]);

    // 清空重做栈，因为有了新的操作
    this.redoStack = [];

    // 如果已有路径点，使用NavMeshPathFinder计算绕行路径
    if (this.pathPoints.length > 0) {
      const lastPoint = this.pathPoints[this.pathPoints.length - 1].position;
      const pathFinder = NavMeshPathFinder.getInstance();

      // 检查是否穿墙
      if (pathFinder.hasObstacleBetween(lastPoint, position)) {
        console.log('[PathDrawingTool] 检测到路径穿墙，计算绕行路径');

        // 计算绕行路径
        const detourPath = pathFinder.findPath(lastPoint, position);

        // 如果找到有效的绕行路径
        if (detourPath.length > 1) {
          // 至少有起点和终点
          // 添加绕行点（除了最后一个点，最后一个点是用户点击的目标点）
          for (let i = 1; i < detourPath.length - 1; i++) {
            // 跳过第一个点（起点）和最后一个点（终点）
            const detourPosition = detourPath[i];

            // 创建绕行点
            const detourPoint: PathPoint = {
              position: detourPosition.clone(),
              isDetour: true, // 标记为绕行点
            };

            // 添加到路径点数组
            this.pathPoints.push(detourPoint);

            // 添加点标记（使用不同颜色标识绕行点）
            this._addPointMarker(detourPoint.position, true);
          }

          // 显示成功消息
          this._showMessage('已自动计算绕行路径', 'success');
        } else {
          // 如果无法找到有效路径，显示警告
          this._showMessage('无法找到有效的绕行路径，请尝试其他位置', 'warning');
          return; // 不添加这个点
        }
      }
    }

    // 创建新的路径点
    const newPoint: PathPoint = {
      position: position.clone(),
      isDetour: false, // 标记为用户添加的点
    };

    // 添加到路径点数组
    this.pathPoints.push(newPoint);

    // 添加点标记
    this._addPointMarker(position);

    // 更新路径线
    this._updatePathLine();

    // 触发点添加事件
    if (this.events.onPointAdded) {
      this.events.onPointAdded(newPoint, this.pathPoints.length - 1);
    }
  }

  /**
   * 执行射线检测
   * @param event 鼠标事件
   * @returns 射线检测结果
   */
  private _performRaycast(event: MouseEvent): THREE.Vector3 | null {
    // 计算鼠标在归一化设备坐标中的位置
    const rect = this.renderer.domElement.getBoundingClientRect();
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    // 设置射线
    this.raycaster.setFromCamera(this.mouse, this.camera);

    // 获取场景中的地面对象（只有名称包含"Floor"的对象）
    const floorObjects = this._getFloorObjects();

    // 获取所有可能的表面（用于检测无效区域）
    const allSurfaces = this._getAllPossibleSurfaces();

    // 执行射线检测（所有表面）
    const allIntersects = this.raycaster.intersectObjects(allSurfaces, true);

    // 执行射线检测（只有地面）
    const floorIntersects = this.raycaster.intersectObjects(floorObjects, true);

    // 更新鼠标样式和高亮
    if (allIntersects.length > 0) {
      // 检查是否命中了地面
      const hitFloor = floorIntersects.length > 0;

      // 更新鼠标样式
      document.body.style.cursor = hitFloor ? 'crosshair' : 'not-allowed';

      // 更新状态
      this.isOverValidSurface = hitFloor;

      // 如果命中了非地面对象，显示提示
      if (!hitFloor && allIntersects[0].object) {
        const now = performance.now();
        if (now - this.lastMessageTime > this.messageDebounceTime) {
          this._showMessage('只能在地板(Floor)区域上绘制路径点', 'warning');
          this.lastMessageTime = now;
        }
      }
    }

    // 如果有地面交点，返回第一个交点位置
    if (floorIntersects.length > 0) {
      return floorIntersects[0].point;
    }

    // 如果没有地面交点，返回null（不再使用水平面交点）
    return null;
  }

  /**
   * 获取地面对象
   * @returns 地面对象数组
   */
  private _getFloorObjects(): THREE.Object3D[] {
    const floorObjects: THREE.Object3D[] = [];
    this.scene.traverse((object) => {
      // 只检查Mesh
      if (object.visible && object instanceof THREE.Mesh) {
        const name = object.name.toLowerCase();
        if (name.includes('floor')) {
          // 保证变换和包围盒已更新
          object.updateMatrixWorld(true);
          if (object.geometry) {
            object.geometry.computeBoundingBox();
            object.geometry.computeBoundingSphere();
          }
          // 检查材质可见
          if (
            // @ts-ignore
            !object.material ||
            object.material.visible !== false
          ) {
            floorObjects.push(object);
            // 调试日志
            // console.log('[PathDrawingTool] floor mesh:', object.name, object);
          }
        }
      }
    });
    if (floorObjects.length === 0) {
      console.warn('[PathDrawingTool] 未找到名称中包含 "floor" (不区分大小写) 的地面对象');
    } else {
      console.log('[PathDrawingTool] 总共找到地板对象数量:', floorObjects.length);
    }
    return floorObjects;
  }

  /**
   * 获取所有可能的绘制表面（用于高亮显示）
   * @returns 所有可能的绘制表面
   */
  private _getAllPossibleSurfaces(): THREE.Object3D[] {
    const surfaces: THREE.Object3D[] = [];

    this.scene.traverse((object) => {
      if (object.visible && object instanceof THREE.Mesh) {
        surfaces.push(object);
      }
    });

    return surfaces;
  }

  /**
   * 处理鼠标移动事件
   * @param event 鼠标事件
   */
  private _handleMouseMove = throttle((event: MouseEvent): void => {
    if (this.state !== DrawingToolState.DRAWING) return;

    // 检查是否在UI元素上移动
    if (this._isOverUIElement(event)) {
      // 如果在UI元素上移动，移除预览线并更改鼠标样式
      this._removePreviewLine();
      document.body.style.cursor = 'default';
      return;
    }

    // 执行射线检测
    const hitPoint = this._performRaycast(event);

    if (hitPoint) {
      // 更新预览点
      this.previewPoint = hitPoint;

      // 检查是否穿墙并更新预览线
      if (this.pathPoints.length > 0) {
        // 获取最后一个路径点
        const lastPoint = this.pathPoints[this.pathPoints.length - 1].position;

        // 检查是否穿墙 - 使用NavMeshPathFinder
        const pathFinder = NavMeshPathFinder.getInstance();
        const hasWall = pathFinder.hasObstacleBetween(lastPoint, hitPoint);

        // 更新鼠标样式以提供视觉反馈
        document.body.style.cursor = hasWall ? 'not-allowed' : 'crosshair';

        // 如果穿墙且鼠标移动时间间隔足够长，显示提示（降低提示频率）
        if (hasWall) {
          const now = performance.now();
          if (now - this.lastMessageTime > this.messageDebounceTime * 2) {
            // 增加消息间隔
            this._showMessage('路径会穿墙，系统将自动计算绕行路径', 'warning');
            this.lastMessageTime = now;
          }
        }

        // 更新预览线
        this._updatePreviewLine();
      }

      // 如果正在拖拽，添加路径点（使用现有的时间间隔控制）
      if (this.isDragging) {
        this._addPathPoint(hitPoint);
      }
    } else {
      // 如果没有命中点，移除预览线
      this._removePreviewLine();
    }
  }, 100); // 添加100毫秒的节流，减少计算频率

  /**
   * 检查鼠标是否在UI元素上
   * @param event 鼠标事件
   * @returns 是否在UI元素上
   */
  private _isOverUIElement(event: MouseEvent): boolean {
    // 检查事件目标是否是UI元素
    const target = event.target as HTMLElement;

    // 检查目标元素是否属于工具栏
    if (
      target &&
      (target.closest('.patrol-toolbar') ||
        target.closest('.toolbar-btn') ||
        target.closest('.ant-tooltip') ||
        target.classList.contains('toolbar-btn') ||
        target.classList.contains('pointer-events-auto'))
    ) {
      return true;
    }

    // 检查目标元素的data属性
    if (target && target.dataset && target.dataset.viewControl) {
      return true;
    }

    return false;
  }

  /**
   * 处理鼠标按下事件
   * @param event 鼠标事件
   */
  private _handleMouseDown(event: MouseEvent): void {
    if (this.state !== DrawingToolState.DRAWING || event.button !== 0) return;

    // 检查是否点击了UI元素
    if (this._isClickingUIElement(event)) {
      console.log('[PathDrawingTool] 检测到点击UI元素，忽略此次点击');
      return;
    }

    // 执行射线检测
    const hitPoint = this._performRaycast(event);

    if (hitPoint) {
      // 检查是否穿墙（如果有前一个点）
      if (this.pathPoints.length > 0) {
        const lastPoint = this.pathPoints[this.pathPoints.length - 1].position;
        const pathFinder = NavMeshPathFinder.getInstance();

        // 如果会穿墙，显示提示但仍然添加点（系统会自动计算绕行路径）
        if (pathFinder.hasObstacleBetween(lastPoint, hitPoint)) {
          this._showMessage('检测到路径会穿墙，系统将自动计算绕行路径', 'info');
        }
      }

      // 添加路径点（内部会处理绕行逻辑）
      this._addPathPoint(hitPoint);

      // 开始拖拽
      this.isDragging = true;
    } else if (!this.isOverValidSurface) {
      // 如果点击了非法区域，显示提示
      this._showMessage('只能在地板(Floor)区域上绘制路径点', 'warning');
    }
  }

  /**
   * 检查是否点击了UI元素
   * @param event 鼠标事件
   * @returns 是否点击了UI元素
   */
  private _isClickingUIElement(event: MouseEvent): boolean {
    // 检查事件目标是否是UI元素
    const target = event.target as HTMLElement;

    // 检查是否有阻止默认行为或阻止冒泡的标志
    if (event.defaultPrevented) {
      return true;
    }

    // 检查目标元素是否属于工具栏
    if (
      target &&
      (target.closest('.patrol-toolbar') ||
        target.closest('.toolbar-btn') ||
        target.closest('.ant-tooltip') ||
        target.classList.contains('toolbar-btn') ||
        target.classList.contains('pointer-events-auto'))
    ) {
      return true;
    }

    // 检查目标元素的data属性
    if (target && target.dataset && target.dataset.viewControl) {
      return true;
    }

    return false;
  }

  /**
   * 处理鼠标释放事件
   * @param event 鼠标事件
   */
  private _handleMouseUp(event: MouseEvent): void {
    if (this.state !== DrawingToolState.DRAWING || event.button !== 0) return;

    // 结束拖拽
    this.isDragging = false;
  }

  /**
   * 处理键盘事件
   * @param event 键盘事件
   */
  private _handleKeyDown(event: KeyboardEvent): void {
    if (this.state !== DrawingToolState.DRAWING) {
      console.log('[PathDrawingTool] 忽略键盘事件：当前不在绘制状态');
      return;
    }

    console.log('[PathDrawingTool] 处理键盘事件:', event.key);

    // 只保留ESC键取消绘制功能
    if (event.key === 'Escape') {
      console.log('[PathDrawingTool] 检测到ESC键，取消绘制');
      this.cancelDrawing();
    }

    // 移除Enter键完成绘制的功能，改为使用UI按钮
  }

  /**
   * 高亮可绘制区域
   */
  private _highlightDrawableAreas(): void {
    // 检查当前是否处于透明模式
    const globalThreeStore = useGlobalThreeStore();
    const isTransparencyActive = globalThreeStore.transparencyMode;

    // 如果当前处于透明模式，先保存透明状态
    if (isTransparencyActive) {
      console.log('[PathDrawingTool] 检测到透视模式已启用，保存透明状态');
      // 我们不需要做特殊处理，因为透明状态是由全局状态管理的
      // 只需确保在移除高亮时正确恢复透明效果
    }

    // 移除现有高亮
    this._removeHighlights();

    // 获取地面对象
    const floorObjects = this._getFloorObjects();

    // 高亮地面对象
    floorObjects.forEach((object) => {
      if (object instanceof THREE.Mesh) {
        // 保存原始材质
        this.originalMaterials.set(object, object.material);

        // 应用高亮材质
        object.material = this.highlightMaterial;

        // 添加到高亮对象列表
        this.highlightedObjects.push(object);
      }
    });

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  /**
   * 移除高亮
   * @param forceReapplyTransparency 是否强制重新应用透明效果，即使当前不处于透明模式
   */
  private _removeHighlights(forceReapplyTransparency: boolean = false): void {
    // 检查当前是否处于透明模式
    const globalThreeStore = useGlobalThreeStore();
    const isTransparencyActive = globalThreeStore.transparencyMode;

    console.log(
      `[PathDrawingTool] 移除高亮效果，当前透视模式状态: ${isTransparencyActive ? '启用' : '禁用'}, 强制重新应用: ${forceReapplyTransparency}`
    );

    // 如果没有高亮对象，直接返回
    if (this.highlightedObjects.length === 0 && this.originalMaterials.size === 0) {
      console.log('[PathDrawingTool] 没有高亮对象需要清除');

      // 如果强制重新应用透明效果，则执行
      if (forceReapplyTransparency || isTransparencyActive) {
        this._reapplyTransparencyEffect(isTransparencyActive);
      }
      return;
    }

    // 恢复原始材质
    this.highlightedObjects.forEach((object) => {
      if (object instanceof THREE.Mesh) {
        const originalMaterial = this.originalMaterials.get(object);
        if (originalMaterial) {
          object.material = originalMaterial;
          console.log(`[PathDrawingTool] 恢复对象 ${object.name || '未命名'} 的原始材质`);
        } else {
          console.warn(`[PathDrawingTool] 未找到对象 ${object.name || '未命名'} 的原始材质`);
        }
      }
    });

    // 清空高亮对象列表和原始材质映射
    const highlightedCount = this.highlightedObjects.length;
    this.highlightedObjects = [];
    this.originalMaterials.clear();
    console.log(`[PathDrawingTool] 已清空 ${highlightedCount} 个高亮对象`);

    // 如果当前处于透明模式或强制重新应用，重新应用透明效果
    if (isTransparencyActive || forceReapplyTransparency) {
      this._reapplyTransparencyEffect(isTransparencyActive);
    }

    // 请求渲染
    SceneManager.getInstance().needsRender = true;
  }

  /**
   * 重新应用透明效果
   * @param isTransparencyActive 当前是否处于透明模式
   */
  private _reapplyTransparencyEffect(isTransparencyActive: boolean): void {
    if (isTransparencyActive) {
      console.log('[PathDrawingTool] 检测到透视模式已启用，重新应用透视效果');
      try {
        // 使用setTimeout确保在下一帧应用透明效果，避免与当前操作冲突
        setTimeout(() => {
          try {
            const transparencyManager = HighPerformanceTransparencyManager.getInstance();
            transparencyManager.toggleTransparency(true);
            console.log('[PathDrawingTool] 透视效果重新应用成功');
          } catch (innerError) {
            console.error('[PathDrawingTool] 在setTimeout中重新应用透视效果失败:', innerError);
          }
        }, 50); // 增加延迟时间，确保有足够时间处理
      } catch (error) {
        console.error('[PathDrawingTool] 重新应用透视效果失败:', error);
      }
    } else {
      console.log('[PathDrawingTool] 当前不处于透视模式，确保不应用透视效果');

      // 确保全局状态正确
      try {
        const globalThreeStore = useGlobalThreeStore();
        if (globalThreeStore.transparencyMode) {
          console.log('[PathDrawingTool] 修正全局透明模式状态为禁用');
          globalThreeStore.setTransparencyMode(false);
        }
      } catch (error) {
        console.error('[PathDrawingTool] 修正全局透明模式状态失败:', error);
      }
    }
  }

  /**
   * 显示消息
   * @param content 消息内容
   * @param _type 消息类型（未使用）
   */
  private _showMessage(content: string, _type: 'success' | 'info' | 'warning' | 'error'): void {
    // Message notifications disabled for clean interface during patrol
    console.log(`[PathDrawingTool] ${content}`);
  }
}
