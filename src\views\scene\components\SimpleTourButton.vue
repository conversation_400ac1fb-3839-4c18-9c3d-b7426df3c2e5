<template>
  <div>
    <!-- 巡视按钮 -->
    <a-tooltip placement="right" :title="isTouring ? '停止巡视' : '自动巡视'">
      <div
        class="w-[2.2vw] h-[2.2vw] bg-[rgba(23,43,77,0.8)] border border-[rgba(36,108,249,0.3)] rounded-[0.3vw] flex flex-col items-center justify-center cursor-pointer text-white transition-all duration-300 relative overflow-hidden hover:(bg-[rgba(36,108,249,0.2)] border-[rgba(36,108,249,0.5)] scale-105) before:content-empty before:absolute before:left-[-100%] before:top-[-50%] before:w-[200%] before:h-[200%] before:bg-gradient-to-r before:from-transparent before:via-[rgba(36,108,249,0.3)] before:to-transparent before:rotate-45 hover:before:animate-scan"
        :class="{ 'bg-[rgba(36,108,249,0.3)] border-[rgba(36,108,249,0.8)] shadow-[0_0_10px_rgba(36,108,249,0.3)]': isTouring }"
        @click="toggleTour"
      >
        <component :is="isTouring ? PauseCircleOutlined : PlayCircleOutlined" class="text-[0.8vw] mb-[0.1vw]" />
        <span class="text-[0.5vw] text-white/80">{{ isTouring ? '停止' : '巡视' }}</span>
      </div>
    </a-tooltip>

    <!-- 巡视提示 -->
    <div
      v-if="isTouring"
      class="fixed bottom-[2vw] left-1/2 -translate-x-1/2 bg-black/70 text-white px-[1vw] py-[0.5vw] rounded-full text-[0.8vw] z-50 flex items-center"
    >
      <component :is="PlayCircleOutlined" class="text-[0.8vw] mr-[0.5vw] text-blue-400" />
      <span
        >正在自动巡视中，按下<span class="mx-[0.3vw] px-[0.3vw] bg-white/20 rounded">ESC</span>或点击<span
          class="mx-[0.3vw] px-[0.3vw] bg-white/20 rounded"
          >停止</span
        >按钮退出</span
      >
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onBeforeUnmount } from 'vue';
  import { PlayCircleOutlined, PauseCircleOutlined } from '@ant-design/icons-vue';
  import { CameraController } from '../lib/CameraController';
  import { ModelLoaderManager } from '../lib/load/ModelLoaderManager';
  import { useGlobalThreeStore } from '../store/globalThreeStore';
  import { isFloorDevice, extractFloorNumber } from '../utils/deviceIdentifier';
  import { ControlManager } from '../lib/control/ControlManager';
  import { SceneManager } from '../lib/SceneManager';
  import * as THREE from 'three';

  // 状态定义
  const isTouring = ref(false);
  const globalThreeStore = useGlobalThreeStore();

  // 获取消息组件
  const techMessage = ref(null);

  // 显示消息的安全方法
  const showMessage = (text: string, type: string = 'info', options = {}) => {
    try {
      if (window.$message) {
        window.$message[type]?.(text);
      }
    } catch (error) {
      console.log('显示消息失败:', error);
    }
  };

  // 巡视状态
  let tourDevices: THREE.Object3D[] = [];
  let currentDeviceIndex = 0;
  let tourInterval: number | null = null;
  const deviceStayTime = 3000; // 每个设备停留时间(毫秒)

  // 保存原始状态
  let wasTransparencyEnabled = false;
  let controlManager: ControlManager | null = null;

  // 监听ESC键
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape' && isTouring.value) {
      stopTour();
    }
  };

  // 切换巡视状态
  const toggleTour = () => {
    if (isTouring.value) {
      stopTour();
    } else {
      startTour();
    }
  };

  // 开始巡视
  const startTour = () => {
    // 获取当前楼层的设备
    collectDevices();

    if (tourDevices.length === 0) {
      window.$message?.warning('未找到可巡视的设备');
      return;
    }

    // 保存当前透视状态
    controlManager = ControlManager.getInstance();
    const viewControl = document.querySelector('[data-view-control="transparency"]');
    wasTransparencyEnabled = viewControl?.classList.contains('active') || false;

    // 启用透视功能
    if (!wasTransparencyEnabled) {
      // 触发透视功能
      const event = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window,
      });
      viewControl?.dispatchEvent(event);

      // 显示提示
      showMessage('已自动启用透视功能，以便更清晰地查看设备', 'info');
    }

    // 禁用摄像机控制
    if (controlManager) {
      controlManager.lockControl();
    }

    isTouring.value = true;
    currentDeviceIndex = 0;

    // 添加键盘事件监听
    window.addEventListener('keydown', handleKeyDown);

    // 开始巡视第一个设备
    moveToCurrentDevice();

    // 设置定时器，定期移动到下一个设备
    tourInterval = window.setInterval(() => {
      moveToNextDevice();
    }, deviceStayTime);
  };

  // 停止巡视
  const stopTour = () => {
    if (tourInterval) {
      clearInterval(tourInterval);
      tourInterval = null;
    }

    // 移除键盘事件监听
    window.removeEventListener('keydown', handleKeyDown);

    // 恢复摄像机控制
    if (controlManager) {
      controlManager.unlockControl();
    }

    // 如果原来没有启用透视，则关闭透视
    if (!wasTransparencyEnabled) {
      // 获取透视按钮并关闭透视
      const viewControl = document.querySelector('[data-view-control="transparency"]');
      const isCurrentlyActive = viewControl?.classList.contains('active') || false;

      if (isCurrentlyActive) {
        // 触发透视功能关闭
        const event = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window,
        });
        viewControl?.dispatchEvent(event);
      }
    }

    isTouring.value = false;
  };

  // 收集当前楼层的设备
  const collectDevices = () => {
    const modelLoader = ModelLoaderManager.getInstance();
    const currentFloorId = globalThreeStore.currentFloorId;
    tourDevices = [];

    // 获取所有模型
    const allModels = modelLoader.getCurrentModels();

    // 创建一个Map来存储已处理的设备，避免重复
    const processedDevices = new Map<string, boolean>();

    // 遍历所有模型，查找符合条件的设备
    allModels.forEach((model) => {
      model.traverse((object) => {
        // 跳过已处理的对象
        if (processedDevices.has(object.uuid)) return;
        processedDevices.set(object.uuid, true);

        // 检查是否是楼层设备
        if (object.name && isFloorDevice(object.name)) {
          // 提取楼层号
          const deviceFloorNumber = extractFloorNumber(object.name);

          if (!deviceFloorNumber) return;

          // 检查当前楼层
          const currentFloorMatch = currentFloorId?.match(/floor-1-(\d+)/);
          const currentFloorNumber = currentFloorMatch ? currentFloorMatch[1] : null;

          if (currentFloorNumber && deviceFloorNumber === currentFloorNumber) {
            // 只添加当前楼层的设备
            tourDevices.push(object);
          }
        }
      });
    });

    console.log(`找到${tourDevices.length}个设备进行巡视`);

    // 按名称排序
    tourDevices.sort((a, b) => a.name.localeCompare(b.name));
  };

  // 移动到当前设备
  const moveToCurrentDevice = () => {
    if (tourDevices.length === 0 || currentDeviceIndex >= tourDevices.length) return;

    const device = tourDevices[currentDeviceIndex];
    const cameraController = CameraController.getInstance();

    // 获取设备位置和尺寸
    const devicePosition = new THREE.Vector3();
    device.getWorldPosition(devicePosition);

    // 计算设备的包围盒
    const boundingBox = new THREE.Box3().setFromObject(device);
    const deviceSize = new THREE.Vector3();
    boundingBox.getSize(deviceSize);

    // 确定设备的前方向
    // 尝试从设备名称或结构推断前方向
    let frontDirection = new THREE.Vector3(0, 0, 1); // 默认Z轴正方向为前方

    // 如果设备有旋转，考虑其旋转
    if (device.rotation) {
      frontDirection.applyEuler(device.rotation);
    }

    // 计算相机位置 (在设备前方适当距离)
    const distance = Math.max(deviceSize.x, deviceSize.z) * 2.5; // 根据设备大小调整距离
    const cameraPosition = devicePosition.clone();
    cameraPosition.y += deviceSize.y * 0.5; // 对准设备中心高度

    // 在设备前方放置相机
    cameraPosition.x -= frontDirection.x * distance;
    cameraPosition.z -= frontDirection.z * distance;

    // 确保相机位置不会太低
    cameraPosition.y = Math.max(cameraPosition.y, 1.6); // 至少在1.6米高度

    // 移动相机
    cameraController.moveToPosition(
      { x: cameraPosition.x, y: cameraPosition.y, z: cameraPosition.z },
      { x: devicePosition.x, y: devicePosition.y, z: devicePosition.z },
      1000
    );

    // 在控制台输出当前巡视的设备名称（仅用于调试）
    console.log('当前巡视设备:', device.name, `(${currentDeviceIndex + 1}/${tourDevices.length})`);

    // 显示当前巡视设备信息
    showMessage(`正在巡视: ${device.name}`, 'info');
  };

  // 移动到下一个设备
  const moveToNextDevice = () => {
    currentDeviceIndex = (currentDeviceIndex + 1) % tourDevices.length;
    moveToCurrentDevice();
  };

  // 组件卸载时清理
  onBeforeUnmount(() => {
    stopTour();
    window.removeEventListener('keydown', handleKeyDown);
  });
</script>
